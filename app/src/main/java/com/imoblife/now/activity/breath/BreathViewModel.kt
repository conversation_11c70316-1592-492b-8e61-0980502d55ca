package com.imoblife.now.activity.breath

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.BreathShareEntity
import com.imoblife.now.bean.BreathStaticsEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.util.SpUtil

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022-8-5
 * 描   述：BreathViewModel
 */
class BreathViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { BreathRepository() }

    // 呼吸模式 - 分享数据
    private val _shareData = MutableLiveData<UiStatus<BreathShareEntity>>()
    val shareData: LiveData<UiStatus<BreathShareEntity>> = _shareData

    // 呼吸「历史」
    private val _breathStatics = MutableLiveData<UiStatus<BreathStaticsEntity>>()
    val breathStatics: LiveData<UiStatus<BreathStaticsEntity>> = _breathStatics

    /**
     * 呼吸「历史」
     */
    fun getBreathStatics() {
        mRepository.getBreathStatics(_breathStatics)
    }

    /**
     * 呼吸模式 - 分享数据
     */
    fun getBreathShareData(onlyId: String) {
        val schemaId = SpUtil.getInstance().getIntValue(
            "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_MODE_SELECT_STATUS}",
            1
        )
        mRepository.getBreathShareData(schemaId, onlyId, _shareData)
    }

}