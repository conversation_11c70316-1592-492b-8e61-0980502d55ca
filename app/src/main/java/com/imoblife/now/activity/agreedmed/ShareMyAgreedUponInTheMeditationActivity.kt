package com.imoblife.now.activity.agreedmed

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.view.View
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.R
import com.imoblife.now.bean.MeditationClassShareEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutAcAgreedUponInTheMeditationShareBinding
import com.imoblife.now.player.PermissionsCheck
import com.imoblife.now.util.*

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-21
 * 描   述：分享我的约定冥想
 */
class ShareMyAgreedUponInTheMeditationActivity : BaseVMActivity<BaseViewModel<Any?>>() {

    companion object {
        fun startActivity(
            context: Context,
            meditationClassShareEntity: MeditationClassShareEntity?,
        ) {
            Intent(context, ShareMyAgreedUponInTheMeditationActivity::class.java).run {
                if (EmptyUtils.isNotEmpty(meditationClassShareEntity)) putExtra(
                    ConsCommon.SHARE_MY_AGREED_UPON_IN_THE_MEDITATION,
                    meditationClassShareEntity
                )
                context.startActivity(this)
            }
        }
    }

    private lateinit var mBind: LayoutAcAgreedUponInTheMeditationShareBinding

    private var mMedClassShare: MeditationClassShareEntity? = null

    override fun superInit(intent: Intent?) {
        if (hasExtra(ConsCommon.SHARE_MY_AGREED_UPON_IN_THE_MEDITATION)) {
            mMedClassShare =
                intent?.getParcelableExtra(ConsCommon.SHARE_MY_AGREED_UPON_IN_THE_MEDITATION)
        }
    }

    override fun getLayoutResId() = R.layout.layout_ac_agreed_upon_in_the_meditation_share

    override fun initVM() = BaseViewModel<Any?>()

    override fun initImmersionBar() {
        ImmersionBar.with(this)
            .statusBarColor(R.color.color_7E7E7E)
            .statusBarDarkFont(true)
            .fitsSystemWindows(true)
            .init()
    }

    override fun initView() {
        mBind = mBinding as LayoutAcAgreedUponInTheMeditationShareBinding
        mBind.apply {
            entity = mMedClassShare
            clickProxy = ClickProxy()
            executePendingBindings()
        }
    }

    override fun initData() {}

    override fun startObserve() {}

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                R.id.stvSaveToAlbum -> {
                    saveImage()
                }
            }
        }

    }

    /**
     * 保存图片到相册
     */
    private fun saveImage() {
        PermissionsCheck.checkHasStoragePermission { isPermission: Boolean ->
            if (isPermission) {
                Handler().post {
                    mBind.clContainer.let { view ->
                        BitmapUtil.layoutView(view, view.width, view.height)
                        val bitmap = BitmapUtil.loadBitmapFromView(view)
                        bitmap?.let {
                            FileUtils.saveImageToGallery(
                                this@ShareMyAgreedUponInTheMeditationActivity,
                                it
                            )
                            finish()
                        }
                    }
                }
            }
        }
    }

}
