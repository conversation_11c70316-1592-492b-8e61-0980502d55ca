package com.imoblife.now.activity.member
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager.widget.ViewPager
import com.imoblife.now.R
import com.imoblife.now.adapter.BasePageAdapter
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.databinding.ActivityOrderBinding
import com.imoblife.now.fragment.OrderCourseFragment
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel

class OrderActivity : BaseVMActivity<NoViewModel>() {

    private lateinit var mBind:ActivityOrderBinding
    private lateinit var pagerAdapter : BasePageAdapter<String>
    private val title = listOf("冥想", "拓展进阶","行动营")
    override fun getLayoutResId()= R.layout.activity_order
    override fun superInit(intent: Intent?) {}
    override fun initVM()= ViewModelProvider(this,ViewModelProvider.NewInstanceFactory()).get(NoViewModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityOrderBinding
        ToolbarUtils.setToolbar(this, "已购课程", NavIconType.BACK, true)
    }

    override fun initData() {
        mBind.tabLayout.setSnapOnTabClick(true)
        mBind.viewpager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}
            override fun onPageSelected(position: Int) {
                for (i in 0 until pagerAdapter.count) {
                    val isSelect = i == position
                    mBind.tabLayout.getTitleView(i).textSize = if (isSelect) 15f else 14f
                }
            }
            override fun onPageScrollStateChanged(state: Int) {}
        })
        pagerAdapter = object : BasePageAdapter<String>(this.supportFragmentManager) {
            override fun getFragment(position: Int, data: String?): Fragment? {
                return OrderCourseFragment.getInstance(position)
            }
            override fun getTitle(data: String?): String? {
                return data
            }
        }
        pagerAdapter.setNewData(title)
        mBind.viewpager.adapter = pagerAdapter
        mBind.viewpager.offscreenPageLimit=title.size
        mBind.tabLayout.setViewPager(mBind.viewpager)
        mBind.tabLayout?.getTitleView(0)?.textSize=15f
    }
    override fun startObserve() {}

}