package com.imoblife.now.activity.monitor

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutAcExitSleepAnalysisBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/26
 * 描   述：退出睡眠分析
 */
class ExitSleepAnalysisActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, ExitSleepAnalysisActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcExitSleepAnalysisBinding

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .statusBarColor(R.color.color_212121)
            .statusBarDarkFont(false)
            .fitsSystemWindows(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_exit_sleep_analysis

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcExitSleepAnalysisBinding
        mBind.clickProxy = ClickProxy()
    }

    override fun initData() {
    }

    override fun startObserve() {}

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 好的
                R.id.stvBtn -> {
                    // todo: 跳转 开启睡眠监测 页面
                    finish()
                }
                // 立即退出
                R.id.tvExitNow -> finish()
                else -> {}
            }
        }

    }

}