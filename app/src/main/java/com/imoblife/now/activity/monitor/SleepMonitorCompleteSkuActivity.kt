package com.imoblife.now.activity.monitor

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.activity.main.MainActivity
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcSleepMonitorCompleteSkuBinding
import com.imoblife.now.ext.configSleepStateChart
import com.imoblife.now.ext.notShowNoDataText
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.TimeUtils
import com.jaychang.st.SimpleText

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/5/9
 * 描   述：首次睡眠监测 - 完成 - sku
 */
class SleepMonitorCompleteSkuActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            Intent(context, SleepMonitorCompleteSkuActivity::class.java).let {
                context.startActivity(it)
            }
        }

    }

    private lateinit var mBind: LayoutAcSleepMonitorCompleteSkuBinding

    override fun getLayoutResId() = R.layout.layout_ac_sleep_monitor_complete_sku

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentBar()
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun onResume() {
        super.onResume()
        mBind.tvTitle.text = TimeUtils.getBreathTimeFrame()
    }

    override fun initView() {
        mBind = mBinding as LayoutAcSleepMonitorCompleteSkuBinding
        mBind.apply {
            imgClose.onDebounceClickListener {
                SensorsDataEvent.firstDayReportPageClick("关闭")
                onBackPressed()
            }
            tvTitle.text = TimeUtils.getBreathTimeFrame()
            tvSleepAnalysisFrequency.text = SimpleText
                .from("这是你在熊猫睡眠的第 1 次睡眠分析")
                .first("1")
                .textColor(R.color.color_F5D321)
                .size(20)
        }
    }

    override fun initData() {
        SensorsDataEvent.firstDayReportPageShow()
        mViewModel.getSleepMonitorCompleteSku()
    }

    @SuppressLint("SetTextI18n")
    override fun startObserve() {
        mViewModel.sleepMonitorCompleteSku.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    mBind.apply {
                        // 睡眠情况
                        entity.sleep_status?.let { sleepStatus ->
                            if (sleepStatus.data.isNullOrEmpty()) {
                                barChart.notShowNoDataText()
                            } else {
                                barChart.configSleepStateChart(sleepStatus)
                            }
                            tvTime.text = sleepStatus.date
                            tvSleepDuration.text = SimpleText
                                .from("你刚刚睡眠了${sleepStatus.duration}")
                                .first(sleepStatus.duration)
                                .textColor(R.color.color_F5D321)
                            sleepStatus.asleepData?.let { sleepData ->
                                tvDayTime.text = "${sleepData.asleep}-${sleepData.wake}"
                                tvFallASleep.text = getString(
                                    R.string.string_a_sleep_txt, sleepData.asleep
                                )
                                tvWake.text =
                                    getString(R.string.string_wake_txt, sleepData.wake)
                                // 深睡｜浅睡｜清醒｜入睡
                                srssvDeepSleep.setData(sleepData.deep_sleep_prop)
                                srssvLightSleep.setData(sleepData.light_sleep_prop)
                                srssvSober.setData(sleepData.sober_sleep_prop)
                                srssvFallASleep.setData(sleepData.asleep_prop)
                            }
                        }
                        // imgSku
                        if (EmptyUtils.isNotEmpty(entity.sku_list)) {
                            ImageLoader.loadImageUrl(
                                this@SleepMonitorCompleteSkuActivity,
                                entity.sku_list.background_checked,
                                imgSku
                            )
                            privacyView.setData(
                                entity.sku_list,
                                ConfigMgr.getInstance().config.isAuto_vip_privacy_first_daily_report,
                                R.color.color_9094B0,
                                true
                            )
                            privacyView.isFirstDailyReport()
                            imgLayer.onDebounceClickListener {
                                SensorsDataEvent.firstDayReportPageClick("下单购买")
                                privacyView.isAgreePrivacy(entity.sku_list, "首次日报告页面") {
                                    PayCenter.getInstance().doSubmitPay(
                                        this@SleepMonitorCompleteSkuActivity,
                                        entity.sku_list
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        event?.let {
            when (it.eventCode) {
                ConsEventCode.PAY_SUCCESS_EVENT -> finish()

                ConsEventCode.PAY_IN_PROGRESS_EVENT -> DialogUtil.showWaitLoading(
                    this,
                    getString(R.string.string_order_validation_txt),
                    false
                )

                else -> {}
            }
        }
    }

}