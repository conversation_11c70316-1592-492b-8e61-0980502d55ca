package com.imoblife.now.activity.member

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.SkuStyleVipImgAdapter
import com.imoblife.now.bean.AdResourceBean
import com.imoblife.now.bean.SubSkuInfo
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityVipSkuDBinding
import com.imoblife.now.ext.preLoadImg
import com.imoblife.now.ext.removeAnim
import com.imoblife.now.ext.startVipSkuActivity
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.view.dialog.PopupHalfAlterImgDialog
import com.imoblife.now.viewmodel.PaymentViewModel
import org.greenrobot.eventbus.EventBus

/**
 * 订阅中心通用版
 */
class VipSkuDActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        fun startActivity(
            context: Context,
            adResourceBean: AdResourceBean,
            isPracticeFm: Boolean,
            // 是否为二次全屏
            isSecondFull: Boolean = false
        ) {
            Intent(context, VipSkuDActivity::class.java).apply {
                putExtra(ConsIntent.BUNDLE_AD_RESOURCE, adResourceBean)
                putExtra(ConsIntent.BUNDLE_IS_PRACTICE_PAGE, isPracticeFm)
                putExtra(ConsIntent.BUNDLE_IS_SECOND_FULL, isSecondFull)
                context.startActivity(this)
                (context as Activity).overridePendingTransition(R.anim.bottom_in, R.anim.bottom_out)
            }
        }

    }

    override fun finish() {
        // 弹出首训 - 点击关闭全屏时
        if (!mIsPurchase && mIsPracticeFm) {
            EventBus.getDefault().post(BaseEvent(ConsEventCode.VIP_PLAN_BOTTOM_DIALOG_ACTION_CLICK))
        }
        if (!mIsPurchase) {
            mSkuInfo?.let {
                if (EmptyUtils.isNotEmpty(it.auto_open_sku_page)) {
                    startVipSkuActivity(it.auto_open_sku_page, isSecondFull = true)
                }
            }
        }
        super.finish()
        //关闭窗体动画显示
        overridePendingTransition(0, R.anim.bottom_out)
    }

    private lateinit var mBind: ActivityVipSkuDBinding
    private val skuStyleVerticalDAdapter by lazy(LazyThreadSafetyMode.NONE) {
        SkuStyleVipImgAdapter(
            R.layout.layout_sku_vip_item_dynamic_img_size
        )
    }
    private val popupHalfAlterDialog by lazy(LazyThreadSafetyMode.NONE) {
        PopupHalfAlterImgDialog(
            this
        )
    }
    private var isClickCloseVipPage = false
    private var isShowCancelDialogPage = false
    private var mAdResourceBean: AdResourceBean? = null
    private var mSkuInfo: SubSkuInfo? = null

    // 是否购买成功
    private var mIsPurchase = false

    // 全屏模板 - 是否来自首页练习
    private var mIsPracticeFm = false

    // 是否为二次全屏
    private var mIsSecondFull = false

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_AD_RESOURCE)) {
                mAdResourceBean =
                    it.getSerializableExtra(ConsIntent.BUNDLE_AD_RESOURCE) as AdResourceBean
            }
            if (hasExtra(ConsIntent.BUNDLE_IS_PRACTICE_PAGE)) {
                mIsPracticeFm = it.getBooleanExtra(ConsIntent.BUNDLE_IS_PRACTICE_PAGE, false)
            }
            if (hasExtra(intent, ConsIntent.BUNDLE_IS_SECOND_FULL)) {
                mIsSecondFull = it.getBooleanExtra(ConsIntent.BUNDLE_IS_SECOND_FULL, false)
            }
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.activity_vip_sku_d
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentStatusBar().init()
    }

    override fun initVM() = ViewModelProvider(this).get(PaymentViewModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityVipSkuDBinding
        mBind.recyclerSku.apply {
            removeAnim()
            adapter = skuStyleVerticalDAdapter
        }
        lifecycle.addObserver(mBind.paySubmitTxt)
        mBind.paySubmitTxt.initBackGroundRes()
        mBind.paySubmitTxt.setOnClickListener {
            doPreviewPay()
        }
        mBind.closeImg.setOnClickListener {
            mSkuInfo?.let {
                isClickCloseVipPage = true
                it.isIs_full_resource = true
                adSkuPageClose(
                    this,
                    mViewModel,
                    it,
                    isShowCancelDialogPage,
                    skuId = skuStyleVerticalDAdapter.getSubscribe()?.id ?: -1
                )
            } ?: finish()
        }
        val isShouAutoCloseable = if (ConfigMgr.getInstance().config.isAuto_vip_privacy_ad) {
            !ConfigMgr.getInstance().config.auto_vip_privacy_special_ad_list.contains(mSkuInfo!!.source_id)
        } else {
            false
        }
        skuStyleVerticalDAdapter.setActionBlock {
            mBind.subProtocolPrivacy.setAgreementCheckboxFalse()
            skuStyleVerticalDAdapter.getSubscribe()
                ?.let { it1 ->
                    mBind.subProtocolPrivacy.setData(
                        it1, isShouAutoCloseable
                    )
                }
        }
//        skuStyleVerticalDAdapter.setClickActionBlock {
//            mAdResourceBean?.let {
//                if (it.sku_pull_payment ==1){
//                    doPreviewPay()
//                }
//            }
//        }
    }

    private fun doPreviewPay(){
        val entity = skuStyleVerticalDAdapter.getSubscribe()
        entity?.pre_advertising_id = mAdResourceBean?.id ?: 0
        entity?.pre_source_id = mAdResourceBean?.source_id ?: 0
        entity?.pre_source_type = mAdResourceBean?.source_type ?: 0
        entity?.params?.course_id = mAdResourceBean?.pre_course_id ?: 0
        mBind.subProtocolPrivacy.isAgreePrivacy(entity, "首页全屏") {
            PayCenter.getInstance().doSubmitPay(this, entity)
        }
    }
    override fun initData() {
        mAdResourceBean?.let { getAdSkuPageData(mViewModel, it) } ?: finish()
    }

    override fun startObserve() {
        mViewModel.adPageSkuList.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    entity.preLoadImg(this)
                    setSukInfo(entity)
                }
            }
        }
        mViewModel.adPageCancelSkuList.observe(this) { uiStatus ->
            showHalfDialog(uiStatus, popupHalfAlterDialog)
            if (isClickCloseVipPage) {
                isShowCancelDialogPage = true
            }
        }
    }

    private fun setSukInfo(subSkuInfo: SubSkuInfo?) {
        subSkuInfo?.let {
            mSkuInfo = it
            ImageLoader.loadImageUrl(this, it.banner, mBind.topHeaderImg)
            ImageLoader.loadImageUrl(
                this,
                it.close_img,
                mBind.closeImg,
                R.mipmap.icon_vip_sku_close
            )
            skuStyleVerticalDAdapter.setNewData(subSkuInfo.sku_list)
            mBind.paySubmitTxt.setPayButtonData(subSkuInfo.pay_button)
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.PAY_CANCEL_EVENT || event?.eventCode == ConsEventCode.PAY_FAIL_EVENT) {
            mIsPurchase = false
            mSkuInfo?.let {
                it.isIs_full_resource = true
                adSkuPageCancelPay(
                    mViewModel,
                    it,
                    skuId = skuStyleVerticalDAdapter.getSubscribe()?.id ?: -1
                )
            }
        } else if (event?.eventCode == ConsEventCode.PAY_SUCCESS_EVENT) {
            mIsPurchase = true
            if (!UserMgr.getInstance().isLogin) {
                LoginCenter.getInstance().loginControl(this)
            }
            finish()
        }
    }

    override fun onBackPressed() {}
}