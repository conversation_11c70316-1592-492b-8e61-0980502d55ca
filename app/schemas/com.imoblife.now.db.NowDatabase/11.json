{"formatVersion": 1, "database": {"version": 11, "identityHash": "a3db8a41dc1625a8930e6fbcd0069b5f", "entities": [{"tableName": "User", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `token` TEXT, `login_type` TEXT, `username` TEXT, `nickname` TEXT, `avatar` TEXT, `gender` TEXT, `range_age` TEXT, `user_is_manger` INTEGER NOT NULL, `register_time` TEXT, `user_practice_num` INTEGER NOT NULL, `practice_minute_count` INTEGER NOT NULL, `continue_day_count` INTEGER NOT NULL, `user_binding` INTEGER NOT NULL, `coupons_num` INTEGER NOT NULL, `invit_polite_url` TEXT, `is_login` INTEGER NOT NULL, `is_question` INTEGER NOT NULL, `level` INTEGER NOT NULL, `level_score` INTEGER NOT NULL, `level_total_score` INTEGER NOT NULL, `nickname_color` TEXT, `reg_days` TEXT, `sleep_total_time` INTEGER NOT NULL, `sleep_average_time` INTEGER NOT NULL, `sleep_average_quality` INTEGER NOT NULL, `current_level` TEXT, `target_practice_hours` TEXT, `gift_address_url` TEXT, `now_vip` INTEGER, `now_vip_forever` INTEGER, `now_expire_time` INTEGER, `now_expire_notice` TEXT, `now_user_coin_count` REAL, `yoga_vip` INTEGER, `yoga_expire_time` INTEGER, `yoga_expire_notice` TEXT, `excellent_vip` INTEGER, `excellent_expire_time` INTEGER, `excellent_expire_notice` TEXT, `book_vip` INTEGER, `book_expire_time` INTEGER, `book_expire_notice` TEXT, `sport_vip` INTEGER, `sport_expire_time` INTEGER, `sport_expire_notice` TEXT, `training_camp_vip` INTEGER, `training_camp_expire_time` INTEGER, `training_camp_expire_notice` TEXT, `wooden_fish_vip` INTEGER, `wooden_fish_expire_time` INTEGER, `wooden_fish_expire_notice` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "id", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "token", "columnName": "token", "affinity": "TEXT", "notNull": false}, {"fieldPath": "login_type", "columnName": "login_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nickname", "columnName": "nickname", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "range_age", "columnName": "range_age", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is_manager", "columnName": "user_is_manger", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "created_at", "columnName": "register_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "user_practice_num", "columnName": "user_practice_num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "practice_minute_count", "columnName": "practice_minute_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "continue_day_count", "columnName": "continue_day_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "binding_mobile", "columnName": "user_binding", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "coupons_num", "columnName": "coupons_num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "invit_polite_url", "columnName": "invit_polite_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is<PERSON>ogin", "columnName": "is_login", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is_question", "columnName": "is_question", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level_score", "columnName": "level_score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level_total_score", "columnName": "level_total_score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nickname_color", "columnName": "nickname_color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "regDays", "columnName": "reg_days", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sleep_total_time", "columnName": "sleep_total_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sleep_average_time", "columnName": "sleep_average_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sleep_average_quality", "columnName": "sleep_average_quality", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "current_level", "columnName": "current_level", "affinity": "TEXT", "notNull": false}, {"fieldPath": "target_practice_hours", "columnName": "target_practice_hours", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gift_address_url", "columnName": "gift_address_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "now_vip.is_vip", "columnName": "now_vip", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "now_vip.vip_forever", "columnName": "now_vip_forever", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "now_vip.expire_time", "columnName": "now_expire_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "now_vip.expire_notice", "columnName": "now_expire_notice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "now_vip.coin_count", "columnName": "now_user_coin_count", "affinity": "REAL", "notNull": false}, {"fieldPath": "yoga.is_vip", "columnName": "yoga_vip", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "yoga.expire_time", "columnName": "yoga_expire_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "yoga.expire_notice", "columnName": "yoga_expire_notice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "excellent.is_vip", "columnName": "excellent_vip", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "excellent.expire_time", "columnName": "excellent_expire_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "excellent.expire_notice", "columnName": "excellent_expire_notice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "book.is_vip", "columnName": "book_vip", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "book.expire_time", "columnName": "book_expire_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "book.expire_notice", "columnName": "book_expire_notice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "now_sport.is_vip", "columnName": "sport_vip", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "now_sport.expire_time", "columnName": "sport_expire_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "now_sport.expire_notice", "columnName": "sport_expire_notice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "training_camp.is_vip", "columnName": "training_camp_vip", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "training_camp.expire_time", "columnName": "training_camp_expire_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "training_camp.expire_notice", "columnName": "training_camp_expire_notice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "wooden_fish.is_vip", "columnName": "wooden_fish_vip", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "wooden_fish.expire_time", "columnName": "wooden_fish_expire_time", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "wooden_fish.expire_notice", "columnName": "wooden_fish_expire_notice", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["userId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "AdRecord", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER NOT NULL, `ad_show_time` TEXT, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "adShowTime", "columnName": "ad_show_time", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "NowLog", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`unique_id` TEXT NOT NULL, `course_id` INTEGER NOT NULL, `section_id` INTEGER NOT NULL, `up_state` INTEGER NOT NULL, `user` TEXT, `practice_duration` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `course_type` TEXT, `start_time` INTEGER NOT NULL, `finish_time` INTEGER NOT NULL, `play_completed` INTEGER NOT NULL, `play_mode` TEXT, `page_id` INTEGER NOT NULL, `category_id` INTEGER NOT NULL, `page_title` TEXT, `category_title` TEXT, PRIMARY KEY(`unique_id`))", "fields": [{"fieldPath": "id", "columnName": "unique_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "catId", "columnName": "course_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "trackId", "columnName": "section_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "upState", "columnName": "up_state", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "user", "columnName": "user", "affinity": "TEXT", "notNull": false}, {"fieldPath": "practice_duration", "columnName": "practice_duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVipUser", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "courseType", "columnName": "course_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startTime", "columnName": "start_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finishTime", "columnName": "finish_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completed", "columnName": "play_completed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "playMode", "columnName": "play_mode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "page_id", "columnName": "page_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "category_id", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "page_title", "columnName": "page_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category_title", "columnName": "category_title", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["unique_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "DownLoadInfo", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` TEXT NOT NULL, `track_id` TEXT, `url` TEXT, `file_name` TEXT, `file_size` INTEGER NOT NULL, `break_point` INTEGER NOT NULL, `state` INTEGER NOT NULL DEFAULT 4, `level` INTEGER NOT NULL, `error_code` INTEGER NOT NULL, `file_path` TEXT, `md5` TEXT, `name` TEXT, `cat_id` TEXT, `r_01` TEXT, `r_02` TEXT, `r_03` TEXT, `r_04` TEXT, `r_05` TEXT, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "_id", "columnName": "_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "trackId", "columnName": "track_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fileName", "columnName": "file_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fileSize", "columnName": "file_size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "breakPoint", "columnName": "break_point", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "state", "columnName": "state", "affinity": "INTEGER", "notNull": true, "defaultValue": "4"}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "errorCode", "columnName": "error_code", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "filePath", "columnName": "file_path", "affinity": "TEXT", "notNull": false}, {"fieldPath": "md5", "columnName": "md5", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "cat_id", "columnName": "cat_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "r_01", "columnName": "r_01", "affinity": "TEXT", "notNull": false}, {"fieldPath": "r_02", "columnName": "r_02", "affinity": "TEXT", "notNull": false}, {"fieldPath": "r_03", "columnName": "r_03", "affinity": "TEXT", "notNull": false}, {"fieldPath": "r_04", "columnName": "r_04", "affinity": "TEXT", "notNull": false}, {"fieldPath": "r_05", "columnName": "r_05", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "Course", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER NOT NULL, `title` TEXT, `subtitle_new` TEXT, `content_new` TEXT, `title_img_new` TEXT, `content_img_new` TEXT, `label_img_new` TEXT, `sequence` INTEGER NOT NULL, `lecturer` INTEGER NOT NULL, `section_total_count` INTEGER NOT NULL, `collect_count` INTEGER NOT NULL, `join_user_count` INTEGER NOT NULL, `pay_count` INTEGER NOT NULL, `play_count` INTEGER NOT NULL, `track_ids` TEXT, `type_new` TEXT, `free` TEXT, `coin_price` REAL NOT NULL, `vip_coin_price` REAL NOT NULL, `price` REAL NOT NULL, `vip_price` REAL NOT NULL, `type` TEXT, `teacher` TEXT, `teacher_name` TEXT, `small_num` INTEGER NOT NULL, `item_num` INTEGER NOT NULL, `was_listen_num` INTEGER NOT NULL, `thumb_img` TEXT, `subtitle` TEXT, `resource_type` TEXT, `discount_price` REAL NOT NULL, `vip_discount` TEXT, `coin_discount_price` REAL NOT NULL, `playing_background_img` TEXT, `body_img_new` TEXT, `is_collect` INTEGER NOT NULL, `share_button_status` TEXT, `share_button_url` TEXT, `is_micro_course` INTEGER NOT NULL, `micro_course_link_url` TEXT, `micro_course_banner` TEXT, `is_share` INTEGER NOT NULL, `share_title` TEXT, `share_description` TEXT, `type_subcat` TEXT, `category_name` TEXT, `duration` INTEGER NOT NULL, `is_new` INTEGER NOT NULL, `is_listen_over` INTEGER NOT NULL, `is_history_listen` INTEGER NOT NULL, `model_id` INTEGER NOT NULL, `sleep_type_id` INTEGER NOT NULL, `content_details_title` TEXT, `content_details_url` TEXT, `content_practice` TEXT, `content_introduction` TEXT, `content_suitable` TEXT, `time_free_end` TEXT, `free_start_time` TEXT, `future_subscribe` INTEGER NOT NULL, `splay_count` INTEGER NOT NULL, `quotes` TEXT, `display_position` TEXT, `category_type` TEXT, `display_time` TEXT, `is_lock` INTEGER NOT NULL, `ad_id` INTEGER NOT NULL, `imgUrl` TEXT, `tag` TEXT, `lessionsID` TEXT, `update_status` INTEGER NOT NULL, `play_url` TEXT, `sections_id` INTEGER NOT NULL, `page_id` INTEGER NOT NULL, `category_id` INTEGER NOT NULL, `page_title` TEXT, `category_title` TEXT, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subtitle_new", "columnName": "subtitle_new", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content_new", "columnName": "content_new", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title_img_new", "columnName": "title_img_new", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content_img_new", "columnName": "content_img_new", "affinity": "TEXT", "notNull": false}, {"fieldPath": "label_img_new", "columnName": "label_img_new", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sequence", "columnName": "sequence", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lecturer", "columnName": "lecturer", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "section_total_count", "columnName": "section_total_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collect_count", "columnName": "collect_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "join_user_count", "columnName": "join_user_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pay_count", "columnName": "pay_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "play_count", "columnName": "play_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "trackIds", "columnName": "track_ids", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type_new", "columnName": "type_new", "affinity": "TEXT", "notNull": false}, {"fieldPath": "free", "columnName": "free", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coin_price", "columnName": "coin_price", "affinity": "REAL", "notNull": true}, {"fieldPath": "vip_coin_price", "columnName": "vip_coin_price", "affinity": "REAL", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "REAL", "notNull": true}, {"fieldPath": "vip_price", "columnName": "vip_price", "affinity": "REAL", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "teacher", "columnName": "teacher", "affinity": "TEXT", "notNull": false}, {"fieldPath": "teacher_name", "columnName": "teacher_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "small_num", "columnName": "small_num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "item_num", "columnName": "item_num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "was_listen_num", "columnName": "was_listen_num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumb_img", "columnName": "thumb_img", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subtitle", "columnName": "subtitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "resource_type", "columnName": "resource_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "discount_price", "columnName": "discount_price", "affinity": "REAL", "notNull": true}, {"fieldPath": "vip_discount", "columnName": "vip_discount", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coin_discount_price", "columnName": "coin_discount_price", "affinity": "REAL", "notNull": true}, {"fieldPath": "playing_background_img", "columnName": "playing_background_img", "affinity": "TEXT", "notNull": false}, {"fieldPath": "body_img_new", "columnName": "body_img_new", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is_collect", "columnName": "is_collect", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "share_button_status", "columnName": "share_button_status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "share_button_url", "columnName": "share_button_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is_micro_course", "columnName": "is_micro_course", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "micro_course_link_url", "columnName": "micro_course_link_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "micro_course_banner", "columnName": "micro_course_banner", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is_share", "columnName": "is_share", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "share_title", "columnName": "share_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "share_description", "columnName": "share_description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type_subcat", "columnName": "type_subcat", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category_name", "columnName": "category_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shichang", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is_new", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is_listen_over", "columnName": "is_listen_over", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is_history_listen", "columnName": "is_history_listen", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "model_id", "columnName": "model_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "model_sleep_id", "columnName": "sleep_type_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "content_details_title", "columnName": "content_details_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content_details_url", "columnName": "content_details_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "practice", "columnName": "content_practice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "introduction", "columnName": "content_introduction", "affinity": "TEXT", "notNull": false}, {"fieldPath": "suitable", "columnName": "content_suitable", "affinity": "TEXT", "notNull": false}, {"fieldPath": "free_end_time", "columnName": "time_free_end", "affinity": "TEXT", "notNull": false}, {"fieldPath": "free_start_time", "columnName": "free_start_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subscribe", "columnName": "future_subscribe", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "splay_count", "columnName": "splay_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "quotes", "columnName": "quotes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "display_position", "columnName": "display_position", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category_type", "columnName": "category_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "display_time", "columnName": "display_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is_lock", "columnName": "is_lock", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ad_id", "columnName": "ad_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "imgUrl", "columnName": "imgUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lessionsID", "columnName": "lessionsID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "update_status", "columnName": "update_status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "play_url", "columnName": "play_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sections_id", "columnName": "sections_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "page_id", "columnName": "page_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "category_id", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "page_title", "columnName": "page_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category_title", "columnName": "category_title", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "PayOrder", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`product_id` INTEGER NOT NULL, `type` TEXT, `product_name` TEXT, `pay_time` TEXT, PRIMARY KEY(`product_id`))", "fields": [{"fieldPath": "product_id", "columnName": "product_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "product_name", "columnName": "product_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pay_time", "columnName": "pay_time", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["product_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "Track", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER NOT NULL, `type` TEXT, `title` TEXT, `subtitle` TEXT, `course_id` INTEGER NOT NULL, `sequence` INTEGER NOT NULL, `url` TEXT, `video_url` TEXT, `document_url` TEXT, `main_duration` INTEGER NOT NULL, `audio_id` INTEGER NOT NULL, `free` TEXT, `track_size` INTEGER NOT NULL, `share_img` TEXT, `share_button_status` TEXT, `share_button_url` TEXT, `section_play_count` INTEGER NOT NULL, `resource_type` TEXT, `created_at` TEXT, `update_date` TEXT, `main_length` TEXT, `play_background_img` TEXT, `track_radio_detail_title` TEXT, `track_radio_detail_content` TEXT, `track_radio_detail_url` TEXT, `current_status` TEXT, `language` TEXT, `en` INTEGER NOT NULL, `cn` INTEGER NOT NULL, `section_quotes` TEXT, `track_completed` INTEGER NOT NULL, `is_like` INTEGER NOT NULL, `is_listen` INTEGER NOT NULL, `show_index` INTEGER NOT NULL, `group_id` INTEGER NOT NULL, `guest_name` TEXT, `pre_duration` INTEGER NOT NULL, `background_sound_url` TEXT, `track_thumb` TEXT, `thumb_img` TEXT, `finish_page_star` INTEGER NOT NULL, `finish_page_comment` INTEGER NOT NULL, `finish_page_timer` INTEGER NOT NULL, `is_recently_played` INTEGER NOT NULL, `play_times` INTEGER NOT NULL, `page_id` INTEGER NOT NULL, `category_id` INTEGER NOT NULL, `page_title` TEXT, `category_title` TEXT, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subtitle", "columnName": "subtitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "course_id", "columnName": "course_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sequence", "columnName": "sequence", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "video_url", "columnName": "video_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "document_url", "columnName": "document_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "main_duration", "columnName": "main_duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "audio_id", "columnName": "audio_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "free", "columnName": "free", "affinity": "TEXT", "notNull": false}, {"fieldPath": "size", "columnName": "track_size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "shareImg", "columnName": "share_img", "affinity": "TEXT", "notNull": false}, {"fieldPath": "share_button_status", "columnName": "share_button_status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "share_button_url", "columnName": "share_button_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "section_play_count", "columnName": "section_play_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "resource_type", "columnName": "resource_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "created_at", "columnName": "created_at", "affinity": "TEXT", "notNull": false}, {"fieldPath": "update_date", "columnName": "update_date", "affinity": "TEXT", "notNull": false}, {"fieldPath": "main_length", "columnName": "main_length", "affinity": "TEXT", "notNull": false}, {"fieldPath": "play_background_img", "columnName": "play_background_img", "affinity": "TEXT", "notNull": false}, {"fieldPath": "detail_title", "columnName": "track_radio_detail_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "detail_content", "columnName": "track_radio_detail_content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "detail_url", "columnName": "track_radio_detail_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "current_status", "columnName": "current_status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "language", "columnName": "language", "affinity": "TEXT", "notNull": false}, {"fieldPath": "en", "columnName": "en", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cn", "columnName": "cn", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "section_quotes", "columnName": "section_quotes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "trackCompleted", "columnName": "track_completed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is_like", "columnName": "is_like", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is_listen", "columnName": "is_listen", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "index", "columnName": "show_index", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "group_id", "columnName": "group_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "guest_name", "columnName": "guest_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pre_duration", "columnName": "pre_duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "background_sound_url", "columnName": "background_sound_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thumb_img", "columnName": "track_thumb", "affinity": "TEXT", "notNull": false}, {"fieldPath": "course_thumb_img", "columnName": "thumb_img", "affinity": "TEXT", "notNull": false}, {"fieldPath": "finish_page_star", "columnName": "finish_page_star", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finish_page_comment", "columnName": "finish_page_comment", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finish_page_timer", "columnName": "finish_page_timer", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is_recently_played", "columnName": "is_recently_played", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "play_times", "columnName": "play_times", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "page_id", "columnName": "page_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "category_id", "columnName": "category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "page_title", "columnName": "page_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category_title", "columnName": "category_title", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "track_group", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`group_id` INTEGER NOT NULL, `title` TEXT, `course_id` INTEGER NOT NULL, `order_num` INTEGER NOT NULL, `unlock_date` TEXT, PRIMARY KEY(`group_id`))", "fields": [{"fieldPath": "id", "columnName": "group_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "course_id", "columnName": "course_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "order_num", "columnName": "order_num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unlock_date", "columnName": "unlock_date", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["group_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "sleep_record_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`only_id` TEXT NOT NULL, `user_id` INTEGER NOT NULL, `sleep_time` INTEGER NOT NULL, `wake_up_time` INTEGER NOT NULL, PRIMARY KEY(`only_id`))", "fields": [{"fieldPath": "only_id", "columnName": "only_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "user_id", "columnName": "user_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sleep_time", "columnName": "sleep_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "wake_up_time", "columnName": "wake_up_time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["only_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "sleep_monitor_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `sleep_only_id` TEXT, `point_time` INTEGER NOT NULL, `decibel` INTEGER NOT NULL, `light` INTEGER NOT NULL, `move` INTEGER NOT NULL, FOREIGN KEY(`sleep_only_id`) REFERENCES `sleep_record_table`(`only_id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sleep_only_id", "columnName": "sleep_only_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "point_time", "columnName": "point_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "decibel", "columnName": "decibel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "light", "columnName": "light", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "move", "columnName": "move", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_sleep_monitor_table_sleep_only_id", "unique": false, "columnNames": ["sleep_only_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_sleep_monitor_table_sleep_only_id` ON `${TABLE_NAME}` (`sleep_only_id`)"}], "foreignKeys": [{"table": "sleep_record_table", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["sleep_only_id"], "referencedColumns": ["only_id"]}]}, {"tableName": "sleep_sound_bite_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `start_time` INTEGER NOT NULL, `end_time` INTEGER NOT NULL, `sound_features` TEXT, `sleep_only_id` TEXT, `local_path` TEXT, `oss_path` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "start_time", "columnName": "start_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "end_time", "columnName": "end_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sound_features", "columnName": "sound_features", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sleep_only_id", "columnName": "sleep_only_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "local_path", "columnName": "local_path", "affinity": "TEXT", "notNull": false}, {"fieldPath": "oss_path", "columnName": "oss_path", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'a3db8a41dc1625a8930e6fbcd0069b5f')"]}}