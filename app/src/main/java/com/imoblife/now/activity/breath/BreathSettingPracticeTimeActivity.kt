package com.imoblife.now.activity.breath

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.view.View
import androidx.databinding.DataBindingUtil
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.mvvm.BaseActivity
import com.imoblife.commlibrary.view.wheelview.view.WheelView
import com.imoblife.now.R
import com.imoblife.now.activity.setting.BuzzerActivity
import com.imoblife.now.adapter.ArrayWheelAdapter
import com.imoblife.now.adapter.NumericWheelAdapter
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.ActivityBreathBinding
import com.imoblife.now.databinding.ActivitySettingPracticeTimeBinding
import com.imoblife.now.model.StatisticalMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.*
import com.imoblife.now.view.dialog.OpenNotificationDialog

private val AMAPData by lazy {
    mutableListOf<String>().apply {
        add("上午")
        add("下午")
    }
}

/**
 * 设置提醒用户练习呼吸Activity
 */
class BreathSettingPracticeTimeActivity : BaseActivity(), View.OnClickListener {

    private lateinit var mBind: ActivitySettingPracticeTimeBinding
    private lateinit var mBindBreath: ActivityBreathBinding

    override fun getLayoutResId(): Int = 0

    @SuppressLint("ClickableViewAccessibility")
    override fun initView() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_setting_practice_time)
        mBindBreath =
            DataBindingUtil.inflate(layoutInflater, R.layout.activity_breath, null, false)
        mBindBreath.lifecycleOwner = this
        mBind.apply {
            lifecycleOwner = this@BreathSettingPracticeTimeActivity
            mBindBreath.ivClose.setOnClickListener(this@BreathSettingPracticeTimeActivity)
            ivClose.setOnClickListener(this@BreathSettingPracticeTimeActivity)
            tvSave.setOnClickListener(this@BreathSettingPracticeTimeActivity)
            wvAmAp.setOnTouchListener { _: View, _ -> true }
            initWheelView(wvAmAp)
            wvAmAp.setCyclic(false)
            initWheelView(wvHours)
            initWheelView(wvMinutes)
            stSwitch.isChecked = true

            //默认显示8：00
            try {
                wvHours.currentItem = DateUtil.getCurrentTimeH()
                wvMinutes.currentItem = DateUtil.getCurrentTimeMinutes()
                if (DateUtil.getCurrentTimeH() < 12) {
                    if (wvAmAp.currentItem != 0) {
                        wvAmAp.currentItem = 0
                    }
                } else {
                    if (wvAmAp.currentItem != 1) {
                        wvAmAp.currentItem = 1
                    }
                }
            } catch (e: Exception) {
                wvHours.currentItem = 8
                wvMinutes.currentItem = 0
            }
            wvHours.setOnItemSelectedListener {
                if (it < 12) {
                    if (wvAmAp.currentItem != 0) {
                        wvAmAp.currentItem = 0
                    }
                } else {
                    if (wvAmAp.currentItem != 1) {
                        wvAmAp.currentItem = 1
                    }
                }
            }
        }
    }

    private fun initWheelView(wheelView: WheelView) {
        wheelView.setTextSize(23F)
        wheelView.setLineSpacingMultiplier(1.8F)
        wheelView.setDividerColor(Color.WHITE)
        wheelView.setTextColorOut(Color.WHITE)
        wheelView.setTextColorCenter(Color.WHITE)
    }

    override fun initData() {
        mBindBreath.apply {
            toolbar.title = ""
            toolbarCenterTitleTv.text = getString(R.string.setting_practice_time)
        }
        ImageLoader.loadImageLocal(
            this,
            R.mipmap.breath_setting_practice_time,
            R.mipmap.breath_setting_practice_time,
            mBind.ivBg
        )

        mBind.apply {
            wvAmAp.adapter = ArrayWheelAdapter(AMAPData)
            wvHours.adapter = NumericWheelAdapter(0, 23)
            wvMinutes.adapter = NumericWheelAdapter(0, 59)
        }

    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_close -> {
                finish()
            }
            R.id.tv_save -> {
                if (NotificationUtil.isNotificationEnable(this)) {
                    saveBuzzer()
                } else {
                    OpenNotificationDialog().showDialog(
                        this,
                        ConsRequestCode.SEND_REQUEST_SETTING_NOTIFY
                    )
                }
            }
        }
    }

    /**
     * 设置提醒
     */
    private fun saveBuzzer() {
        if (NotificationUtil.isNotificationEnable(this)) {
            val hours = mBind.wvHours.currentItem
            val minutes = mBind.wvMinutes.currentItem
            BuzzerActivity.savePracticeRemind(if (mBind.stSwitch.isChecked) 1 else 2, hours, minutes,1)
            ToastUtils.showLongToast(getString(R.string.save_hint))
            finish()
        }
    }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .titleBar(R.id.toolbar)
            .init()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == ConsRequestCode.SEND_REQUEST_SETTING_NOTIFY) {
            saveBuzzer()
        }
    }

}