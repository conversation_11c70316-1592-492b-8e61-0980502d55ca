package com.imoblife.now.activity.collect

import com.imoblife.now.mvvm.BaseViewModel
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.CollectTrackBean
import com.imoblife.now.bean.Course

class CollectViewModel: BaseViewModel<Any?>() {
    private val collectRepository by lazy { CollectRepository() }
    var collectCourses=MutableLiveData<UiStatus<List<Course>>>()
    var collectTracks=MutableLiveData<UiStatus<List<CollectTrackBean>>>()
    var collectCourseChange=MutableLiveData<UiStatus<Boolean>>()
    var collectTrackChange=MutableLiveData<UiStatus<Boolean>>()

    fun getCollectCourse(isReadCache: Boolean){
        collectRepository.getCollectCourse(isReadCache,collectCourses)
    }
    fun getCollectTrack(){
         collectRepository.getCollectTrack(collectTracks)
    }
    fun addCollectCourse(course: Course){
        collectRepository.addCollectCourse(course,collectCourseChange)
    }
    fun addCollectTrack(courseId:Int,trackId:Int){
        collectRepository.addCollectTrack(courseId,trackId,collectTrackChange)
    }
}