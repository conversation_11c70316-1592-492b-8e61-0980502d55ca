package com.imoblife.now.activity.monitor.alarm

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutSleepRemindViewBinding
import com.imoblife.now.ext.getBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：铃声 | 轻唤醒场景音 - 睡眠闹钟 - Adapter
 * mType == 0 为闹钟铃声
 * mType == 1 为轻唤醒场景音
 */
class SleepRemindAdapter : BaseQuickAdapter<String, BaseViewHolder>(R.layout.layout_sleep_remind_view) {

    override fun convert(holder: BaseViewHolder, item: String) {
        holder.getBinding(LayoutSleepRemindViewBinding::bind).apply {
            checkBox.setOnCheckedChangeListener { _, isChecked ->

            }
        }
    }

}