package com.imoblife.now.activity.comment;

import android.content.Context;
import android.content.Intent;

import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.imoblife.commlibrary.base.BaseEvent;
import com.imoblife.now.MyApplication;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.adapter.CommentDetailAdapter;
import com.imoblife.now.bean.CommentComment;
import com.imoblife.now.bean.CommentCourse;
import com.imoblife.now.constant.ConsCommon;
import com.imoblife.now.constant.ConsEventCode;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.constant.ConsRequestCode;
import com.imoblife.now.databinding.ActivityCommentDetailBinding;
import com.imoblife.now.model.CommentMgr;
import com.imoblife.now.model.UserMgr;
import com.imoblife.now.net.BaseCallBack;
import com.imoblife.now.util.ToastUtils;
import com.imoblife.now.view.dialog.BottomMenuDialog;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class CommentDetailActivity<T> extends MvpBaseActivity {

    private ActivityCommentDetailBinding mBind;

    private CommentDetailAdapter commentDetailAdapter;
    private List<T> commentList = new ArrayList<>();
    private int commentId;
    private int courseId = 0;
    private int page = 1;
    private int count = Integer.MAX_VALUE;
    private CommentCourse.Comment commentCourse;

    @Override
    protected int setContentViewId() {
        return 0;
    }

    public static void openCommentDetailActivity(Context context, int commentId, int courseId) {
        Intent intent = new Intent(context, CommentDetailActivity.class);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID, commentId);
        intent.putExtra(ConsIntent.BUNDLE_COURSE_ID, courseId);
        context.startActivity(intent);
    }

    @Override
    protected void superInit(Intent intent) {
        super.superInit(intent);
        if (hasExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID)) {
            commentId = getIntent().getIntExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_ID)) {
            courseId = getIntent().getIntExtra(ConsIntent.BUNDLE_COURSE_ID, 0);
        }
    }

    @Override
    protected void initDataBinding() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_comment_detail);
        mBind.setLifecycleOwner(this);
    }

    @Override
    protected void initView() {
        mBind.title.titleBackImg.setImageResource(R.mipmap.icon_back);
        mBind.refreshLayout.setColorSchemeResources(R.color.main_color);
        commentDetailAdapter = new CommentDetailAdapter(this);
        mBind.recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mBind.recyclerView.setAdapter(commentDetailAdapter);
        mBind.refreshLayout.setOnRefreshListener(() -> getComment(true));
        commentDetailAdapter.setAdapterOnViewClick((rId, data) -> sendCommentComment(data));

        mBind.title.titleBackImg.setOnClickListener(v -> onBackPressed());
        mBind.bottomCommentLayout.commentBottomLly.setOnClickListener(v -> sendCommentComment(commentCourse));
    }

    @Override
    protected void initData() {
        getComment(true);
    }

    public void sendCommentComment(Object data) {
        if (data instanceof CommentComment.ListBean) {
            CommentComment.ListBean comment = ((CommentComment.ListBean) data);
            if (UserMgr.getInstance().getLoginUserId().equals(comment.getUid() + "")) {
                deleteComment(ConsCommon.COMMENT_TYPE_COMMENT, comment.getRid());
            } else {
                startComment(comment.getCourse_id(), comment.getComment_id(), comment.getUid(), comment.getFid(), comment.getNickname());
            }
        } else if (data instanceof CommentCourse.Comment) {
            CommentCourse.Comment comment = ((CommentCourse.Comment) data);
            startComment(comment.getCourse_id(), comment.getId(), comment.getUser_id(), 0, comment.getNickname());
        }
    }

    private void deleteComment(final int type, final int commentId) {
        BottomMenuDialog.BottomMenuBuilder bottomMenuDialog = new BottomMenuDialog.BottomMenuBuilder();
        bottomMenuDialog.addItem(getString(R.string.delete_txt), v -> delete(type, commentId));
        bottomMenuDialog.addItem(getString(R.string.string_cancel_text), null);
        bottomMenuDialog.build().show(getSupportFragmentManager());
    }

    private void delete(int type, int commentId) {
        CommentMgr.getInstance().deleteComment(type, commentId, new BaseCallBack() {
            @Override
            public void onSuc(Object response) {
                boolean isSuccess = (boolean) response;
                if (isSuccess) {
                    page = 1;
                    getComment(true);
                    ToastUtils.showShortToastCenter(getString(R.string.string_comment_delete_successful));
                } else {
                    ToastUtils.showShortToastCenter(getString(R.string.string_comment_delete_failed));
                }
            }

            @Override
            public void onFail(String message) {
            }
        });
    }

    /**
     * 发表对评论的评论
     *
     * @param courseId
     * @param commentId
     * @param fId
     * @param rId
     * @param nickname
     */
    private void startComment(int courseId, int commentId, int fId, int rId, String nickname) {
        Intent intent = new Intent(this, SendCommentActivity.class);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_TYPE, ConsCommon.COMMENT_TYPE_COMMENT);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID, courseId);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_ID, commentId);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_FID, fId);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_RID, rId);
        intent.putExtra(ConsIntent.BUNDLE_KEY_REPLY_NICKNAME, nickname);
        startActivityForResult(intent, ConsRequestCode.SEND_REQUEST_DETAIL_COMMENT_CODE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == ConsRequestCode.SEND_REQUEST_DETAIL_COMMENT_CODE) {
            getComment(true);
        }
    }

    private void getComment(final boolean scrollToTop) {
        CommentMgr.getInstance().getCommentComment(commentId, page, count, new BaseCallBack() {
            @Override
            public void onSuc(Object response) {
                hiddenRefresh();
                CommentComment commentComment = (CommentComment) response;
                if (commentComment == null) {
                    return;
                }
                commentCourse = commentComment.getComment();
                EventBus.getDefault().post(new BaseEvent(ConsEventCode.CHANGE_COMMENT_COURSE_DETAIL_CHANGE, commentComment));

                mBind.bottomCommentLayout.commentTxt.setHint(getString(R.string.string_reply_comment) + commentComment.getComment().getNickname());
                mBind.title.titleContentText.setText(String.format(getResources().getString(R.string.string_total_reply), commentComment.getCount()));
                commentList.clear();
                commentList.add(0, (T) commentComment.getComment());
                commentList.addAll(1, (Collection<? extends T>) commentComment.getList());
                commentDetailAdapter.setUpdate(commentList);
                if (scrollToTop) {
                    mBind.recyclerView.scrollToPosition(0);
                }
            }

            @Override
            public void onFail(String message) {
                hiddenRefresh();
            }
        });
    }

    private void hiddenRefresh() {
        if (mBind.refreshLayout.isRefreshing()) {
            mBind.refreshLayout.setRefreshing(false);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mBind.bottomCommentLayout.commentTxt.setText(MyApplication.getInstance().defaultEditStr);
    }

    @Override
    public void onEventMainThread(BaseEvent event) {
        super.onEventMainThread(event);
        if (event.getEventCode() == ConsEventCode.CHANGE_COURSE_COMMENT_ZAN_EVENT) {
            getComment(false);
        }
    }

//    @Override
//    public void onBackPressed() {
//        super.onBackPressed();
//        if (courseId!=0){
//            ProductDetailActivity.openActivity(this,courseId,-1);
//            finish();
//        }
//    }
}
