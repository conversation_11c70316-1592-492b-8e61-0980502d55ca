package com.imoblife.now.activity.category

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.databinding.LayoutItemCategoryNavigationRvItemBinding
import com.imoblife.now.ext.pageRoute

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/11
 * 描   述：首页 - 金刚区 - 分类导航 - 分类Item - itemView
 */
class CategoryNavigationItemItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {

    private var mBind: LayoutItemCategoryNavigationRvItemBinding =
        DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.layout_item_category_navigation_rv_item,
            this,
            true
        )

    private var mPosition: Int = 0

    private var mEntity: CategoryNavigationItemEntity? = null

    init {
        setOnClickListener { mEntity?.pageRoute(context, mPosition) }
    }

    fun setItemData(itemData: CategoryNavigationItemEntity, position: Int) {
        mBind.apply {
            mPosition = position
            mEntity = itemData
            entity = itemData
            executePendingBindings()
        }
    }

}