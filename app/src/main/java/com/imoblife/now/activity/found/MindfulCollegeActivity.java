package com.imoblife.now.activity.found;

import android.content.Context;
import android.content.Intent;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.widget.TextView;
import com.flyco.tablayout.SlidingTabLayout;
import com.imoblife.commlibrary.mvp.CreatePresenter;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.adapter.BasePageAdapter;
import com.imoblife.now.bean.CollegeType;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.fragment.CollegeFragment;
import com.imoblife.now.mvp_contract.CollegeTypeContract;
import com.imoblife.now.mvp_presenter.CollegeTypePresenter;
import com.imoblife.now.view.NoAnimationViewPager;
import java.util.List;

/**
 * 正念学院
 */
@CreatePresenter(presenter = CollegeTypePresenter.class)
public class MindfulCollegeActivity extends MvpBaseActivity<CollegeTypePresenter> implements CollegeTypeContract.IFoundTypeView {
    private SlidingTabLayout tabLayout;
    private NoAnimationViewPager viewpager;
    private BasePageAdapter mAdapter;
    private int fundTypeId;
    private String fundTypeTitle;
    public static void openCollegeActivity(Context context,int id,String title){
        Intent intent=new Intent(context, MindfulCollegeActivity.class);
        intent.putExtra(ConsIntent.BUNDLE_FOUND_TYPE_ID,id);
        intent.putExtra(ConsIntent.BUNDLE_FOUND_TITLE,title);
        context.startActivity(intent);
    }
    @Override
    protected void superInit(Intent intent) {
        super.superInit(intent);
        if (hasExtra(ConsIntent.BUNDLE_FOUND_TYPE_ID)){
            fundTypeId=getIntent().getIntExtra(ConsIntent.BUNDLE_FOUND_TYPE_ID,24);
        }
        if (hasExtra(ConsIntent.BUNDLE_FOUND_TITLE)){
            fundTypeTitle=getIntent().getStringExtra(ConsIntent.BUNDLE_FOUND_TITLE);
        }
    }

    @Override
    protected int setContentViewId() {
        return R.layout.activity_college;
    }
    @Override
    protected void initView() {
        TextView title= (TextView) findView(R.id.title_content_text);
        title.setText(fundTypeTitle);
        findView(R.id.title_back_img).setOnClickListener(v -> finish());
        viewpager=findViewById(R.id.viewpager);
        tabLayout=findViewById(R.id.tabLayout);
        tabLayout.setSnapOnTabClick(true);
        mAdapter = new BasePageAdapter<CollegeType>(getSupportFragmentManager()) {
            @Override
            protected Fragment getFragment(int position, CollegeType data) {
                return CollegeFragment.getInstance(data);
            }
            @Override
            protected String getTitle(CollegeType data) {
                return data.getTitle();
            }
        };
        viewpager.setOffscreenPageLimit(3);
        viewpager.setAdapter(mAdapter);
        tabLayout.setViewPager(viewpager);
        viewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {}
            @Override
            public void onPageSelected(int position) {
                for (int i = 0; i < mAdapter.getCount(); i++) {
                    boolean isSelect = i == position;
                    tabLayout.getTitleView(i).setTextSize(isSelect?18:13);
                }
            }
            @Override
            public void onPageScrollStateChanged(int state) {}
        });
    }

    @Override
    protected void initData() {
        getPresenter().getCollegeType(fundTypeId);
    }
    @Override
    public void CollegeTypeData(List<CollegeType> collegeTypes) {
        if (collegeTypes==null){
            return;
        }
        mAdapter.setNewData(collegeTypes);
        tabLayout.notifyDataSetChanged();
        if (collegeTypes!=null && collegeTypes.size()>0){
            if (tabLayout!=null && tabLayout.getTitleView(0)!=null) {
                tabLayout.getTitleView(0).setTextSize(18);
            }
        }
    }
}
