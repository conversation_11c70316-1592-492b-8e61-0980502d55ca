package com.imoblife.now.activity.category

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationTagItemEntity
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.util.ImageLoader
import com.makeramen.roundedimageview.RoundedImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - 呼吸计时
 */
class CategoryBreathTimingAdapter :
    BaseQuickAdapter<CategoryNavigationTagItemEntity, BaseViewHolder>(R.layout.layout_item_category_breath_timing) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationTagItemEntity?) {
        item?.apply {
            val rivImg = holder.getView<RoundedImageView>(R.id.rivImg)

            ImageLoader.loadImageUrl(mContext, item.icon, rivImg)
            holder.itemView.setOnClickListener { pageRoute(mContext, holder.layoutPosition) }
        }
    }

}