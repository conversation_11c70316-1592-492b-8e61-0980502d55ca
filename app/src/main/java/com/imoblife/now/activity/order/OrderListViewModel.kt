package com.imoblife.now.activity.order

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.OrderListEntity
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/10/17
 * 描   述：订单列表 - ViewModel
 */
class OrderListViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy(LazyThreadSafetyMode.NONE) { OrderListRepository() }

    // 获取订单列表
    private val _onSiteNotifications = MutableLiveData<UiStatus<List<OrderListEntity>>>()
    val onSiteNotifications: LiveData<UiStatus<List<OrderListEntity>>> = _onSiteNotifications

    /**
     * 获取订单列表
     */
    fun getOnSiteNotifications() {
        mRepository.getOnSiteNotifications(initPage = true, _onSiteNotifications)
    }

    /**
     * 获取订单列表 - 加载更多
     */
    fun getOnSiteNotificationsLoadMore() {
        mRepository.getOnSiteNotifications(initPage = false, _onSiteNotifications)
    }

}