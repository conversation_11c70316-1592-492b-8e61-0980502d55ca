package com.imoblife.now.activity.mood

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.Course
import com.imoblife.now.bean.DayMoodEntity
import com.imoblife.now.bean.Mood
import com.imoblife.now.bean.MoodLog
import com.imoblife.now.bean.WeekDayMood
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import java.util.Calendar
import java.util.Calendar.getInstance

class MoodModel : BaseViewModel<Any?>() {
    private val moodRepository by lazy { MoodRepository() }
    private var _moodSelected = MutableLiveData<Mood>()
    private val _moodSaved = MutableLiveData<UiStatus<Boolean>>()
    private var _yearMonth = MutableLiveData<Pair<Int, Int>>()
    private var _yearWeek = MutableLiveData<Int>()
    private val _weekDayMood = MutableLiveData<UiStatus<List<WeekDayMood>>>()
    private val _weekDayMonthMood = MutableLiveData<UiStatus<List<WeekDayMood>>>()
    private val _deleteMoodLog = MutableLiveData<UiStatus<Boolean>>()
    private var _moodCourses = MutableLiveData<UiStatus<List<Course>>>()
    val moodSelected = _moodSelected
    val moodSaved = _moodSaved
    val yearMonth = _yearMonth
    val yearWeek = _yearWeek
    val weekDayMood = _weekDayMood
    val weekDayMonthMood = _weekDayMonthMood
    val deleteMoodLog = _deleteMoodLog
    val moodCourse = _moodCourses

    // 心情记录 - 总天数
    private val _essayDays = MutableLiveData<UiStatus<DayMoodEntity>>()
    val essayDays: LiveData<UiStatus<DayMoodEntity>> = _essayDays

    // 心情记录 - 月 - 详细数据
    private val _detailMonthData = MutableLiveData<UiStatus<List<MoodLog>>>()
    val detailMonthData: LiveData<UiStatus<List<MoodLog>>> = _detailMonthData

    // 编辑保存我的心情记录
    private val _editSaveDiary = MutableLiveData<UiStatus<Boolean>>()
    val editSaveDiary: LiveData<UiStatus<Boolean>> = _editSaveDiary

    init {
        _yearMonth.observeForever {
//            moodRepository.getMoodMonthCalendar(_weekDayMonthMood, it.first, it.second)
            moodRepository.getMonthMood(_weekDayMonthMood, _detailMonthData, it.first, it.second)
        }
    }

    /**
     * 心情表情
     */
    fun getMoodFace() {
        moodRepository.getMoodFace()
    }

    /**
     * 影响心情的标签
     */
    fun getMoodTag() {
        moodRepository.getMoodTag()
    }

    /**
     * 选择表情选项
     */
    fun setSelectMood(mood: Mood) {
        _moodSelected.value = mood
    }

    fun saveSelectedMood(faceId: Int, tagIds: String, content: String) {
        moodRepository.saveMoodContent(_moodSaved, faceId, tagIds, content)
    }

    fun getWeekDayMood() {
        moodRepository.getMoodWeekCalendar(
            _weekDayMood,
            getInstance().get(Calendar.YEAR),
            getInstance().get(Calendar.WEEK_OF_YEAR)
        )
        moodRepository.getWeekMood(
            _weekDayMood,
            getInstance().get(Calendar.YEAR),
            getInstance().get(Calendar.WEEK_OF_YEAR)
        )
    }

    fun getMoodCourse(moodId: Int) {
        moodRepository.getMoodCourse(_moodCourses, moodId)
    }

    fun getWeekOfYear() {
        _yearWeek.value = getInstance().get(Calendar.MONTH) + 1
    }

    /**
     * 获取当前月日历
     */
    fun getWeekDayMonthMood() {
        getInstance().apply {
            get(Calendar.MONTH)
            val year = get(Calendar.YEAR)
            val month = get(Calendar.MONTH) + 1
            _yearMonth.value = Pair(year, month)
        }
    }

    fun deleteMoodLog(moodLogId: Int, contentType: String) {
        moodRepository.deleteMoodLog(_deleteMoodLog, moodLogId, contentType)
    }

    fun getLastWeekDayMonthMood() {
        _yearMonth.value?.let {
            var year = it.first
            var month = it.second
            if (month == 1) {
                year--
                month = month.plus(11)
            } else {
                month--
            }
            _yearMonth.value = Pair(year, month)
        }
    }

    fun getNextWeekDayMonthMood() {
        _yearMonth.value?.let {
            var year = it.first
            var month = it.second
            if (month == 12) {
                year++
                month = month.minus(11)
            } else {
                month++
            }
            _yearMonth.value = Pair(year, month)
        }
    }

    /**
     * 心情记录 - 总天数
     */
    fun getEssayDays() {
        moodRepository.getEssayDays(_essayDays)
    }

    /**
     * 设置当前月份
     *
     * @param pair Pair<Int, Int>
     */
    fun setCurrentDate(pair: Pair<Int, Int>) {
        _yearMonth.value = pair
    }

    /**
     * 编辑保存我的心情记录
     *
     * @param diaryId id
     * @param type 类型 => 日记 ｜ 心情记录
     * @param content 内容
     */
    fun editSaveDiary(diaryId: Int, type: String, content: String) {
        moodRepository.editSaveDiary(_editSaveDiary, diaryId, type, content)
    }

}