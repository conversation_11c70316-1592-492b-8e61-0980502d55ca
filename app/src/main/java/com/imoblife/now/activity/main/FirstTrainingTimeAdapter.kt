package com.imoblife.now.activity.main

import android.annotation.SuppressLint
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.bean.WeekDayEntity
import com.imoblife.now.databinding.LayoutViewFirstTrainingWeekDayBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.ext.onDebounceClickListener

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/3/17
 * 描   述：首训 - 会员计划 - 设置练习提醒 - adapter
 */
class FirstTrainingTimeAdapter(private val mBlockClick: ((value: String) -> Unit)) :
    BaseQuickAdapter<WeekDayEntity, BaseViewHolder>(R.layout.layout_view_first_training_week_day) {

    private val mSelectValue by lazy(LazyThreadSafetyMode.NONE) { mutableListOf<Int>() }

    @SuppressLint("NotifyDataSetChanged")
    override fun convert(helper: BaseViewHolder, item: WeekDayEntity?) {
        helper.getBinding(LayoutViewFirstTrainingWeekDayBinding::bind).apply {
            item?.let {
                stvContent.text = when (it.title) {
                    "周日" -> "S\n\n日"
                    "周一" -> "M\n\n一"
                    "周二" -> "T\n\n二"
                    "周三" -> "W\n\n三"
                    "周四" -> "T\n\n四"
                    "周五" -> "F\n\n五"
                    "周六" -> "S\n\n六"
                    else -> ""
                }
                if (it.isSelected) {
                    stvContent.solid = ContextCompat.getColor(mContext, R.color.color_B4FCF8)
                    stvContent.setTextColor(ContextCompat.getColor(mContext, R.color.color_111111))
                } else {
                    stvContent.solid = ContextCompat.getColor(mContext, R.color.color_transparent)
                    stvContent.setTextColor(ContextCompat.getColor(mContext, R.color.color_333333))
                }
                root.onDebounceClickListener {
                    it.isSelected = !it.isSelected
                    mSelectValue.clear()
                    data.forEach { entity ->
                        mSelectValue.add(if (entity.isSelected) 1 else 0)
                    }
                    mBlockClick.invoke(mSelectValue.joinToString(","))
                    notifyDataSetChanged()
                }
            }
        }
    }

}