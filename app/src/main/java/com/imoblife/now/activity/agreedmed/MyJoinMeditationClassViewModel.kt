package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.MeditationClassJoinEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-14
 * 描   述：我的参与_冥想班_ViewModel
 */
class MyJoinMeditationClassViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { MyJoinMeditationClassRepository() }

    // 我的参与_冥想班
    private val _meditationClassJoinList =
        MutableLiveData<UiStatus<List<MeditationClassJoinEntity>>>()
    val meditationClassJoinList: LiveData<UiStatus<List<MeditationClassJoinEntity>>> =
        _meditationClassJoinList

    /**
     * 获取我参与的冥想班
     */
    fun getJoinMeditationClass() {
        mRepository.getJoinMeditationClass(initPage = true, _meditationClassJoinList)
    }

    /**
     * 获取更多我参与的冥想班
     */
    fun getMoreJoinMeditationClass() {
        mRepository.getJoinMeditationClass(initPage = false, _meditationClassJoinList)
    }

}