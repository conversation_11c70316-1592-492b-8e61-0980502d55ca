package com.imoblife.now.activity.found;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.coorchice.library.SuperTextView;
import com.imoblife.commlibrary.base.BaseEvent;
import com.imoblife.commlibrary.mvp.CreatePresenter;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.activity.member.SubscribeActivity;
import com.imoblife.now.activity.yoga.YogaPayActivity;
import com.imoblife.now.adapter.CourseActiveAdapter;
import com.imoblife.now.adapter.CourseArticleAdapter;
import com.imoblife.now.adapter.CourseJpAdapter;
import com.imoblife.now.adapter.TrainAdapter;
import com.imoblife.now.adapter.decoration.CommonItemDecoration;
import com.imoblife.now.adapter.loading.NavIconType;
import com.imoblife.now.adapter.loading.ToolbarUtils;
import com.imoblife.now.bean.AdResourceBean;
import com.imoblife.now.bean.Course;
import com.imoblife.now.bean.FoundCourse;
import com.imoblife.now.bean.NowArticle;
import com.imoblife.now.constant.ConsClickOrigin;
import com.imoblife.now.constant.ConsCommon;
import com.imoblife.now.constant.ConsEventCode;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.model.ConfigMgr;
import com.imoblife.now.model.UserMgr;
import com.imoblife.now.mvp_contract.FoundDataContract;
import com.imoblife.now.mvp_presenter.FoundDataPresenter;
import com.imoblife.now.util.AdResourceUtils;
import com.imoblife.now.util.DisplayUtil;
import com.imoblife.now.view.EmptyViewUtils;
import com.imoblife.now.viewmodel.AdViewModel;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;

import java.util.ArrayList;
import java.util.List;

@CreatePresenter(presenter = FoundDataPresenter.class)
public class FoundDataActivity extends MvpBaseActivity<FoundDataPresenter> implements FoundDataContract.IFoundDataView {
    private SmartRefreshLayout mRefreshLayout;
    private RecyclerView mRecyclerView;
    private FrameLayout trainOpenVipView;
    //页数
    private int mPageNum = 1;
    //每页数量
    private int mPageCount = 20;
    //精品课适配器
    private CourseJpAdapter courseJpAdapter;
    private List<Course> courses;
    //Now简文适配器
    private CourseArticleAdapter courseArticleAdapter;
    private List<NowArticle> articles;
    //活动适配器
    private CourseActiveAdapter courseActiveAdapter;
    private List<FoundCourse> actives;
    //用户参加的训练营
    private TrainAdapter trainAdapter;
    private List<FoundCourse> trainsData;

    private int fundTypeId;
    private String foundType;
    private String foundTitle;
    private SuperTextView stvOpen;
    private TextView tvContent;

    // 神策数据 - 当前页面id
    private int mPageId;
    // 神策数据 - 当前页面 - 分类id
    private int mCategoryId;

    private AdViewModel mAdViewModel;

    private AdResourceBean mAdResourceBean;

    public static void openFoundDataActivity(Context context, int fundTypeId, String foundType, String foundTitle, int pageId, int categoryId) {
        Intent intent = new Intent(context, FoundDataActivity.class);
        intent.putExtra(ConsIntent.BUNDLE_FOUND_TYPE_ID, fundTypeId);
        intent.putExtra(ConsIntent.BUNDLE_FOUND_TYPE, foundType);
        if (!TextUtils.isEmpty(foundTitle)) {
            intent.putExtra(ConsIntent.BUNDLE_FOUND_TITLE, foundTitle);
        }
        intent.putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId);
        intent.putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId);
        context.startActivity(intent);
    }

    @Override
    protected void superInit(Intent intent) {
        super.superInit(intent);
        if (hasExtra(ConsIntent.BUNDLE_FOUND_TYPE_ID)) {
            fundTypeId = getIntent().getIntExtra(ConsIntent.BUNDLE_FOUND_TYPE_ID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_FOUND_TYPE)) {
            foundType = getIntent().getStringExtra(ConsIntent.BUNDLE_FOUND_TYPE);
        }
        if (hasExtra(ConsIntent.BUNDLE_FOUND_TITLE)) {
            foundTitle = getIntent().getStringExtra(ConsIntent.BUNDLE_FOUND_TITLE);
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
            mPageId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
            mCategoryId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        initJpCourseView();
    }

    @Override
    protected int setContentViewId() {
        return R.layout.activity_found_data;
    }

    @Override
    protected void initView() {
        ToolbarUtils.setToolbar(this, foundTitle, NavIconType.BACK);
        trainOpenVipView = (FrameLayout) findView(R.id.train_top_open_view);
        stvOpen = trainOpenVipView.findViewById(R.id.stvOpen);
        tvContent = trainOpenVipView.findViewById(R.id.tvContent);
        mRefreshLayout = (SmartRefreshLayout) findView(R.id.swipe_layout);
        mRecyclerView = (RecyclerView) findView(R.id.recycler);
        mRefreshLayout.setOnRefreshListener(refreshLayout -> {
            mPageNum = 1;
            FoundDataActivity.this.getData();
            refreshLayout.finishRefresh(2000);
        });
        mRefreshLayout.setOnLoadMoreListener(refreshLayout -> {
            mPageNum++;
            FoundDataActivity.this.getData();
        });
        //拓展课堂
        if (ConsCommon.FOUND_TYPE_TEACHER_COURSE.equals(foundType)) {
            courses = new ArrayList<>();
            courseJpAdapter = new CourseJpAdapter(courses, ConsClickOrigin.COURSE_PAGE_ORIGIN_ALL_COURSE_JP);
            courseJpAdapter.setParentCategoryName(getString(R.string.string_expand_advanced));
            courseJpAdapter.setCategoryName(getString(R.string.string_expand_advanced));
            mRecyclerView.setAdapter(courseJpAdapter);
        } else if (ConsCommon.FOUND_TYPE_ARTICLE.equals(foundType)) {
            articles = new ArrayList<>();
            courseArticleAdapter = new CourseArticleAdapter(articles);
            mRecyclerView.setAdapter(courseArticleAdapter);
        } else if (ConsCommon.FOUND_TYPE_ACTIVE.equals(foundType) || ConsCommon.FOUND_TYPE_TRAIN.equals(foundType)) {
            actives = new ArrayList<>();
            courseActiveAdapter = new CourseActiveAdapter(actives);
            mRecyclerView.setAdapter(courseActiveAdapter);
        } else if (ConsCommon.TYPE_JOIN_ACTIVE.equals(foundType)) {
            actives = new ArrayList<>();
            courseActiveAdapter = new CourseActiveAdapter(actives);
            mRecyclerView.setAdapter(courseActiveAdapter);
            mRefreshLayout.setEnableLoadMore(false);
        } else if (ConsCommon.TYPE_JOIN_TRAIN.equals(foundType)) {
            trainsData = new ArrayList<>();
            trainAdapter = new TrainAdapter(trainsData);
            mRecyclerView.setAdapter(trainAdapter);
            mRecyclerView.addItemDecoration(new CommonItemDecoration(0, DisplayUtil.dip2px(20f), DisplayUtil.dip2px(13f), DisplayUtil.dip2px(20f), DisplayUtil.dip2px(13f), DisplayUtil.dip2px(13f)));
        }
        openVipTopBannerImgView();
    }

    private void openVipTopBannerImgView() {
        if (ConsCommon.FOUND_TYPE_TEACHER_COURSE.equals(foundType)) {
            trainOpenVipView.setVisibility(View.VISIBLE);
            initJpCourseView();
            trainOpenVipView.setOnClickListener(v -> {
                if (ConsCommon.FOUND_TYPE_TEACHER_COURSE.equals(foundType)) {
                    if (mAdResourceBean != null) {
                        AdResourceUtils.adResourceClick(this, mAdResourceBean);
                    } else {
                        YogaPayActivity.startYogaPay(FoundDataActivity.this, 0, ConsCommon.COURSE_TYPE_TEACHER_COURSE);
                    }
                } else {
                    YogaPayActivity.startYogaPay(FoundDataActivity.this, 0, ConsCommon.COURSE_TYPE_YOGA);
                }
            });
        } else if (ConsCommon.FOUND_TYPE_TRAIN.equals(foundType) && !UserMgr.getInstance().isHasNowVip()) {
            trainOpenVipView.setVisibility(View.VISIBLE);
            trainOpenVipView.setOnClickListener(v -> SubscribeActivity.openSubscribeActivity(this, 0, 20, 1));
        } else {
            trainOpenVipView.setVisibility(View.GONE);
        }
    }

    @Override
    protected void initData() {
        getData();
    }

    @Override
    public void teacherDataView(List<Course> courseList) {
        if (mPageNum == 1) {
            setEmptyView(courseJpAdapter, courseList);
            courses.clear();
            courses.addAll(courseList);
        } else {
            if (courseList != null && !courseList.isEmpty()) {
                courses.addAll(courses.size(), courseList);
                mRefreshLayout.finishLoadMore();
            } else {
                mRefreshLayout.finishLoadMoreWithNoMoreData();
            }
        }
        courseJpAdapter.notifyDataSetChanged();
    }


    @Override
    public void activeDataView(List<FoundCourse> nowActives) {
        if (mPageNum == 1) {
            setEmptyView(courseActiveAdapter, nowActives);
            actives.clear();
            actives.addAll(nowActives);
        } else {
            if (nowActives != null && !nowActives.isEmpty()) {
                actives.addAll(actives.size(), nowActives);
                mRefreshLayout.finishLoadMore();
            } else {
                mRefreshLayout.finishLoadMoreWithNoMoreData();
            }
        }
        courseActiveAdapter.notifyDataSetChanged();
    }

    private void initJpCourseView() {
        if (UserMgr.getInstance().isHasExcellentVip()) {
            tvContent.setText(UserMgr.getInstance().getUser().getExcellent().getExpire_notice());
            stvOpen.setText(getString(R.string.install_open_renewal));
        } else if (ConsCommon.FOUND_TYPE_TEACHER_COURSE.equals(foundType)) {
            tvContent.setText(getString(R.string.string_join_the_membership_expand_and_advance_txt));
            stvOpen.setText(getString(R.string.install_open_vip));
        }
    }

    @Override
    public void articleDataView(List<NowArticle> articleList) {
        if (mPageNum == 1) {
            setEmptyView(courseArticleAdapter, articleList);
            articles.clear();
            articles.addAll(articleList);
        } else {
            if (articleList != null && !articleList.isEmpty()) {
                articles.addAll(articles.size(), articleList);
                mRefreshLayout.finishLoadMore();
            } else {
                mRefreshLayout.finishLoadMoreWithNoMoreData();
            }
        }
        courseArticleAdapter.notifyDataSetChanged();
    }

    @Override
    public void joinTrainDataView(List<FoundCourse> trains) {
        if (mPageNum == 1) {
            setEmptyTrainView(trainAdapter, trains);
            trainsData.clear();
            trainsData.addAll(trains);
        } else {
            if (trainsData != null && !trainsData.isEmpty()) {
                trainsData.addAll(trainsData.size(), trains);
                mRefreshLayout.finishLoadMore();
            } else {
                mRefreshLayout.finishLoadMoreWithNoMoreData();
            }
        }
        trainAdapter.notifyDataSetChanged();
    }

    public void getData() {
        if (ConsCommon.FOUND_TYPE_TEACHER_COURSE.equals(foundType)) {
            getPresenter().getTeacherData(fundTypeId, mPageNum, mPageCount, mPageId, mCategoryId);
            // Ad - 触达场景 - 顶部会员卡片条 - 拓展进阶
            mAdViewModel = new ViewModelProvider(this).get(AdViewModel.class);
            mAdViewModel.getCampaignTriggerAds(ConsCommon.POSITION_IN_BREAK_TOP_MEMBER_CARD_BAR_EXPAND_AND_ADVANCE);
        } else if (ConsCommon.FOUND_TYPE_ARTICLE.equals(foundType)) {
            getPresenter().getArticleData(fundTypeId, mPageNum, mPageCount, mPageId, mCategoryId);
        } else if (ConsCommon.FOUND_TYPE_ACTIVE.equals(foundType)
                || ConsCommon.FOUND_TYPE_TRAIN.equals(foundType)) {
            getPresenter().getActiveData(fundTypeId, mPageNum, mPageCount, mPageId, mCategoryId);
        }  else if (ConsCommon.TYPE_JOIN_TRAIN.equals(foundType)) {
            getPresenter().getJoinTrainData(mPageNum, mPageCount, mPageId, mCategoryId);
        }
        // Ad - 触达场景 - 顶部会员卡片条 - 拓展进阶
        mAdViewModel.getCampaignTriggerAds().observe(this, adResourceBeanUiStatus -> {
            if (adResourceBeanUiStatus.isSuccess()) {
                mAdResourceBean = adResourceBeanUiStatus.getSuccessData();
            }
        });
    }

    private void setEmptyView(BaseQuickAdapter adapter, List data) {
        if (data == null || data.size() == 0) {
            adapter.setEmptyView(EmptyViewUtils.getEmptyView(this, v -> getData()));
        }
    }

    private void setEmptyTrainView(BaseQuickAdapter adapter, List data) {
        if (data == null || data.size() == 0) {
            adapter.setEmptyView(EmptyViewUtils.getEmptyTrainView(this, v ->
                    FoundDataActivity.openFoundDataActivity(this, ConfigMgr.getInstance().getConfig().getTrain_category_id(), ConsCommon.FOUND_TYPE_TRAIN, "训练营", mPageId, mCategoryId)
            ));
        }
    }

    @Override
    public void onEventMainThread(BaseEvent event) {
        super.onEventMainThread(event);
        if (event.getEventCode() == ConsEventCode.CHANGE_SUBSCRIBE_EVENT) {
            openVipTopBannerImgView();
        }
    }
}
