package com.imoblife.now.activity.diary

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.DiaryList
import com.imoblife.now.net.*

class DiaryRepository : BaseRepository() {

    fun getDiary(
        diary: Int,
        liveData: MutableLiveData<UiStatus<DiaryList>>
    ) {
        ApiClient.getInstance().createService(ApiService::class.java)
            .getDiary(diary)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<DiaryList>>() {
                override fun onSuccess(response: BaseResult<DiaryList>) {
                    liveData.value = UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.REFRESHSUCCESS)
                }
            })
    }

    fun userActionLog(
        action: String,
        data: String,
        liveData: MutableLiveData<UiStatus<Any>>
    ) {
        ApiClient.getInstance().createService(ApiUserService::class.java)
            .userActionLog(action, data)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<Any>>() {
                override fun onSuccess(response: BaseResult<Any>) {
                    liveData.value = UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.REFRESHSUCCESS)
                }
            })
    }
    fun deleteDiary(groupId:Int,childId:Int,diaryId: Int,
                    liveData: MutableLiveData<UiStatus<Triple<Int,Int,Int>>>
    ) {
        ApiClient.getInstance().createService(ApiUserService::class.java)
            .userDeleteDiary(diaryId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<Any>>() {
                override fun onSuccess(response: BaseResult<Any>) {
                    response?.let {
                        if (it.isSuccess){
                            liveData.value=UiStatus(true,Triple(diaryId,groupId,childId))
                        }else{
                            liveData.value=UiStatus(false,Triple(diaryId,groupId,childId))
                        }
                    }
                }
                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.REFRESHSUCCESS)
                }
            })
    }
    fun editDiary(
        diaryId: Int,content:String,
        liveData: MutableLiveData<UiStatus<Any>>
    ) {
        ApiClient.getInstance().createService(ApiUserService::class.java)
            .userEditDiary(diaryId,content)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<Any>>() {
                override fun onSuccess(response: BaseResult<Any>) {
                    liveData.value = UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }
                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.REFRESHSUCCESS)
                }
            })
    }
}