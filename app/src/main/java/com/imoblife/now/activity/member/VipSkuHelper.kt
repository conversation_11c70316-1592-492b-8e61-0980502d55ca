package com.imoblife.now.activity.member

import android.app.Activity
import android.text.TextUtils
import com.imoblife.now.ActivityStackManager
import com.imoblife.now.activity.WebViewActivity
import com.imoblife.now.activity.main.MainActivity
import com.imoblife.now.activity.member.signing.ObBreathingExercisesActivity
import com.imoblife.now.activity.member.signing.TransformationGoalActivity
import com.imoblife.now.bean.AdBaseSource
import com.imoblife.now.bean.SubSkuInfo
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.view.dialog.PopupHalfAlterImgDialog
import com.imoblife.now.viewmodel.PaymentViewModel
import com.jeremyliao.liveeventbus.LiveEventBus


fun getAdSkuPageData(paymentViewModel: PaymentViewModel, adResourceBean: AdBaseSource) {
    adResourceBean.let {
        paymentViewModel.getAdPageSkuList(
            it.source_type,
            it.source_id,
            it.template_id_429,
            it.countdown
        )
    }
}

/**
 * ob - 挽留弹窗
 *
 * @param paymentViewModel PaymentViewModel
 * @param adResourceBean AdBaseSource?
 * @param skuId skuId
 */
fun getAdSkuPageCancelData(
    paymentViewModel: PaymentViewModel,
    adResourceBean: AdBaseSource?,
    skuId: Int = -1
) {
    adResourceBean?.let {
        if (it.isIs_full_resource) {
            paymentViewModel.getFullAdPageSkuCancelAlert(
                it.source_id,
                it.source_type,
                it.source_id,
                adResourceBean.close_retain_page,
                skuId
            )
        } else {
            paymentViewModel.getAdPageSkuCancelAlert(
                it.source_type,
                it.source_id,
                adResourceBean.close_retain_page,
                skuId
            )
        }
    }
}

/**
 * 停留挽留弹框
 *
 * 「用户在ob大于22s未点击下单则弹出挽留弹窗」
 *
 * @param skuId skuId
 */
fun adSkuPageStayAndRetain(
    paymentViewModel: PaymentViewModel,
    adResourceBean: AdBaseSource,
    skuId: Int = -1,
) {
    if (adResourceBean.isClose_page_alert) {
        adResourceBean.close_retain_page = 2
        getAdSkuPageCancelData(paymentViewModel, adResourceBean, skuId)
    }
}

/**
 * 取消支付挽留弹框
 *
 * @param skuId skuId
 */
fun adSkuPageCancelPay(
    paymentViewModel: PaymentViewModel,
    adResourceBean: AdBaseSource,
    skuId: Int = -1,
) {
    if (adResourceBean.isClose_page_alert) {
        adResourceBean.close_retain_page = 1
        getAdSkuPageCancelData(paymentViewModel, adResourceBean, skuId)
    }
}

/**
 * 关闭按钮挽留弹框
 *
 * @param isSecondOb 是否第二次ob订阅
 * @param skuId skuId
 */
fun adSkuPageClose(
    activity: Activity,
    paymentViewModel: PaymentViewModel,
    adResourceBean: AdBaseSource,
    isCancelAlter: Boolean,
    isSecondOb: Int = 0,
    skuId: Int = -1,
) {
    if (adResourceBean.isClose_page_alert && !isCancelAlter) {
        adResourceBean.close_retain_page = 0
        getAdSkuPageCancelData(paymentViewModel, adResourceBean, skuId)
    } else {
        if (isSecondOb == 1) {
            ObBreathingExercisesActivity.startActivity(activity)
            activity.finish()
            return
        }
        if (adResourceBean.need_second_ob == 1) {
            TransformationGoalActivity.startActivity(activity)
            activity.finish()
        } else {
            if (!ActivityStackManager.getInstance().isOpenActivity(MainActivity::class.java)) {
                MainActivity.openMainActivity(
                    activity,
                    ConfigMgr.getInstance().config.app_default_tab
                )
            }
            activity.finish()
            if (!TextUtils.isEmpty(adResourceBean.vip_program_url)) {
                WebViewActivity
                    .openWebViewActivity(
                        ActivityStackManager.getInstance().currentActivity,
                        adResourceBean.vip_program_url, null
                    )
            }
        }
    }
}

/**
 * 半屏 - sku - 挽留弹窗
 *
 * @param uiStatus entity
 * @param halfAlterDialog dialog
 * @param triggerReason 神策数据 - 商业线订阅页面挽留弹框曝光 - 触发原因
 * @param isFirstShow 神策数据 - 商业线订阅页面挽留弹框曝光 - 是否当次ob的首次曝光
 */
fun showHalfDialog(
    uiStatus: UiStatus<SubSkuInfo>,
    halfAlterDialog: PopupHalfAlterImgDialog,
    triggerReason: String = "",
    isFirstShow: Boolean = false
) {
    if (uiStatus.isSuccess) {
        uiStatus.successData?.let {
            halfAlterDialog.setSkuInfoData(it, triggerReason, isFirstShow)
        }
    }
}

/**
 * 通知商业线广告条和皇冠更换数据
 */
fun notifyVipSkuAdChangeData(adResourceId: Int = 0, action: Int = 1) {
    val pair = Pair<Int, Int>(adResourceId, action)
    LiveEventBus.get(ConsEventCode.CLOSE_BOTTOM_AD_EVENT).post(pair)
}
