package com.imoblife.now.activity.course

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.*

/**
 *    <AUTHOR> <PERSON><PERSON>
 *    e-mail : <EMAIL>
 *    @date   : 5/14/21 6:51 PM
 *    desc   :
 *    version: 1.0
 */
class CourseModel : BaseViewModel<Any?>() {
    private val courseRepository by lazy { CourseRepository() }
    val courseTagLiveData = MutableLiveData<UiStatus<List<CourseTagGroup>>>()
    val courseByTagLiveData = MutableLiveData<UiStatus<List<Course>>>()
    val selectTagIdsLiveData = MutableLiveData<LinkedHashMap<Int, List<Int>>>()
    val hotCourseLiveData = MutableLiveData<UiStatus<List<Course>>>()

    private val _courseDetail= MutableLiveData<UiStatus<CourseDetail>>()
    val courseDetail: LiveData<UiStatus<CourseDetail>> =_courseDetail

    private val _courseTrack= MutableLiveData<UiStatus<List<GroupTrack>>>()
    val courseTrack:LiveData<UiStatus<List<GroupTrack>>> = _courseTrack

    fun getCourseDetail(courseId: Int,pageId: Int, categoryId: Int){
        courseRepository.getCourseDetail(courseId,pageId,categoryId,_courseDetail)
    }
    fun getCourseTrack(courseId: Int,pageId: Int, categoryId: Int){
        courseRepository.getTrackList(courseId,pageId,categoryId,_courseTrack)
    }

    fun getHotCourseList(pageId: Int, categoryId: Int) {
        courseRepository.getHotCourse(hotCourseLiveData, pageId, categoryId)
    }

    fun getCourseTag() {
        courseRepository.getCourseTag(courseTagLiveData)
    }

    fun getCourseByTag(tag: String, page: Int, size: Int, pageId: Int, categoryId: Int) {
        courseRepository.getCourseByTag(tag, page, size, pageId, categoryId, courseByTagLiveData)
    }
}