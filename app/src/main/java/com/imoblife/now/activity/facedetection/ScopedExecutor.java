package com.imoblife.now.activity.facedetection;

import androidx.annotation.NonNull;

import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - ScopedExecutor
 */
public class ScopedExecutor implements Executor {

    private final Executor executor;
    private final AtomicBoolean shutdown = new AtomicBoolean();

    public ScopedExecutor(@NonNull Executor executor) {
        this.executor = executor;
    }

    @Override
    public void execute(@NonNull Runnable command) {
        if (shutdown.get()) {
            return;
        }
        executor.execute(
                () -> {
                    if (shutdown.get()) {
                        return;
                    }
                    command.run();
                });
    }

    public void shutdown() {
        shutdown.set(true);
    }

}
