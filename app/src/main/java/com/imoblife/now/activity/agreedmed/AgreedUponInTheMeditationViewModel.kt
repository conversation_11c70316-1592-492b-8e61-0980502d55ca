package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.MeditationClassListEntity
import com.imoblife.now.bean.MeditationClassShareEntity
import com.imoblife.now.bean.MeditationClassShareParamsEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-11
 * 描   述：约定冥想_ViewModel
 */
class AgreedUponInTheMeditationViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { AgreedUponInTheMeditationRepository() }

    // 冥想班列表
    private val _meditationClassListEntity =
        MutableLiveData<UiStatus<MeditationClassListEntity>>()
    val meditationClassListEntity: LiveData<UiStatus<MeditationClassListEntity>> =
        _meditationClassListEntity

    // 冥想班分享信息
    private val _meditationClassShareEntity =
        MutableLiveData<UiStatus<MeditationClassShareEntity>>()
    val meditationClassShare: LiveData<UiStatus<MeditationClassShareEntity>> =
        _meditationClassShareEntity

    // 分享_创建冥想班成功
    private val _meditationClassShareParamsEntity =
        MutableLiveData<UiStatus<MeditationClassShareParamsEntity>>()
    val meditationClassShareParamsEntity: LiveData<UiStatus<MeditationClassShareParamsEntity>> =
        _meditationClassShareParamsEntity

    /**
     * 获取冥想班列表
     */
    fun getMeditationClassList() {
        mRepository.getMeditationClassList(initPage = true, _meditationClassListEntity)
    }

    /**
     * 获取更多冥想班列表
     */
    fun getMoreMeditationClassList() {
        mRepository.getMeditationClassList(initPage = false, _meditationClassListEntity)
    }

    /**
     * 冥想班分享信息
     *
     * @param teamId 组队id
     */
    fun getMeditationClassShare(teamId: Int) {
        mRepository.getMeditationClassShareInfo(teamId, _meditationClassShareEntity)
    }

    /**
     * 分享_创建冥想班成功
     */
    fun getMeditationClassShareParams(teamId: Int) {
        mRepository.getMeditationClassShareParams(teamId, _meditationClassShareParamsEntity)
    }

}