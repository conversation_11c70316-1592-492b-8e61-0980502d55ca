package com.imoblife.now.activity.memberchallenge

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.MemberChallengeEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/20
 * 描   述：会员挑战 - 挑战未知的自己 - viewModel
 */
class MemberChallengeViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { MemberChallengeRepository() }

    private val _listData = MutableLiveData<UiStatus<MemberChallengeEntity>>()
    val listData: LiveData<UiStatus<MemberChallengeEntity>> = _listData

    private val _joinSignChallenge = MutableLiveData<UiStatus<Boolean>>()
    val joinSignChallenge: LiveData<UiStatus<Boolean>> = _joinSignChallenge

    /**
     * 会员挑战 - 挑战未知的自己 - 列表
     *
     * @param page_id page_id
     * @param category_id category_id
     * @param source source 页面来源 为 vip 时，首页会员挑战赛数据刷新
     */
    fun getChallengeList(page_id: Int = -1, category_id: Int = -1, source: String = "") {
        mRepository.getChallengeList(_listData, page_id, category_id, source)
    }

    /**
     * 会员挑战 - 挑战未知的自己 - 加入挑战赛
     *
     * @param id 挑战赛列表ID
     */
    fun joinSignChallenge(id: Int) {
        mRepository.joinSignChallenge(_joinSignChallenge, id)
    }

}