package com.imoblife.now.activity.category

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.CategoryNavigationEntity
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.bean.Course
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/6
 * 描   述：首页 - 金刚区 - 分类导航 - ViewModel
 */
class CategoryNavigationViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { CategoryNavigationRepository() }

    // 首页 - 金刚区 - 分类导航
    private val _listData = MutableLiveData<UiStatus<CategoryNavigationEntity>>()
    val listData: LiveData<UiStatus<CategoryNavigationEntity>> = _listData

    // 首页 - 金刚区 - 分类导航 - 刷新状态
    private val _refreshState = MutableLiveData<Boolean>()
    val refreshState: LiveData<Boolean> = _refreshState

    // 首页 - 金刚区 - 分类导航 - 分类Item
    private val _listItemData = MutableLiveData<UiStatus<List<CategoryNavigationItemEntity>>>()
    val listItemData: LiveData<UiStatus<List<CategoryNavigationItemEntity>>> = _listItemData

    // 首页 - 推荐 - 此刻TOP5
    private val _listItemCourseData = MutableLiveData<UiStatus<List<Course>>>()
    val listItemCourseData: LiveData<UiStatus<List<Course>>> = _listItemCourseData

    /**
     * 首页 - 金刚区 - 分类导航
     *
     * @param type cat_id
     * @param page_id page_id
     * @param category_id category_id
     */
    fun getCategoryData(type: Int, page_id: Int, category_id: Int, isReadCache: Boolean = true) {
        mRepository.getCategoryData(
            _listData,
            _refreshState,
            type,
            page_id,
            category_id,
            isReadCache
        )
    }

    /**
     * 首页 - 金刚区 - 分类导航 - 分类Item
     *
     * @param type type
     * @param cat_id cat_id
     * @param page_id page_id
     * @param category_id category_id
     */
    private fun getCategoryItemData(type: String, cat_id: Int, page_id: Int, category_id: Int) {
        mRepository.getCategoryItemData(_listItemData, type, cat_id, page_id, category_id)
    }

    /**
     * 首页 - 推荐 - 此刻TOP5
     *
     * @param type type
     * @param cat_id cat_id
     * @param page_id page_id
     * @param category_id category_id
     */
    private fun getItemCourseData(type: String, cat_id: Int, page_id: Int, category_id: Int) {
        mRepository.getItemCourseData(_listItemCourseData, type, cat_id, page_id, category_id)
    }

    /**
     * 首页 - 金刚区 - 分类导航 - 分类Item ｜ 首页 - 推荐 - 此刻TOP5
     *
     * @param type now_hot_practice => 首页 - 推荐 - 正在听 else 首页 - 金刚区 - 分类导航 - 分类Item
     * @param cat_id Int
     * @param page_id Int
     * @param category_id Int
     */
    fun getData(type: String, cat_id: Int, page_id: Int, category_id: Int) {
        if (type == ConsCommon.HOME_RECOMMEND_NOW_HOT_MORE) {
            getItemCourseData(type, cat_id, page_id, category_id)
        } else {
            getCategoryItemData(type, cat_id, page_id, category_id)
        }
    }

}