package com.imoblife.now.activity.monitor

import androidx.appcompat.app.AppCompatActivity
import com.imoblife.now.R
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutViewSetTheWakeUpTimeBinding
import com.imoblife.now.util.SpUtil
import com.imoblife.now.view.dialog.BaseBottomSheetDialog
import com.imoblife.now.view.dialog.OnClickViewListener

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：睡眠闹钟 - 设置起床时间
 */
class BottomSetTheWakeUpTimeDialog : BaseBottomSheetDialog(true) {

    override val viewId = R.layout.layout_view_set_the_wake_up_time

    private lateinit var mBind: LayoutViewSetTheWakeUpTimeBinding

    private var mContent = "30"

    private var mDefaultCursorValue = 0

    private var mOnClickViewListener: OnClickViewListener? = null

    override fun initView() {
        mBind = mBinding as LayoutViewSetTheWakeUpTimeBinding
        mBind.apply {
            // 关闭
            imgClose.setOnClickListener {
                dismiss()
            }
            // 选择器
            numberPicker.apply {
                val data = arrayOf("3", "5", "10", "15", "20", "30")
                minValue = 1
                maxValue = data.size
                displayedValues = data
                mDefaultCursorValue = data.size
                value =
                    SpUtil.getInstance()
                        .getIntValue(ConsCommon.SET_THE_WAKE_UP_TIME_CURSOR, mDefaultCursorValue)
                mContent = SpUtil.getInstance()
                    .getStringValue(ConsCommon.SET_THE_WAKE_UP_TIME_CONTENT, mContent)
                setOnValueChangedListener { _, _, newVal ->
                    mDefaultCursorValue = newVal
                    mContent = data[newVal - 1]
                }
            }
            // 保存
            stvBtn.setOnClickListener {
                SpUtil.getInstance()
                    .saveIntToSp(ConsCommon.SET_THE_WAKE_UP_TIME_CURSOR, mDefaultCursorValue)
                SpUtil.getInstance()
                    .saveStringToSp(ConsCommon.SET_THE_WAKE_UP_TIME_CONTENT, mContent)
                mOnClickViewListener?.onImgConfirmClick(mContent)
                dismiss()
            }
        }
    }

    fun show(context: AppCompatActivity?, mClick: OnClickViewListener) {
        mOnClickViewListener = mClick
        context?.let { show(it.supportFragmentManager, it.javaClass.simpleName) }
    }

}