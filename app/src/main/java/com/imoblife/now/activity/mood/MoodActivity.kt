package com.imoblife.now.activity.mood

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.animation.AnimationUtils
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.MoodAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.Mood
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.databinding.ActivityMoodBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.PaperCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MoodActivity : BaseVMActivity<MoodModel>() {

    private val moodAdapter by lazy { MoodAdapter() }

    companion object {
        @JvmStatic
        fun startMood(context: Context) {
            context.startActivity(Intent(context, MoodActivity::class.java))
        }
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentStatusBar().init()
    }

    private lateinit var mBind: ActivityMoodBinding

    override fun getLayoutResId() = R.layout.activity_mood

    override fun superInit(intent: Intent?) {}

    override fun initVM() =
        ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(MoodModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityMoodBinding
        mBind.apply {
            moodToolbar.apply {
                tvTitle.text = getString(R.string.string_mood_diary_txt)
                leftImg.setOnClickListener { onBackPressed() }
                rightImg.setImageResource(R.mipmap.icon_mood_history)
                rightImg.setOnClickListener {
                    if (UserMgr.getInstance().isLogin) {
                        MoodHistoryActivity.startHistoryMood(this@MoodActivity, null)
                    } else {
                        LoginCenter.getInstance().loginControl(
                            this@MoodActivity,
                            LoginCenter.LoginStyleDialog,
                            ConsRequestCode.SEND_REQUEST_LOGIN_HISTORY_MOOD
                        )
                    }
                }
            }
            recycler.apply {
                addItemDecoration(CommonItemDecoration(24.dp, 24.dp, 30.dp, 0, 30.dp, 0))
                adapter = moodAdapter
            }
        }
    }

    override fun initData() {
        lifecycleScope.launch(Dispatchers.Main) {
            val moodList = withContext(Dispatchers.IO) {
                (PaperCache.read<List<Mood>>(ConsCommon.MOOD_DIARY_EMOJI_JSON))
            }
            mBind.recycler.apply {
                layoutAnimation = AnimationUtils.loadLayoutAnimation(
                    context, R.anim.grid_layout_animation_scale_random
                )
                moodAdapter.setNewData(moodList)
                scheduleLayoutAnimation()
            }
            mViewModel.apply { getMoodFace() }
        }
    }

    override fun startObserve() {}

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == ConsRequestCode.SEND_REQUEST_LOGIN_HISTORY_MOOD) {
                MoodHistoryActivity.startHistoryMood(this@MoodActivity, null)
            }
        }
    }

}