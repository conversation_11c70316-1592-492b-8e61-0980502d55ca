package com.imoblife.now.activity.monitor.sleep

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Looper
import android.view.Gravity
import android.view.View
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.RxHelper
import com.imoblife.now.R
import com.imoblife.now.activity.member.SubscribeActivity
import com.imoblife.now.activity.monitor.*
import com.imoblife.now.activity.monitor.alarm.SetSleepAlarmClockActivity
import com.imoblife.now.activity.monitor.history.AllSleepHistoryActivity
import com.imoblife.now.activity.monitor.report.SleepMonitoringReportActivity
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.constant.ConsUrl
import com.imoblife.now.databinding.ActivitySleepMonitorBinding
import com.imoblife.now.databinding.LayoutSleepOverTipsBinding
import com.imoblife.now.ext.minute
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.player.PlayCenter
import com.imoblife.now.player.PlayModeUtils
import com.imoblife.now.sleep.SleepMonitorCenter
import com.imoblife.now.sleep.SleepMonitorService
import com.imoblife.now.sleep.SleepRecord
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.*
import com.imoblife.now.view.dialog.TurnOnMicrophonePermissionsDialog
import com.imoblife.now.viewmodel.UserViewModel
import com.jaychang.st.SimpleText
import com.now.audioplayer.SongInfo
import com.now.audioplayer.manager.PlaybackStage
import com.qw.soul.permission.SoulPermission
import com.qw.soul.permission.bean.Permission
import com.qw.soul.permission.callbcak.CheckRequestPermissionListener
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import per.goweii.anylayer.AnyLayer
import per.goweii.anylayer.dialog.DialogLayer
import per.goweii.anylayer.ktx.setAlign
import per.goweii.anylayer.ktx.setAnimStyle
import per.goweii.anylayer.ktx.setContentView
import per.goweii.anylayer.ktx.setGravity
import per.goweii.anylayer.popup.PopupLayer

class SleepMonitorActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object{
        @JvmStatic
        fun startSleepMonitor(context: Context){
            Intent(context,SleepMonitorActivity::class.java).run {
                context.startActivity(this)
            }
        }
    }

    private val userViewModel by viewModels<UserViewModel>()
    private var timerDisposable: Disposable? =null
    private lateinit var mBind: ActivitySleepMonitorBinding
    private var isObtainAudioPermission=false
    //是否打开了闹钟
    private var isOpenAlarmClock=false
    //当次睡眠记录
    private var mSleepRecord:SleepRecord?=null
    //当次睡眠记录小于4小时没有报告提醒
    private var sleepRecordLayer:PopupLayer?=null

    private var mWhileJob: Job? = null

    override fun getLayoutResId()= R.layout.activity_sleep_monitor
    override fun superInit(intent: Intent?) {}

    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentStatusBar().init()
    }
    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        isOpenAlarmClock=SpUtil.getInstance().getBoolenValue(ConsCommon.SLEEP_ALARM_CLOCK_CHECK_STATE,true)
        mBind=mBinding as ActivitySleepMonitorBinding
        mBind.apply {
            clickProxy=ClickProxy()
            ImageLoader.loadImageLocal(this@SleepMonitorActivity,R.mipmap.icon_start_sleep_monitor_gif,startSleepMonitorImg)
            numberPickerStart.value = SpUtil.getInstance().getIntValue(ConsSp.SP_KEY_SET_START_SLEEP_HOUR,7)
            numberPickerEnd.value = SpUtil.getInstance().getIntValue(ConsSp.SP_KEY_SET_START_SLEEP_MINUTES,30)
            expectedSleepTime(numberPickerStart.value,numberPickerEnd.value)
            numberPickerStart.setOnValueChangedListener { _, _, hour -> expectedSleepTime(hour,numberPickerEnd.value) }
            numberPickerEnd.setOnValueChangedListener { _, _, minute -> expectedSleepTime(numberPickerStart.value,minute) }
            audioPermissionsTxt.text= SimpleText.from("${getString(R.string.string_audio_lack_permissions)}${getString(R.string.string_audio_permissions_setting)}")
                .first(getString(R.string.string_audio_permissions_setting))
                .textColor(R.color.main_color)
                .onClick(audioPermissionsTxt) { _, _,_ ->
                    SoulPermission.getInstance().goApplicationSettings {
                        goPermissionSettingBack()
                    }
                }
            scrollView.setScrollPullListener {
                stopSleepMonitor()
                true
            }
            lifecycle.addObserver(voiceVolumeView)
        }
        SleepMonitorCenter.instance.sleepMonitorListener=soundMeter
        sleepContextView(SleepMonitorCenter.instance.isStartMonitoring)
    }
    //计算还能睡多长时间
    private fun expectedSleepTime(hour: Int,minutes:Int) {
        SpUtil.getInstance().saveIntToSp(ConsSp.SP_KEY_SET_START_SLEEP_HOUR,hour)
        SpUtil.getInstance().saveIntToSp(ConsSp.SP_KEY_SET_START_SLEEP_MINUTES,minutes)
        if (isOpenAlarmClock){
            val pair = TimeUtils.compareTimeDifference(hour, minutes)
            mBind.sleepExpectTimeTxt.text = "我们将在${pair.first.minute}小时${pair.second.minute}分钟后唤醒你"
            mBind.sleepNumberTime.visibility=View.VISIBLE
        }else{
            mBind.sleepExpectTimeTxt.text = "闹钟已关闭\n仅睡眠分析"
            mBind.sleepNumberTime.visibility=View.INVISIBLE
        }

    }
    private fun sleepContextView(isRunning: Boolean) {
        if (isRunning) {
            mBind.startSleepContentView.visibility = View.GONE
            mBind.sleepingContentView.visibility = View.VISIBLE
            mBind.iconSleepBack.visibility=View.GONE
            if (PermissionsUtils.lacksPermission(this,PermissionsUtils.permissionAudio)){
                mBind.audioPermissionsTxt.visibility=View.VISIBLE
                mBind.sleepDecibelGroup.visibility=View.GONE
            }else{
                mBind.audioPermissionsTxt.visibility=View.GONE
                mBind.sleepDecibelGroup.visibility=View.VISIBLE
            }
            showSleepTimer()
        } else {
            mBind.startSleepContentView.visibility = View.VISIBLE
            mBind.sleepingContentView.visibility = View.GONE
            mBind.iconSleepBack.visibility=View.VISIBLE
            mBind.audioPermissionsTxt.visibility=View.GONE
        }
    }

    private val soundMeter=object :SleepMonitorCenter.SleepMonitorListener{
        @SuppressLint("SetTextI18n")
        override fun sleepDbChange(db: Int) {
            lifecycleScope.launchWhenResumed {
                mBind.voiceVolumeView.setVolume(db)
                if (db>50){
                    mBind.decibelValueTxt.text="当前环境噪音${db}分贝 可能会影响睡眠质量"
                }else{
                    mBind.decibelValueTxt.text="当前环境噪音${db}分贝"
                }
            }
        }
        override fun sleepMonitorRunning(isRunning: Boolean) {
            sleepContextView(isRunning)
        }
        override fun sleepStart() {
            mBind.voiceVolumeView.start()
            val sleepHour=mBind.numberPickerStart.value
            val sleepMinute=mBind.numberPickerEnd.value
            setTimerAlarm(sleepHour, sleepMinute)
        }
        override fun sleepStop(sleepRecord: SleepRecord) {
            mBind.voiceVolumeView.stop()
            hideSleepTimer()
            mViewModel.uploadSleepMonitorData()
            mSleepRecord=sleepRecord
            mWhileJob?.cancel()
            RingtoneManagerCenter.stop()
        }
    }

    /**
     * 设置闹铃时间
     */
    private fun setTimerAlarm(sleepHour: Int, sleepMinute: Int) {
        SpUtil.getInstance().saveIntToSp(ConsCommon.SLEEP_ALARM_CLOCK_HOUR, sleepHour)
        SpUtil.getInstance().saveIntToSp(ConsCommon.SLEEP_ALARM_CLOCK_MINUTE, sleepMinute)
        displayAlarmTimer()
    }

    private fun hideSleepTimer() {
        timerDisposable?.let { if (!it.isDisposed) { it.dispose() } }
    }

    private fun showSleepTimer() {
        timerDisposable = RxHelper.interval(1).subscribe {
            mBind.currentTimeTxt.text =
                "${TimeUtils.getCurrentTimeHourAndMinutes().first.minute}:${TimeUtils.getCurrentTimeHourAndMinutes().second.minute}"
        }
        displayAlarmTimer()
    }
    private fun displayAlarmTimer() {
        if (isOpenAlarmClock){
            mBind.alarmTimeTxt.visibility=View.VISIBLE
            val first="闹钟"
            val hour=SpUtil.getInstance().getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_HOUR,7)
            val minutes=SpUtil.getInstance().getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_MINUTE, 50)
            val startWakeup=TimeUtils.reduceTime(hour,minutes,SpUtil.getInstance().getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_MINUTE,30))
            val startWakeUpTime="${startWakeup.first.minute}:${startWakeup.second.minute}"
            val endWakeUpTime="${hour.minute}:${minutes.minute}"
            val second="${startWakeUpTime}-${endWakeUpTime}"
            mBind.alarmTimeTxt.text="${first}：${second}"
            mBind.alarmTimeTxt.setOnClickListener {
                BottomChangeAlarmClockDialog().setOnSleepTimerListener(object :BottomChangeAlarmClockDialog.SleepTimerListener{
                    override fun onSetTimer(hour: Int, minute: Int) {
                        setTimerAlarm(hour,minute)
                    }
                }).show(this@SleepMonitorActivity)
            }
            startAlarmClock(hour, minutes)
        }else{
            mBind.alarmTimeTxt.visibility=View.GONE
        }
    }

    override fun initData() {
        userViewModel.getUserVipStatus()
        Looper.getMainLooper().queue.addIdleHandler {
            val proxyUrl = AudioCacheUtils.getInstance().cacheServer.getProxyUrl(ConsUrl.SLEEP_DEFAULT_BACKGROUND_AUDIO)
            var songInfo=SongInfo()
            songInfo.songId="0"
            songInfo.albumId="0"
            songInfo.songUrl=proxyUrl
            songInfo.songName="深度放松·沉浸式大自然钢琴曲"
            songInfo.songType="sound_background"
            songInfo.albumCover=ConsUrl.SLEEP_DEFAULT_BACKGROUND_IMG
            PlayCenter.getInstance().playSongInfo(songInfo)
            PlayModeUtils.setPlayModeCycleOne()
            PlayCenter.getInstance().playerControl.stopByTimedOff(30*60*1000,
                isPause = true,
                isFinishCurrSong = false
            )
            false
        }
    }

    override fun startObserve() {
        userViewModel.userVipStatusChange.observe(this) {
            mBind.vipOpenView.visibility=if(it.successData == true){View.GONE}else{View.VISIBLE}
        }
        mViewModel.uploadSleepMonitorLiveData.observe(this) { uiStatus ->
            if (uiStatus.isSuccess) {
                if (!uiStatus.successData.isNullOrEmpty()) {
                    run out@{
                        uiStatus.successData?.forEach { entity ->
                            if (entity.isStatus) {
                                if (entity.isIs_new) {
                                    SleepMonitorCompleteSkuActivity.startActivity(this)
                                } else {
                                    SleepMonitoringReportActivity.startActivity(this)
                                }
                                EventBus.getDefault()
                                    .post(BaseEvent(ConsEventCode.SLEEP_REFRESH_MAIN_REPORT_UI_DATA))
                                return@out
                            }
                        }
                        mSleepRecord?.let { sleepOverViewOverlay(it) }
                    }
                }
            }
        }
        PlayCenter.getInstance().playerControl?.playbackState()?.observe(this) {
            it.songInfo?.apply {
                if (isSoundBackgroundAudio()){
                    mBind.sleepAudioText.text=getString(R.string.string_start_sleep_audio)
                }else{
                    mBind.sleepAudioText.text=songName
                    if (!isNature()){
                        PlayCenter.getInstance().playerControl.onStopCountdownTimer()
                    }
                }
            }
            when(it.stage){
                PlaybackStage.PLAYING->{
                    mBind.sleepMusicPlay.setImageResource(R.mipmap.icon_sleep_monitor_playing)
                }
                PlaybackStage.IDEA,
                PlaybackStage.ERROR,
                PlaybackStage.ENDED,
                PlaybackStage.PAUSE->{
                    mBind.sleepMusicPlay.setImageResource(R.mipmap.icon_sleep_monitor_pause)
                }
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        SleepMonitorCenter.instance.clearSleepMonitorListener()
        hideSleepTimer()
        PlayCenter.getInstance().stop()
        ConfigMgr.getInstance().isFromSleepPage=false
        PlayCenter.getInstance().playerControl.onStopCountdownTimer()
        sleepRecordLayer?.dismiss()
        mWhileJob?.cancel()
        RingtoneManagerCenter.stop()
    }
    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                R.id.icon_sleep_back->{
                    onBackPressed()
                }
                R.id.start_sleep_monitor_img,
                R.id.start_sleep_monitor_txt->{
                    if (UserMgr.getInstance().isLogin){
                        clickStartSleepMonitor()
                    }else{
                        LoginCenter.getInstance().loginControl(this@SleepMonitorActivity)
                    }
                    SensorsDataEvent.sleepMonitorPageClick("开始睡眠")
                }
                R.id.start_sleep_alarm_txt->{
                    SetSleepAlarmClockActivity.startActivity(this@SleepMonitorActivity)
                    SensorsDataEvent.sleepMonitorPageClick("闹钟")
                }
                R.id.sleep_music_select,
                R.id.sleep_audio_text->{
                    SleepAssistActivity.startSleepAssist(this@SleepMonitorActivity)
                    SensorsDataEvent.sleepMonitorPageClick("开启助眠音")
                }
                R.id.sleep_music_play->{
                    PlayCenter.getInstance().controlPlayOrPause()
                }
                R.id.start_sleep_record_txt->{
                    AllSleepHistoryActivity.startActivity(this@SleepMonitorActivity)
                    SensorsDataEvent.sleepMonitorPageClick("睡眠记录")
                }
                R.id.vip_open_view->{
                    SubscribeActivity.openSubscribeActivity(this@SleepMonitorActivity,pageOrigin = 5)
                    SensorsDataEvent.sleepMonitorPageClick("开通会员")
                }
            }
        }
    }

    private fun goPermissionSettingBack() {
        if (isObtainAudioPermission){
            return
        }
        if (!PermissionsUtils.lacksPermission(this,PermissionsUtils.permissionAudio)){
            //重新获取了录音权限
            isObtainAudioPermission=true
            mBind.audioPermissionsTxt.visibility=View.GONE
            mBind.sleepDecibelGroup.visibility=View.VISIBLE
            regainSleepMonitor()
        }
    }

    override fun onBackPressed() {
        if (mBind.iconSleepBack.isVisible){
            finish()
        }
    }
    private fun clickStartSleepMonitor() {
        TurnOnMicrophonePermissionsDialog(this)
            .setOnTurnOnAudioPermissionListener(object :TurnOnMicrophonePermissionsDialog.TurnOnAudioPermissionListener{
                override fun isOpenPermission(isOpenPermission: Boolean,isNeedApplyPermission:Boolean) {
                    if (isOpenPermission){
                        isObtainAudioPermission=true
                        startSleepMonitor()
                    }else{
                        if (isNeedApplyPermission){
                            SoulPermission.getInstance().checkAndRequestPermission(
                                PermissionsUtils.permissionAudio,
                                object : CheckRequestPermissionListener {
                                    override fun onPermissionOk(permission: Permission) {
                                        isObtainAudioPermission=true
                                        startSleepMonitor()
                                    }
                                    override fun onPermissionDenied(permission: Permission) {
                                        isObtainAudioPermission=false
                                        startSleepMonitor()
                                    }
                                })
                        }else{
                            isObtainAudioPermission=false
                            startSleepMonitor()
                        }
                    }
                }
            })
            .showDialog()
    }
    private fun startSleepMonitor() {
        BottomSleepMonitoringTipsDialog(this).show()
        Intent(this@SleepMonitorActivity, SleepMonitorService::class.java).apply {
            action = SleepMonitorService.Action.START.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                <EMAIL>(this)
                return
            }
            <EMAIL>(this)
        }
    }
    private fun regainSleepMonitor() {
        Intent(this@SleepMonitorActivity, SleepMonitorService::class.java).apply {
            action = SleepMonitorService.Action.REGAIN_PERMISSION.name
            if (SleepMonitorCenter.instance.isStartMonitoring){
                <EMAIL>(this)
            }else{
                startSleepMonitor()
            }
        }
    }
    private fun stopSleepMonitor() {
        Intent(this@SleepMonitorActivity, SleepMonitorService::class.java).apply {
            action = SleepMonitorService.Action.STOP.name
            <EMAIL>(this)
        }
        PlayCenter.getInstance().pause()
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        event?.apply {
            when(eventCode){
                ConsEventCode.CHANGE_SUBSCRIBE_EVENT->{
                    userViewModel.getUserVipStatus()
                }
                ConsEventCode.SLEEP_ALARM_CLOCK_CHANGE->{
                    val alarmClockStatus=this.getResult<Boolean>()
                    if (isOpenAlarmClock!=alarmClockStatus){
                        isOpenAlarmClock=alarmClockStatus
                        displayAlarmTimer()
                        expectedSleepTime(mBind.numberPickerStart.value,mBind.numberPickerEnd.value)
                    }
                }
            }
        }
    }
    private fun sleepOverViewOverlay(sleepRecord: SleepRecord) {
        val sleepTime=sleepRecord.wake_up_time - sleepRecord.sleep_time
        if (sleepTime<0 || sleepTime>4*60*60*1000){
            return
        }
        sleepRecordLayer?.dismiss(false)
        val mBindJoinView: LayoutSleepOverTipsBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.layout_sleep_over_tips,
            ConstraintLayout(this).also {
                it.layoutParams = ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                )
            },
            true
        )
        val days: Long = sleepTime / (1000 * 60 * 60 * 24)
        val hours: Long = sleepTime % (1000 * 60 * 60 * 24) / (1000 * 60 * 60)
        var minutes: Long = sleepTime % (1000 * 60 * 60) / (1000 * 60)
        val seconds: Long = sleepTime % (1000 * 60) / 1000
        if (hours>0){
            mBindJoinView.tvTitle.text="睡眠时长${hours}小时${minutes}分钟"
        }else if (minutes<1) {
            mBindJoinView.tvTitle.text="睡眠时长小于1分钟"
        }else{
            mBindJoinView.tvTitle.text="睡眠时长${minutes}分钟"
        }
        sleepRecordLayer = AnyLayer.popup(mBind.topDividerLine)
            .setAlign(
                PopupLayer.Align.Direction.VERTICAL,
                PopupLayer.Align.Horizontal.CENTER,
                PopupLayer.Align.Vertical.BELOW,
                true
            )
            .setContentView(mBindJoinView.root)
            .setGravity(Gravity.CENTER)
            .setAnimStyle(DialogLayer.AnimStyle.TOP)
        sleepRecordLayer?.show()
        lifecycleScope.launch {
            delay(3600)
            sleepRecordLayer?.dismiss()
        }
    }

    /**
     * 启动闹钟
     *
     * @param sleepHour 时
     * @param sleepMinute 分
     */
    private fun startAlarmClock(sleepHour: Int, sleepMinute: Int) {
        mWhileJob?.cancel()
        // 闹钟 & 轻唤醒场景音 启动
        mWhileJob = lifecycleScope.launch(Dispatchers.IO) {
            var firstLightRing = true
            var firstRing = true
            // 闹钟铃声｜轻唤醒场景音 - 某一音频
            val ringingTone = SpUtil.getInstance().getStringValue(
                ConsCommon.SLEEP_ALARM_CLOCK_RINGING_TONE,
                ConsCommon.SLEEP_ALARM_CLOCK_RINGING_TONE_URL
            )
            val sceneSound = SpUtil.getInstance().getStringValue(
                ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING,
                ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_URL
            )
            val pairTime = SettingAlarmClockUtil.setAlarmClock(sleepHour, sleepMinute)
            while (true) {
                delay(1_000)
                // 轻唤醒
                if (firstLightRing && System.currentTimeMillis() >= pairTime.second) {
                    firstLightRing = false
                    RingtoneManagerCenter.start(sceneSound, isLooper = false)
                    withContext(Dispatchers.Main) { PlayCenter.getInstance().pause() }
                }
                // 闹钟
                if (firstRing && System.currentTimeMillis() >= pairTime.first) {
                    firstRing = false
                    RingtoneManagerCenter.start(ringingTone, isLooper = true)
                    withContext(Dispatchers.Main) { PlayCenter.getInstance().pause() }
                    mWhileJob?.cancel()
                }
            }
        }
    }

}


