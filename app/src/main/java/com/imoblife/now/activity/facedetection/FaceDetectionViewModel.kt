package com.imoblife.now.activity.facedetection

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.FaceDetectionEntity
import com.imoblife.now.bean.FaceDetectionLimitEntity
import com.imoblife.now.bean.FaceDetectionReportEntity
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - ViewModel
 */
class FaceDetectionViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { FaceDetectionRepository() }

    // 上报 reportDeepFace - 图片
    private val _reportDeepFace = MutableLiveData<UiStatus<FaceDetectionEntity>>()
    val reportDeepFace: LiveData<UiStatus<FaceDetectionEntity>> = _reportDeepFace

    // 人脸检测 - 报告
    private val _reportDetail = MutableLiveData<UiStatus<FaceDetectionReportEntity>>()
    val reportDetail: LiveData<UiStatus<FaceDetectionReportEntity>> = _reportDetail

    // 人脸检测 - 限制
    private val _checkDFRight = MutableLiveData<UiStatus<FaceDetectionLimitEntity>>()
    val checkDFRight: LiveData<UiStatus<FaceDetectionLimitEntity>> = _checkDFRight

    /**
     * 人脸检测 - 限制
     */
    fun checkDFRight() {
        mRepository.checkDFRight(_checkDFRight)
    }

    /**
     * 上报 reportDeepFace - 图片
     *
     * @param img 原始图片
     * @param realImg 关键点图片
     */
    fun reportDeepFace(img: String, realImg: String) {
        mRepository.reportDeepFace(img, realImg, _reportDeepFace)
    }

    /**
     * 人脸检测 - 报告
     *
     * @param reportId 报告id
     */
    fun getReportDetail(reportId: String) {
        mRepository.getReportDetail(reportId, _reportDetail)
    }

}