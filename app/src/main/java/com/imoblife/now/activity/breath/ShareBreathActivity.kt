package com.imoblife.now.activity.breath

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Handler
import android.view.View
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import cn.sharesdk.douyin.Douyin
import cn.sharesdk.framework.ShareSDK
import cn.sharesdk.littleredbook.Littleredbook
import cn.sharesdk.sina.weibo.SinaWeibo
import cn.sharesdk.tencent.qq.QQ
import cn.sharesdk.wechat.friends.Wechat
import cn.sharesdk.wechat.moments.WechatMoments
import cn.sharesdk.wework.Wework
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.MyApplication
import com.imoblife.now.R
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsUrl
import com.imoblife.now.databinding.ActivityShareBreathBinding
import com.imoblife.now.databinding.LayoutShareBreathViewBinding
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseActivity
import com.imoblife.now.share.ShareCenter
import com.imoblife.now.share.ShareParamsFactory
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.BitmapUtil
import com.imoblife.now.util.DateUtil
import com.imoblife.now.util.DeviceUtil
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.viewmodel.AdViewModel
import com.yzq.zxinglibrary.encode.CodeCreator

/**
 * 呼吸分享
 */
class ShareBreathActivity : BaseActivity(), View.OnClickListener {

    private val mAdViewModel by viewModels<AdViewModel>()

    private lateinit var mBind: ActivityShareBreathBinding
    private lateinit var mBindShareView: LayoutShareBreathViewBinding

    override fun getLayoutResId(): Int = 0

    override fun initView() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_share_breath)
        mBindShareView =
            DataBindingUtil.inflate(layoutInflater, R.layout.layout_share_breath_view, null, false)
        mBind.apply {
            lifecycleOwner = this@ShareBreathActivity
            ivClose.setOnClickListener(this@ShareBreathActivity)
            layoutSettingPracticeTime.setOnClickListener(this@ShareBreathActivity)

            shareChannelView.apply {
                shareWechatTxt.setOnClickListener(this@ShareBreathActivity)
                shareWechatMomentsTxt.setOnClickListener(this@ShareBreathActivity)
                shareWeiboTxt.setOnClickListener(this@ShareBreathActivity)
//                shareQqTxt.setOnClickListener(this@ShareBreathActivity)
                // 2021/08/02 Yunyang 分享_增加企业微信
//                shareEnterpriseWeChat.setOnClickListener(this@ShareBreathActivity)
                // 2021/08/04 Yunyang 隐藏 保存图片、复制链接
                shareDownloadTxt.visibility = View.GONE
//                shareCopyLinkTxt.visibility = View.GONE

                tvShareLittleredbook.setOnClickListener(this@ShareBreathActivity)
//                tvShareDouYin.setOnClickListener(this@ShareBreathActivity)

//                if (ConfigMgr.getInstance().getConfig().isTiktok_share_switch) {
//                    tvShareDouYin.visibility = View.VISIBLE
//                } else {
//                    tvShareDouYin.visibility = View.GONE
//                }
            }
        }
    }

    override fun initData() {
        val practiceCount = intent.getIntExtra("all_number", 1)
        mBind.apply {
            tvCurrentDate.text = DateUtil.getDateString(false)
            tvCurrentWeek.text = DateUtil.getCurrentWeek()
            tvBreathCount.text = getString(R.string.string_frequency, practiceCount)
            ImageLoader.loadImageLocal(
                this@ShareBreathActivity,
                R.mipmap.breath_share_bg,
                R.mipmap.breath_share_bg,
                ivBg
            )
        }

//        shareView.tv_current_date.text = DateUtil.getDateString(false)
//        shareView.tv_current_week.text = DateUtil.getCurrentWeek()
//        shareView.tv_breath_count_title.text =getString(R.string.breath_practice)
//        shareView.tv_breath_count.text ="${UserMgr.getInstance().userNickname}在Now冥想的第${practiceCount}次呼吸练习"
        mBindShareView.apply {
            lifecycleOwner = this@ShareBreathActivity
            UserMgr.getInstance().user?.let {
                tvCurrentData.text = getString(
                    R.string.string_number_of_breathing_exercises,
                    DateUtil.getDateMDString(),
                    practiceCount
                )
                ImageLoader.loadImageUrl(this@ShareBreathActivity, it.avatar, userHeadImg)
                tvUserName.text = it.nickname
            }
            tvSlogan.text = getString(R.string.breath_slogan)
            val logo = BitmapFactory.decodeResource(resources, R.mipmap.ic_launcher)
            val bp =
                CodeCreator.createQRCode(
                    ConsUrl.getShareUrl(ConfigMgr.getInstance().config.breathing_share_uri),
                    300,
                    300,
                    logo
                )
            shareUrlImg.setImageBitmap(bp)
            BitmapUtil.layoutView(
                root,
                DeviceUtil.getScreenSize(this@ShareBreathActivity)[0],
                DeviceUtil.getScreenSize(this@ShareBreathActivity)[1]
            )
            ImageLoader.loadImageLocal(
                this@ShareBreathActivity,
                R.mipmap.breath_share_bg,
                R.mipmap.breath_share_bg,
                ivShareBg
            )
        }

        mAdViewModel.apply {
            getCampaignTriggerAds(ConsCommon.POSITION_IN_BREAK_BREATHE)
            campaignTriggerAds.observe(this@ShareBreathActivity) {
                if (it.isSuccess) {
                    mBind.shareBreathAd.setAdBannerData(it.successData)
                }
            }
        }
    }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .titleBar(R.id.toolbar)
            .init()
    }

    /**
     * 调整分享渠道时，需同步调整
     * todo：调整分享渠道时，需同步调整
     *
     * 公共分享页面 - ShareActivity.java
     * 计时练习 - 分享 - BreathTimingShareActivity.kt
     * 呼吸 - 分享 - ShareBreathActivity.kt
     */
    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.layout_setting_practice_time -> {
                Intent(this, BreathSettingPracticeTimeActivity::class.java).apply {
                    startActivity(this)
                }
            }

            R.id.iv_close -> finish()
            // 微信
            R.id.share_wechat_txt -> {
                SensorsDataEvent.clickShareComponentsChannel("微信")
                doShare(Wechat.NAME)
            }
            // 微信朋友圈
            R.id.share_wechat_moments_txt -> {
                SensorsDataEvent.clickShareComponentsChannel("微信朋友圈")
                doShare(WechatMoments.NAME)
            }
            // 微博
            R.id.share_weibo_txt -> {
                SensorsDataEvent.clickShareComponentsChannel("微博")
                doShare(SinaWeibo.NAME)
            }
            // QQ
            R.id.share_qq_txt -> {
                SensorsDataEvent.clickShareComponentsChannel("QQ")
                doShare(QQ.NAME)
            }
            // 企业微信
            R.id.share_enterprise_we_chat -> {
                SensorsDataEvent.clickShareComponentsChannel("企业微信")
                doShare(Wework.NAME)
            }
            // 小红书
            R.id.tvShareLittleredbook -> {
                SensorsDataEvent.clickShareComponentsChannel("小红书")
                doShare(Littleredbook.NAME)
            }
            // 抖音
            R.id.tvShareDouYin -> {
                SensorsDataEvent.clickShareComponentsChannel("抖音")
                doShare(Douyin.NAME)
            }

            else -> {}
        }
    }

    private fun doShare(platform: String) {
        Handler().post {
            val bitmap = BitmapUtil.loadBitmapFromView(mBindShareView.root)
            doShare(platform, bitmap)
        }

    }

    private fun doShare(platform: String, shareBitmap: Bitmap) {
        val shareCenter = ShareCenter()
        val shareParams = ShareParamsFactory.getShareBitmapParams(platform, shareBitmap)
        shareCenter.authorize(
            MyApplication.getInstance(),
            ShareSDK.getPlatform(platform),
            shareParams,
            false
        )
    }

}