package com.imoblife.now.activity.activities

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.bean.AdResourceBean
import com.imoblife.now.databinding.LayoutMyActiveCenterAdBinding
import com.imoblife.now.ext.getBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/3/21
 * 描   述：我的活动 - fm - 进行中 - 中部横条
 */
class MyActiveCenterAdAdapter : BaseQuickAdapter<AdResourceBean, BaseViewHolder>(R.layout.layout_my_active_center_ad) {

    override fun convert(helper: BaseViewHolder, item: AdResourceBean?) {
        helper.getBinding(LayoutMyActiveCenterAdBinding::bind).apply {
            adCenterView.setAdBannerData(item)
        }
    }

}