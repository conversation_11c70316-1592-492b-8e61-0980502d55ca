<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>网络中断了，请检查网络设置~</title>
<style>
* {
	-webkit-touch-callout: none;
	/*-webkit-user-select: none;*/
	-webkit-tap-highlight-color: transparent;
}
html {
	font: normal 14px / 20px "Helvetica Neue", Helvetica, Arial, sans-serif;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	background-color: #F1F1F1;
	height: 100%;
}
body {
	margin: 0;
	line-height: 1.6;
	height: 100%;
}
audio, canvas, iframe, img, svg, video {
	vertical-align: middle;
}
audio, canvas, img, svg, video {
	max-width: 100%;
	height: auto;
	box-sizing: border-box;
}
.vcenter {
	width: 100%;
	display: table;
	height: 100%;
	text-align: center;
	color: #B9B9B9;
}
.vcenter .wrap {
	display: table-cell;
	vertical-align: middle;
	padding-bottom: 35px;
}
.icon svg {
	width: 60px;
	fill: #B9B9B9;
	margin-bottom: 15px;
}
</style>
</head>
 
 <script type="text/javascript">
 	function onRefresh(){
 	android.onRefresh();
 	}
 </script>
<body>
	
<div class="vcenter">
	<div class="wrap">
		<div class="icon">
			<?xml version="1.0" standalone="no"?>
			<!DOCTYPE PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
			<svg t="1536226878982" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1362" >
				<defs>
					<style type="text/css">
</style>
				</defs>
				<path d="M515.746341 835.434146c-34.965854 0-62.439024 27.473171-62.439024 62.439025 0 33.717073 27.473171 61.190244 62.439024 61.190244s62.439024-27.473171 62.439025-61.190244c0-34.965854-28.721951-62.439024-62.439025-62.439025zM128.62439 233.521951c-39.960976 23.726829-78.673171 51.2-114.887805 82.419512-16.234146 13.736585-17.482927 38.712195-3.746341 54.946342 13.736585 16.234146 38.712195 17.482927 54.946341 3.746341 37.463415-32.468293 77.42439-59.941463 119.882927-83.668292l-56.195122-57.443903zM1010.263415 315.941463C871.64878 196.058537 695.570732 129.873171 512 129.873171c-78.673171 0-157.346341 12.487805-231.02439 36.214634l62.439024 63.687805c54.946341-13.736585 112.390244-21.229268 169.834146-21.229269 164.839024 0 323.434146 58.692683 447.063415 166.087805 7.492683 6.243902 16.234146 9.990244 24.97561 9.990244 11.239024 0 21.229268-4.995122 29.970732-13.736585 12.487805-16.234146 11.239024-41.209756-4.995122-54.946342zM295.960976 403.356098c-51.2 22.478049-99.902439 52.44878-142.360976 91.160975-16.234146 13.736585-17.482927 38.712195-2.497561 54.946342 14.985366 16.234146 38.712195 17.482927 54.946341 2.497561 43.707317-38.712195 94.907317-68.682927 148.604879-88.663415l-58.692683-59.941463zM505.756098 360.897561c-11.239024 0-22.478049 0-33.717074 1.24878l77.424391 78.673171c94.907317 8.741463 184.819512 48.702439 257.24878 112.390244 7.492683 6.243902 16.234146 9.990244 26.22439 9.990244 11.239024 0 21.229268-4.995122 28.721952-12.487805 14.985366-16.234146 12.487805-41.209756-2.497561-54.946341-97.404878-87.414634-223.531707-134.868293-353.404878-134.868293zM569.443902 683.082927l-84.917073-86.165854c-66.185366 4.995122-129.873171 31.219512-179.82439 77.42439-16.234146 14.985366-17.482927 38.712195-2.497561 54.946342 14.985366 16.234146 38.712195 17.482927 54.946342 2.497561 41.209756-37.463415 93.658537-57.443902 148.604878-57.443903 22.478049 0 43.707317 2.497561 63.687804 8.741464zM112.390244 72.429268C102.4 62.439024 87.414634 62.439024 78.673171 71.180488c-8.741463 8.741463-9.990244 23.726829 0 32.468292l679.336585 691.824391c4.995122 4.995122 11.239024 7.492683 16.234146 7.492683 6.243902 0 11.239024-2.497561 16.234147-6.243903 8.741463-8.741463 9.990244-23.726829 0-32.468292L112.390244 72.429268z" p-id="1363"></path>
			</svg>
		</div>
		<div onclick="onRefresh()" class="info">网络中断了，请检查网络设置</div>
		<div onclick="onRefresh()" class="info">点击刷新</div>
	</div>
</div>
</body>
</html>
