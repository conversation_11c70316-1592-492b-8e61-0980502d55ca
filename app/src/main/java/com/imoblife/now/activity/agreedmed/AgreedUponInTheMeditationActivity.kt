package com.imoblife.now.activity.agreedmed

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider
import com.drakeet.multitype.MultiTypeAdapter
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.Status
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.delegate.*
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.Space
import com.imoblife.now.bean.TitleEntity
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcAgreedUponInTheMeditationBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.viewmodel.AdViewModel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-11
 * 描   述：约定冥想
 */
class AgreedUponInTheMeditationActivity :
    BaseVMActivity<AgreedUponInTheMeditationViewModel>() {

    companion object {
        @JvmStatic
        fun startActivity(context: Context) {
            Intent(context, AgreedUponInTheMeditationActivity::class.java).let {
                context.startActivity(it)
            }
        }
    }

    private lateinit var mBind: LayoutAcAgreedUponInTheMeditationBinding

    private val mAdapter = MultiTypeAdapter()

    private var mItems = ArrayList<Any>()

    private lateinit var loadingHelper: LoadingHelper

    private val adViewModel by viewModels<AdViewModel>()

    override fun getLayoutResId() = R.layout.layout_ac_agreed_upon_in_the_meditation

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(
        AgreedUponInTheMeditationViewModel::class.java
    )

    override fun initView() {
        loadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_agreed_upon_in_the_meditation),
            NavIconType.BACK,
            getString(R.string.string_my_join),
            {
                if (!UserMgr.getInstance().isLogin) {
                    LoginCenter.getInstance()
                        .loginControl(this@AgreedUponInTheMeditationActivity)
                } else {
                    MyJoinMeditationClassActivity.startActivity(this)
                }
            },
            true,
        )
        mBind = mBinding as LayoutAcAgreedUponInTheMeditationBinding
        mAdapter.apply {
            register(BannerDelegate(this@AgreedUponInTheMeditationActivity))
            register(TitleEntity::class.java).to(
                TitleStartDelegate(),
                CreateMeditationDelegate(),
                TitleCenterDelegate()
            ).withKotlinClassLinker { _, entity ->
                when (entity.type) {
                    0 -> TitleStartDelegate::class
                    1 -> CreateMeditationDelegate::class
                    else -> TitleCenterDelegate::class
                }
            }
            register(DailyMeditationDelegate())
            register(SpaceViewProvider())
            items = mItems
        }
        mBind.apply {
            smartRefreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    refreshData(false)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    mViewModel.getMoreMeditationClassList()
                }
            })
            recyclerView.apply {
                adapter = mAdapter
                addItemDecoration(
                    CommonItemDecoration(
                        0,
                        DisplayUtil.dip2px(10f),
                        DisplayUtil.dip2px(20f),
                        0,
                        DisplayUtil.dip2px(20f),
                        DisplayUtil.dip2px(10f)
                    )
                )
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        refreshData()
    }

    override fun initData() {
        refreshData()
        loadingHelper.setOnReloadListener { refreshData() }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.meditationClassListEntity.observe(this) {
            if (it.isSuccess) {
                when (it.status) {
                    Status.REFRESHSUCCESS -> {
                        it.successData?.apply {
                            loadingHelper.showContentView()
                            my?.let { myEntity ->
                                mItems.add(
                                    TitleEntity(
                                        getString(R.string.string_my_agreed_upon_in_the_meditation),
                                        0
                                    )
                                )
                                mItems.add(Space(4f))
                                mItems.add(myEntity)
                            } ?: mItems.add(
                                TitleEntity(
                                    getString(R.string.string_create_meditation_class),
                                    1
                                )
                            )
                            mItems.add(
                                TitleEntity(
                                    getString(R.string.string_meditation_class_list),
                                    0
                                )
                            )
                            mItems.add(Space(4f))
                            if (!recommend.isNullOrEmpty()) {
                                mItems.add(
                                    TitleEntity(
                                        getString(R.string.string_recommended_meditation_classes),
                                        5
                                    )
                                )
                                mItems.addAll(recommend)
                            }
                            if (!general.isNullOrEmpty()) {
                                mItems.add(
                                    TitleEntity(
                                        getString(R.string.string_general_meditation_class),
                                        5
                                    )
                                )
                                mItems.addAll(general)
                            }
                            mAdapter.notifyDataSetChanged()
                        } ?: loadingHelper.showEmptyView()
                        mBind.smartRefreshLayout.finishRefresh()
                    }
                    Status.MORESUCCESS -> {
                        if (!it.successData?.general.isNullOrEmpty()) {
                            it.successData?.general?.let { list ->
                                val oldSize = mItems.size
                                mItems.addAll(oldSize, list)
                                mAdapter.notifyItemRangeInserted(oldSize, list.size)
                            }
                        }
                        mBind.smartRefreshLayout.finishLoadMore()
                    }
                    Status.NOMOREDATA -> {
                        mBind.smartRefreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    Status.EMPTYDATA -> {
                        loadingHelper.showEmptyView()
                        mBind.smartRefreshLayout.finishRefreshWithNoMoreData()
                    }
                    else -> {
                    }
                }
            } else {
                when (it.status) {
                    Status.FAILED -> {
                        loadingHelper.showErrorView()
                        mBind.smartRefreshLayout.finishRefresh(false)
                    }
                    Status.MOREFAIL -> {
                        mBind.smartRefreshLayout.finishLoadMore(false)
                    }
                    else -> {
                    }
                }
            }
        }

        adViewModel.agreedUponInTheMeditation.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { list ->
                    if (mItems.isNotEmpty() && mItems.size > 1) {
                        if (mItems[0] !is List<*>) {
                            mItems.add(0, list)
                            mAdapter.notifyDataSetChanged()
                        }
                    } else {
                        mItems.add(0, list)
                        mAdapter.notifyDataSetChanged()
                    }
                }
            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        when (event?.eventCode) {
            ConsEventCode.REFRESH_MEDITATION_CLASS_DATA -> {
                refreshData()
            }
        }
    }

    /**
     * 刷新数据
     * @param isShowLoading 是否显示 Loading
     */
    private fun refreshData(isShowLoading: Boolean = true) {
        if (isShowLoading) loadingHelper.showLoadingView()
        mItems.clear()
        adViewModel.getAgreedUponInTheMeditationBanner()
        mViewModel.getMeditationClassList()
    }

}