package com.imoblife.now.activity.main

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutAcHomePracticeGuideBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.viewmodel.HomeViewModel

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/9/25
 * 描   述：首页 - 练习 - 引导视频
 */
class HomePracticeGuideActivity : BaseVMActivity<HomeViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            Intent(context, HomePracticeGuideActivity::class.java).let {
                context.startActivity(it)
            }
        }

    }

    private lateinit var mBind: LayoutAcHomePracticeGuideBinding

    override fun getLayoutResId() = R.layout.layout_ac_home_practice_guide

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .fullScreen(true)
            .hideBar(BarHide.FLAG_HIDE_BAR)
            .statusBarDarkFont(false)
            .fitsSystemWindows(false)
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(HomeViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcHomePracticeGuideBinding
    }

    override fun initData() {
        mBind.apply {
            lifecycle.addObserver(bgVideoView)
            bgVideoView.startVideo("android.resource://${packageName}/${R.raw.sku_top_video}")
            bgVideoView.setOnFinishListener { finish() }
            tvBtnSkip.onDebounceClickListener { finish() }
        }
    }

    override fun startObserve() {}

}