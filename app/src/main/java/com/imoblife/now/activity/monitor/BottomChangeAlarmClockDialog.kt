package com.imoblife.now.activity.monitor

import android.content.DialogInterface
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.imoblife.now.R
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutViewChangeAlarmClockBinding
import com.imoblife.now.util.DateUtil
import com.imoblife.now.util.SpUtil
import com.imoblife.now.view.dialog.BaseBottomSheetDialog

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/26
 * 描   述：睡眠监测 - 设置入睡时间
 */
class BottomChangeAlarmClockDialog : BaseBottomSheetDialog(true) {

    override val viewId = R.layout.layout_view_change_alarm_clock

    private lateinit var mBind: LayoutViewChangeAlarmClockBinding

    private var mSleepTimerListener: SleepTimerListener? = null

    override fun initView() {
        mBind = mBinding as LayoutViewChangeAlarmClockBinding
        mBind.apply {
            clickProxy = ClickProxy()
            numberPickerStart.value = SpUtil.getInstance()
                .getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_HOUR, DateUtil.getCurrentTimeH())
            numberPickerEnd.value = SpUtil.getInstance()
                .getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_MINUTE, DateUtil.getCurrentTimeMinutes())
        }
    }

    fun setOnSleepTimerListener(sleepTimerListener: SleepTimerListener): BottomChangeAlarmClockDialog {
        mSleepTimerListener = sleepTimerListener
        return this
    }

    fun show(context: AppCompatActivity?) {
        context?.let { show(it.supportFragmentManager, it.javaClass.simpleName) }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 取消
                R.id.tvCancel -> dismiss()
                // 完成
                R.id.tvComplete -> {
                    mBind.apply {
                        val hour = numberPickerStart.value
                        val minute = numberPickerEnd.value
                        mSleepTimerListener?.onSetTimer(hour, minute)
                    }
                    dismiss()
                }
                else -> {}
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mSleepTimerListener = null
    }

    interface SleepTimerListener {
        fun onSetTimer(hour: Int, minute: Int)
    }

}