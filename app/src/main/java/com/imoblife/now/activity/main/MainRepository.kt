package com.imoblife.now.activity.main

import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.MyApplication
import com.imoblife.now.R
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.NewManual
import com.imoblife.now.bean.TabBean
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiService
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers

/**
 * =======================================
 * 创建日期:2020/9/29 16:52
 * 作   者:Koala
 * 邮   箱:<EMAIL>
 * 描   述:
 * 使   用:
 * =======================================
 */
class MainRepository : BaseRepository() {
    fun getNewManual(liveData: MutableLiveData<UiStatus<NewManual>>) {
        ApiClient.getInstance()
            .createService(ApiService::class.java)
            .newManual
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<NewManual>>() {
                override fun onSuccess(response: BaseResult<NewManual>?) {
                    response?.result?.url?.let {
                        liveData.value = UiStatus(true, response.result)
                    }
                }
            })
    }

    fun getTabHost(liveData: MutableLiveData<UiStatus<List<TabBean>>>) {
        liveData.value = UiStatus(true, mTabEntities)
        ApiClient.getInstance()
            .createService(ApiService::class.java)
            .getHomeTabHost(1, 1)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<TabBean>>>() {
                override fun onSuccess(response: BaseResult<List<TabBean>>?) {
                    response?.result?.forEachIndexed { index, tabBean ->
                        tabBean.icon_url?.let {
                            tabBean.unSelectTextColor =
                                ContextCompat.getColor(MyApplication.getInstance(), R.color.black)
                            tabBean.selectTextColor = ContextCompat.getColor(
                                MyApplication.getInstance(),
                                R.color.main_color
                            )
                            tabBean.unSelectDefaultIcon = mIconDefaultIds[index]
                            tabBean.selectDefaultIcon = mIconSelectIds[index]
                        }
                    }
                    response?.result?.let {
                        liveData.value = UiStatus(true, it)
                    }
                }
            })
    }


    private val mTitles by lazy(LazyThreadSafetyMode.NONE) { listOf("首页", "练习", "睡眠", "冥想", "我的") }

    private val mIconDefaultIds by lazy(LazyThreadSafetyMode.NONE) {
        listOf(
            R.mipmap.icon_tab_practice_default,
            R.mipmap.icon_tab_home_practice_default,
            R.mipmap.icon_tab_sleep_default,
            R.mipmap.icon_tab_meditation_default,
            R.mipmap.icon_tab_mine_default
        )
    }

    private val mIconSelectIds by lazy(LazyThreadSafetyMode.NONE) {
        listOf(
            R.mipmap.icon_tab_practice_selected,
            R.mipmap.icon_tab_home_practice_selected,
            R.mipmap.icon_tab_sleep_selected,
            R.mipmap.icon_tab_meditation_selected,
            R.mipmap.icon_tab_mine_selected
        )
    }

    private val mTabEntities by lazy {
        arrayListOf<TabBean>().also {
            mIconDefaultIds.forEachIndexed { index, _ ->
                it.add(
                    TabBean(
                        mTitles[index],
                        ContextCompat.getColor(MyApplication.getInstance(), R.color.black),
                        ContextCompat.getColor(MyApplication.getInstance(), R.color.main_color),
                        mIconDefaultIds[index], mIconSelectIds[index],
                        null, null
                    )
                )
            }
        }
    }

}