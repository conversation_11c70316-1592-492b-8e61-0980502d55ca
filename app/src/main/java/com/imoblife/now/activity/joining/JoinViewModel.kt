package com.imoblife.now.activity.joining

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.JoiningBean
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.repository.UserRepository

/**
 * 版   权：纳沃科技@版权所有
 * 创建日期：2021/7/27 14:23
 * 创 建 者：TUS
 * 描   述：正在参加
 */
class JoinViewModel : BaseViewModel<Any?>() {

    private val userRepository by lazy { UserRepository() }

    // 用户正在参加的活动和推荐活动
    private val _userJoiningActive = MutableLiveData<UiStatus<JoiningBean>>()
    val userJoiningActive: LiveData<UiStatus<JoiningBean>> = _userJoiningActive

    /**
     * 我的活动
     *
     * @param pageId 神策数据 - 当前页面id
     * @param categoryId 神策数据 - 当前页面 - 分类id
     * @param proceed_type 示例值：1 未开始 2 进行中 3 已结束
     */
    fun getUserJoining(pageId: Int = 0, categoryId: Int = 0, proceed_type: Int = 2) {
        userRepository.getUserJoinActive(proceed_type, pageId, categoryId, _userJoiningActive)
    }

}