package com.imoblife.now.activity.member

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.bean.VipSleepPlanLockEntity
import com.imoblife.now.databinding.LayoutAcVipSleepPlanLockBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.viewmodel.PaymentViewModel
import com.jaychang.st.SimpleText

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/10/14
 * 描   述：专属睡眠方案已解锁 - 支付 - 会员权益
 */
class VipSleepPlanLockActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(context: Context) {
            val intent = Intent(context, VipSleepPlanLockActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcVipSleepPlanLockBinding

    private var mEntity: VipSleepPlanLockEntity? = null

    override fun getLayoutResId() = R.layout.layout_ac_vip_sleep_plan_lock

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[PaymentViewModel::class.java]

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .statusBarDarkFont(true)
            .init()
    }

    override fun initView() {
        mBind = mBinding as LayoutAcVipSleepPlanLockBinding
        mBind.apply {
            imgBack.onDebounceClickListener {
                SensorsDataEvent.purchaseCompletionPageClick(
                    mEntity?.pay_price ?: 0,
                    mEntity?.subscription_id ?: 0,
                    mEntity?.order_title ?: "",
                    "返回"
                )
                finish()
            }
            stvBtn.onDebounceClickListener {
                SensorsDataEvent.purchaseCompletionPageClick(
                    mEntity?.pay_price ?: 0,
                    mEntity?.subscription_id ?: 0,
                    mEntity?.order_title ?: "",
                    "去练习"
                )
                finish()
            }
        }
    }

    override fun initData() {
        mViewModel.getVipSleepPlanLock()
    }

    override fun startObserve() {
        mViewModel.vipSleepPlanLock.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    mEntity = entity
                    mBind.apply {
                        SensorsDataEvent.purchaseCompletionPageExposure(
                            entity.pay_price,
                            entity.subscription_id,
                            entity.order_title
                        )
                        tvVipTermOfValidity.text = entity.vip_time
                        tvUserId.text = entity.uid
                        tvPackage.text = entity.order_title
                        tvPayType.text = entity.pay_name
                        tvPayDate.text = entity.pay_time
                        tvOrderNumber.text = entity.order_id
                        tvPrice.text = SimpleText
                            .from("${entity.title}¥${entity.pre_price}")
                            .first("¥${entity.pre_price}")
                            .textColor(R.color.color_FE7723)
                        tvStartSleep.text = SimpleText
                            .from("与 ${entity.number} 名用户一起开启冥想之旅！")
                            .first(entity.number.toString())
                            .textColor(R.color.color_86867C)
                    }
                } ?: run { finish() }
            } else {
                finish()
            }
        }
    }

}