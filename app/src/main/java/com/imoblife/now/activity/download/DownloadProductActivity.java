package com.imoblife.now.activity.download;


import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.imoblife.commlibrary.base.BackgroundRunner;
import com.imoblife.commlibrary.base.BackgroundTask;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.adapter.DownloadAdapter;
import com.imoblife.now.bean.Course;
import com.imoblife.now.bean.DownLoadInfo;
import com.imoblife.now.bean.ReturnValue;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.model.CourseMgr;
import com.imoblife.now.model.DownloadMgr;
import com.imoblife.now.util.DialogUtil;
import com.imoblife.now.util.ToastUtils;
import com.kongzue.dialog.interfaces.OnDialogButtonClickListener;
import com.kongzue.dialog.util.BaseDialog;
import java.io.Serializable;
import java.lang.ref.WeakReference;
import java.util.List;

/***
 * ================================================
 * @时间 2017/6/7 on 17:43
 * @作者 Yuan
 * @类名 DownloadProductActivity
 * @描述 我的下载
 * ================================================
 */
public class DownloadProductActivity extends MvpBaseActivity {

    private static final String TAG = "DownloadProductActivity";
    private RecyclerView recyclerView;
    private TextView dataNullHint;
    private DownloadAdapter mAdapter;
    private List<DownLoadInfo> downLoadInfoList;
    private final int EVENT_LOAD_DOWNLOAD = 0;
    private final int EVENT_DELETE_TRACK_DOWNLOAD = 1;
    private ImageView backImg;
    private Object delData;
    WeakReference<DownloadProductActivity> mWeakReference;

    @Override
    protected int setContentViewId() {
        return R.layout.activity_download_product;
    }

    @Override
    protected void initView() {
        backImg = (ImageView) findView(R.id.title_back_img);
        backImg.setOnClickListener(listener);
        findView(R.id.title_line).setVisibility(View.VISIBLE);
        TextView title = (TextView) findView(R.id.title_content_text);
        title.setText(getString(R.string.string_course_cache_text));
        recyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        dataNullHint = (TextView) findViewById(R.id.data_null_hint);
        mAdapter = new DownloadAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setAdapterItemListener((position, data) -> {
            try {
                if (data instanceof Course) {
                    Course course = (Course) data;
                    Intent intent = new Intent(DownloadProductActivity.this, DownloadTrackActivity.class);
                    intent.putExtra(ConsIntent.BUNDLE_COURSE, (Serializable) course);
                    startActivity(intent);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        mAdapter.setAdapterViewListener((rId, data) -> deleteDownload(data));
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mWeakReference = new WeakReference<>(DownloadProductActivity.this);
        loadDownloadProduct();
    }


    /**
     * 曲目删除
     */
    private void deleteDownload(Object data) {
        this.delData = data;
        String delTitle = null;
        if (data instanceof Course) {
            Course course = (Course) data;
            delTitle = course.getTitle();
        }
        DialogUtil.showDialog(mWeakReference.get(),
            String.format(getString(R.string.is_del_txt), delTitle),
            new OnDialogButtonClickListener() {
                @Override
                public boolean onClick(BaseDialog baseDialog, View v) {
                    showWaitLoading();
                    new BackgroundRunner(backgroundTask, EVENT_DELETE_TRACK_DOWNLOAD);
                    return false;
                }
            });

    }

    View.OnClickListener listener = v -> {
        switch (v.getId()) {
            case R.id.title_back_img:
                finish();
                break;

        }
    };

    /**
     * 加载下载完成的课程
     */
    private void loadDownloadProduct() {
        new BackgroundRunner(backgroundTask, EVENT_LOAD_DOWNLOAD);
    }


    BackgroundTask backgroundTask = new BackgroundTask() {
        @Override
        public ReturnValue call(int eventCode, Object... params) {
            ReturnValue returnValue = new ReturnValue();
            switch (eventCode) {
                case EVENT_LOAD_DOWNLOAD:
                    downLoadInfoList = DownloadMgr.getInstance().queryListByDownloaded();
                    returnValue.setResult(CourseMgr.getInstance().getCourseByDownload(downLoadInfoList));
                    break;
                case EVENT_DELETE_TRACK_DOWNLOAD:
                    //删除数据库、文件
                    if (delData instanceof Course) {
                        Course course = (Course) delData;
                        returnValue.setResult(DownloadMgr.getInstance().delDownloadByCourseId(course.getId()));
                    }
                    break;
            }
            return returnValue;
        }

        @Override
        public void onResult(int eventCode, Object o) {
            ReturnValue t= (ReturnValue) o;
            if (t == null && isFinishing()) {
                return;
            }
            hideWaitLoading();
            switch (eventCode) {
                case EVENT_LOAD_DOWNLOAD:
                    List<Course> courseList = null;
                    if (t != null) {
                        courseList = t.getResult();
                        if (t.getResult() == null){
                            dataNullHint.setVisibility(View.VISIBLE);
                        }else {
                            dataNullHint.setVisibility(View.GONE);
                        }
                        mAdapter.setDatas(courseList);
                    }
                    break;
                case EVENT_DELETE_TRACK_DOWNLOAD:
                    if (t != null && t.isSuccess()) {
                        loadDownloadProduct();
                        ToastUtils.showShortToastCenter(getString(R.string.string_txt_track_delete_success));
                    }
                    break;
            }
        }
    };
}
