package com.imoblife.now.activity.member

import android.app.Activity
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Looper
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.MyApplication.Companion.mCancelCheckedSkuId
import com.imoblife.now.R
import com.imoblife.now.activity.main.MainActivity
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.SubSkuInfo
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.ActivityVipSkuJBinding
import com.imoblife.now.ext.preLoadImg
import com.imoblife.now.ext.slideInFromLeft
import com.imoblife.now.ext.slideOutToRight
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.NetworkUtils
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.dialog.PopupHalfAlterImgDialog
import com.imoblife.now.viewmodel.PaymentViewModel
import io.paperdb.Paper
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/7/3
 * 描   述：ob - sku - 全屏订阅 & 一个Sku「类似于首页全屏」
 */
class VipSkuJActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        fun startActivity(context: Activity, templateId: Int?, is_second_ob: Int) {
            Intent(context, VipSkuJActivity::class.java).apply {
                putExtra(ConsCommon.SKU_TEMPLATE_ID, templateId)
                putExtra(ConsCommon.SKU_IS_OB_SUB, is_second_ob)
                context.startActivity(this)
                context.slideInFromLeft()
            }
        }

    }

    private lateinit var mBind: ActivityVipSkuJBinding
    private var mSubscribe: Subscribe? = null
    private val popupHalfAlterImageDialog by lazy(LazyThreadSafetyMode.NONE) {
        PopupHalfAlterImgDialog(
            this
        )
    }

    private var isClickCloseVipPage = false
    private var isShowCancelDialogPage = false
    private var mTemplateId: Int = 0
    private var mSkuInfo: SubSkuInfo? = null

    // 是否购买成功
    private var isPurchase = false

    // 是否第二次ob订阅
    private var mIsSecondOb = 0

    // 是否需要刷新当前页面 - update Sku
    private var mNeedRefreshOb = false

    // 神策数据 - 商业线订阅页面挽留弹框曝光 - 触发原因
    private var mTriggerReason = ""

    // 神策数据 - 商业线订阅页面挽留弹框曝光 - 是否当次ob的首次曝光
    private var mIsFirstShow = 0

    // 缓存的entity
    private val mEntity: SubSkuInfo? by lazy(LazyThreadSafetyMode.NONE) {
        Paper.book().read(ConsCommon.OB_SKU_CACHE_ENTITY)
    }

    // 是否展示 - 停留挽留弹窗
    private var mIsShowStayAndRetainDialog = false
    private var mIsShowStayAndRetainDialogJob: Job? = null
    private var mIsShowStayAndRetainDialogState = true

    // 点击支付触发场景逻辑
    private var mPaySceneType = ""

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsCommon.SKU_TEMPLATE_ID)) {
                mTemplateId = it.getIntExtra(ConsCommon.SKU_TEMPLATE_ID, 0)
            }
            if (hasExtra(ConsCommon.SKU_IS_OB_SUB)) {
                mIsSecondOb = it.getIntExtra(ConsCommon.SKU_IS_OB_SUB, 0)
            }
        }
    }

    override fun getLayoutResId() = R.layout.activity_vip_sku_j

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .init()
    }

    override fun initVM() = ViewModelProvider(this).get(PaymentViewModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityVipSkuJBinding
        lifecycle.addObserver(mBind.paySubmitTxt)
        mBind.apply {
            paySubmitTxt.setSubmitPayOnClickListener {
                mPaySceneType = "ob"
                mIsShowStayAndRetainDialog = true
                // 埋点 - 商业线订阅页面点击事件 - 下一步
                SensorsDataEvent.businessLineVipPageClick(
                    mTemplateId,
                    getString(R.string.string_next_step)
                )
                mBind.subProtocolPrivacy.isAgreePrivacy(mSubscribe, "首页全屏") {
                    PayCenter.getInstance().doSubmitPay(this@VipSkuJActivity, mSubscribe)
                }
            }
            closeImg.setOnClickListener {
                mIsShowStayAndRetainDialog = true
                mTriggerReason = "点击订阅页关闭按钮"
                mIsFirstShow += 1
                // 埋点 - 商业线订阅页面点击事件 - 关闭
                SensorsDataEvent.businessLineVipPageClick(
                    mTemplateId,
                    getString(R.string.string_close_txt)
                )
                if (isPurchase) {
                    super.onBackPressed()
                    return@setOnClickListener
                }
                mSkuInfo?.let { skuInfo ->
                    isClickCloseVipPage = true
                    adSkuPageClose(
                        this@VipSkuJActivity,
                        mViewModel,
                        skuInfo,
                        isShowCancelDialogPage,
                        mIsSecondOb,
                        skuId = mSubscribe?.id ?: -1
                    )
                } ?: super.onBackPressed()
            }
        }
    }

    override fun initData() {
        if (ConfigMgr.getInstance().config.isStart_login) {
            if (!UserMgr.getInstance().isLogin) {
                LoginCenter.getInstance().loginControl(this, LoginCenter.LoginStyleDialog)
            }
        }
        mEntity?.let { updateUI(it) } ?: run {
            mViewModel.getSubSkuTest(mTemplateId, mIsSecondOb)
        }
        Looper.myQueue().addIdleHandler {
            if (!NetworkUtils.isNetworkAvailable()) {
                ToastUtils.showShortToast(getString(R.string.string_ob_questionnaire_no_net_txt))
            }
            false
        }
    }

    override fun startObserve() {
        mViewModel.testSubSkuInfo.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    updateUI(entity)
                } ?: super.onBackPressed()
            } else {
                super.onBackPressed()
            }
        }
        mViewModel.adPageCancelSkuList.observe(this) {
            val flag = mIsFirstShow == 1
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    SensorsDataEvent.businessDialogPageShow(
                        entity.template_id_429,
                        flag,
                        mTriggerReason
                    )
                }
            }
            showHalfDialog(it, popupHalfAlterImageDialog, mTriggerReason, flag)
            popupHalfAlterImageDialog.setClickCloseEvent {
                if (mNeedRefreshOb && flag) {
                    mViewModel.getSubSkuTest(mTemplateId, mIsSecondOb, mNeedRefreshOb)
                }
            }
            popupHalfAlterImageDialog.setClickPayEvent { content ->
                mPaySceneType = content
            }
            if (isClickCloseVipPage) {
                isShowCancelDialogPage = true
            }
        }
    }

    /**
     * 更新UI
     *
     * @param entity SubSkuInfo
     */
    private fun updateUI(entity: SubSkuInfo) {
        mNeedRefreshOb = entity.isNeed_refresh_ob
        entity.preLoadImg(this)
        setSukInfo(entity)
    }

    private fun setSukInfo(subSkuInfo: SubSkuInfo?) {
        subSkuInfo?.let {
            mSkuInfo = it
            if (!TextUtils.isEmpty(it.bg_img)) {
                Glide
                    .with(this)
                    .load(it.bg_img)
                    .set(
                        WebpFrameLoader.FRAME_CACHE_STRATEGY,
                        WebpFrameCacheStrategy.AUTO
                    )
                    .into(object : SimpleTarget<Drawable>() {
                        override fun onResourceReady(
                            resource: Drawable,
                            transition: Transition<in Drawable>?
                        ) {
                            mBind.container.background = resource
                        }
                    })
            }
            initStayAndRetain()
            it.sku_list.firstNotNullOf { subscribe ->
                mSubscribe = subscribe
                ImageLoader.loadImageUrl(this, subscribe.background_checked, mBind.topHeaderImg)

                val isShouAutoCloseable =
                    if (ConfigMgr.getInstance().config.isAuto_vip_privacy_ad) {
                        !ConfigMgr.getInstance().config.auto_vip_privacy_special_ad_list.contains(it.source_id)
                    } else {
                        false
                    }
                mBind.subProtocolPrivacy.setData(subscribe, isShouAutoCloseable)

            }
            mBind.paySubmitTxt.setPayButtonData(it.pay_button)
            ImageLoader.loadImageUrl(
                this,
                it.close_img,
                mBind.closeImg,
                R.mipmap.icon_vip_sku_close
            )
        }
    }

    /**
     * 停留挽留弹窗
     */
    private fun initStayAndRetain() {
        if (mIsShowStayAndRetainDialogState) {
            mIsShowStayAndRetainDialogState = false
            mSkuInfo?.let { entity ->
                if (entity.is_residence_time == 1) {
                    mIsShowStayAndRetainDialogJob = lifecycleScope.launch {
                        delay(entity.residence_seconds * 1000L)
                        if (mIsShowStayAndRetainDialog) {
                            mIsShowStayAndRetainDialogJob?.cancel()
                        } else {
                            mSkuInfo?.let { entity ->
                                showStayAndRetainDialog(entity)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 展示停留挽留弹窗
     *
     * @param entity SubSkuInfo
     */
    private fun showStayAndRetainDialog(entity: SubSkuInfo) {
        mTriggerReason = "停留挽留"
        mIsFirstShow += 1
        adSkuPageStayAndRetain(
            mViewModel,
            entity,
            skuId = mSubscribe?.id ?: -1
        )
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        event?.let {
            when (it.eventCode) {
                ConsEventCode.PAY_CANCEL_EVENT, ConsEventCode.PAY_FAIL_EVENT -> {
                    mSkuInfo?.let { entity ->
                        when (mPaySceneType) {
                            "点击订阅页关闭按钮" -> {
                                adSkuPageClose(
                                    this@VipSkuJActivity,
                                    mViewModel,
                                    entity,
                                    isShowCancelDialogPage,
                                    mIsSecondOb,
                                    skuId = mSubscribe?.id ?: -1
                                )
                            }

                            "停留挽留" -> showStayAndRetainDialog(entity)

                            else -> {
                                mTriggerReason = "取消支付"
                                mIsFirstShow += 1
                                adSkuPageCancelPay(
                                    mViewModel,
                                    entity,
                                    skuId = mSubscribe?.id ?: -1
                                )
                            }
                        }
                        isPurchase = false
                    }
                }

                ConsEventCode.PAY_SUCCESS_EVENT -> {
                    isPurchase = true
                    MainActivity.openMainActivity(
                        this,
                        ConfigMgr.getInstance().config.app_default_tab,
                        isForceLogin = "Login"
                    )
                }

                ConsEventCode.PAY_IN_PROGRESS_EVENT -> DialogUtil.showWaitLoading(
                    this,
                    getString(R.string.string_order_validation_txt),
                    false
                )

                else -> {}
            }
        }
    }

    override fun onBackPressed() {}

    override fun finish() {
        if (!isPurchase && mSkuInfo?.need_second_ob == 2) {
            VipSkuKActivity.startActivity(this)
        }
        super.finish()
        slideOutToRight()
    }

    override fun onDestroy() {
        super.onDestroy()
        mCancelCheckedSkuId = 0
        Paper.book().delete(ConsCommon.OB_SKU_CACHE_ENTITY)
    }

}