package com.imoblife.now.activity.category

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.coorchice.library.SuperTextView
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationTagItemEntity
import com.imoblife.now.ext.pageRoute

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - 常见问题
 */
class CategoryCommonProblemAdapter :
    BaseQuickAdapter<CategoryNavigationTagItemEntity, BaseViewHolder>(R.layout.layout_item_category_common_problem) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationTagItemEntity?) {
        item?.apply {
            val stvContent = holder.getView<SuperTextView>(R.id.stvContent)

            stvContent.text = title
            holder.itemView.setOnClickListener { pageRoute(mContext, holder.layoutPosition) }
        }
    }

}