package com.imoblife.now.activity.monitor.history

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import com.imoblife.now.R
import com.imoblife.now.bean.AllSleepHistoryEntity
import com.imoblife.now.databinding.LayoutViewAllSleepHistoryContentBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/28
 * 描   述：所有睡眠记录 - ContentItemView
 */
class AllSleepHistoryContentItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {

    private var mBind: LayoutViewAllSleepHistoryContentBinding =
        DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.layout_view_all_sleep_history_content,
            this,
            true
        )

    private var mEntity: AllSleepHistoryEntity.ListEntity? = null

    init {
        setOnClickListener {
            mEntity?.let { entity -> SleepRecordingActivity.startActivity(context, entity) }
        }
    }

    fun setItemData(item: AllSleepHistoryEntity.ListEntity) {
        mBind.apply {
            mEntity = item
            entity = item
            executePendingBindings()
        }
    }

}