package com.imoblife.now.activity.category

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.drakeet.multitype.MultiTypeAdapter
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.home.HomeRecommendListeningDelegate
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.LayoutAcCategoryNavigationItemBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.EmptyUtils
import org.greenrobot.eventbus.EventBus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/11
 * 描   述：首页 - 金刚区 - 分类导航 - 分类Item
 */
class CategoryNavigationItemActivity : BaseVMActivity<CategoryNavigationViewModel>() {

    companion object {

        fun startActivity(
            context: Context,
            type: String,
            cat_id: Int,
            title: String,
            pageId: Int,
            categoryId: Int
        ) {
            val intent = Intent(context, CategoryNavigationItemActivity::class.java)
            intent.apply {
                putExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE, type)
                putExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_ID, cat_id)
                putExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_TITLE, title)
                putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId)
                putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId)
                context.startActivity(this)
            }
        }

    }

    private lateinit var mBind: LayoutAcCategoryNavigationItemBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private var mCatId = 0

    private var mType: String? = ""

    private var mTitle: String? = ""

    // 神策数据 - 当前页面id
    private var mPageId = 0

    // 神策数据 - 当前页面 - 分类id
    private var mCategoryId = 0

    private val mAdapter = MultiTypeAdapter()

    private var mItems = ArrayList<Any>()

    override fun getLayoutResId() = R.layout.layout_ac_category_navigation_item

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE)) {
                mType = intent.getStringExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE)
            }
            if (hasExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_ID)) {
                mCatId = intent.getIntExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_ID, 0)
            }
            if (hasExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_TITLE)) {
                mTitle = intent.getStringExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_TITLE)
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
                mPageId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0)
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
                mCategoryId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0)
            }
        }
    }

    override fun initVM() = ViewModelProvider(this).get(CategoryNavigationViewModel::class.java)

    override fun initView() {
        mLoadingHelper = ToolbarUtils.setToolbar(this, mTitle, NavIconType.BACK, true)
        mBind = mBinding as LayoutAcCategoryNavigationItemBinding
        mAdapter.apply {
            register(CategoryNavigationItemDelegate())
            register(
                HomeRecommendListeningDelegate(
                    getString(R.string.string_home_page_recommendation),
                    mTitle ?: ""
                )
            )
            items = mItems
        }
        mBind.apply {
            recyclerView.apply {
                adapter = mAdapter
                addItemDecoration(
                    CommonItemDecoration(
                        0,
                        DisplayUtil.dip2px(14F),
                        DisplayUtil.dip2px(14F),
                        DisplayUtil.dip2px(14F),
                        DisplayUtil.dip2px(14F),
                        DisplayUtil.dip2px(14F)
                    )
                )
            }
            smartRefreshLayout.setOnRefreshListener {
                mViewModel.getData(mType ?: "", mCatId, mPageId, mCategoryId)
            }
        }
    }

    override fun initData() {
        mLoadingHelper.apply {
            showLoadingView()
            setOnReloadListener {
                showLoadingView()
                mViewModel.getData(mType ?: "", mCatId, mPageId, mCategoryId)
            }
        }
        mViewModel.getData(mType ?: "", mCatId, mPageId, mCategoryId)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.apply {
            // 首页 - 金刚区 - 分类导航 - 分类Item
            listItemData.observe(this@CategoryNavigationItemActivity) {
                mBind.smartRefreshLayout.finishRefresh()
                if (it.isSuccess) {
                    it.successData?.let { list ->
                        mLoadingHelper.showContentView()
                        if (EmptyUtils.isNotEmpty(list)) {
                            mItems.clear()
                            mItems.addAll(list)
                            mAdapter.notifyDataSetChanged()
                        }
                    } ?: mLoadingHelper.showEmptyView()
                } else mLoadingHelper.showErrorView()
            }
            // 首页 - 推荐 - 正在听
            listItemCourseData.observe(this@CategoryNavigationItemActivity) {
                mBind.smartRefreshLayout.finishRefresh()
                if (it.isSuccess) {
                    it.successData?.let { list ->
                        mLoadingHelper.showContentView()
                        if (EmptyUtils.isNotEmpty(list)) {
                            mItems.clear()
                            mItems.addAll(list)
                            mAdapter.notifyDataSetChanged()
                        }
                    } ?: mLoadingHelper.showEmptyView()
                } else mLoadingHelper.showErrorView()
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        // 刷新首页推荐数据 => 此刻收听热榜
        if (mType == ConsCommon.HOME_RECOMMEND_NOW_HOT_MORE) EventBus.getDefault()
            .post(BaseEvent(ConsEventCode.REFRESH_HOME_COURSE_RECOMMEND))
    }

}