package com.imoblife.now.activity.member;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.imoblife.commlibrary.mvp.CreatePresenter;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.adapter.CoinAdapter;
import com.imoblife.now.adapter.decoration.GridSpacingItemDecoration;
import com.imoblife.now.bean.Coin;
import com.imoblife.now.bean.Subscribe;
import com.imoblife.now.constant.ConsEventCode;
import com.imoblife.commlibrary.base.BaseEvent;
import com.imoblife.now.model.UserMgr;
import com.imoblife.now.mvp_contract.RechargeContact;
import com.imoblife.now.mvp_presenter.RechargePresenter;
import com.imoblife.now.payment.PayCenter;
import com.imoblife.now.util.ToastUtils;

import java.lang.ref.WeakReference;
import java.util.List;

@CreatePresenter(presenter = RechargePresenter.class)
public class RechargeActivity extends MvpBaseActivity<RechargePresenter> implements RechargeContact.IRechargeView, View.OnClickListener {

    private ImageView titleBackImg;
    private TextView titleContentText;
    private TextView memberBalanceTxt;
    private RecyclerView recyclerView;
    private TextView rechargeTxt;
    private CoinAdapter coinAdapter;

    @Override
    protected int setContentViewId() {
        return R.layout.activity_recharge;
    }

    @Override
    protected void initView() {
        titleBackImg = (ImageView) findView(R.id.title_back_img);
        titleBackImg.setOnClickListener(this);
        titleContentText = (TextView) findView(R.id.title_content_text);
        titleContentText.setText(R.string.string_my_account);
        memberBalanceTxt = findViewById(R.id.member_balance_txt);
        recyclerView = findViewById(R.id.recycler_view);
        rechargeTxt = (TextView) findView(R.id.recharge_txt);
        rechargeTxt.setOnClickListener(this);
        coinAdapter = new CoinAdapter(this);
        recyclerView.setLayoutManager(new StaggeredGridLayoutManager(3, StaggeredGridLayoutManager.VERTICAL));
        recyclerView.addItemDecoration(new GridSpacingItemDecoration(3, 32, true));
        recyclerView.setAdapter(coinAdapter);
    }
    @Override
    protected void initData() {
        //获取Now贝列表
        getPresenter().getCoinList();
        setUserCoin();
    }

    private void setUserCoin() {
        if (UserMgr.getInstance().getUser()!=null){
            memberBalanceTxt.setText(UserMgr.getInstance().getUserCoin()+"");
        }
    }

    @Override
    public void onEventMainThread(BaseEvent event) {
        super.onEventMainThread(event);
        if (event.getEventCode() == ConsEventCode.CHANGE_SUBSCRIBE_EVENT) {
            setUserCoin();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.title_back_img:
                onBackPressed();
                break;
            case R.id.recharge_txt:
                if (coinAdapter != null) {
                    Coin coin = coinAdapter.getCheckedCoin();
                    if (coin!=null) {
                        Subscribe subscribe=new Subscribe();
                        subscribe.setPrice(coin.getPrice());
                        subscribe.setDiscount_price(coin.getDiscount_price());
                        subscribe.setId(coin.getId());
                        subscribe.setTitle(coin.getTitle());
                        subscribe.setPay_type(coin.getPay_type());
                        subscribe.setIs_sub(coin.isIs_sub());
                        subscribe.setProduct_type(coin.getProduct_type());
                        PayCenter.getInstance().doSubmitPay(this,subscribe);
                    }
                }
                break;
        }
    }

    @Override
    public void getCoinList(List<Coin> coinList) {
        hideWaitLoading();
        coinAdapter.upData(coinList);
    }

    @Override
    public void getCoinListFail(String msg) {
        hideWaitLoading();
        ToastUtils.showShortToastCenter(msg);
    }

}
