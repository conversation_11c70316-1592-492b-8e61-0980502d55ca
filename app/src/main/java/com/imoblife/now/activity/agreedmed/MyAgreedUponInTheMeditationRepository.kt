package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.MeditationClassCertificateEntity
import com.imoblife.now.bean.MeditationClassCreateEntity
import com.imoblife.now.bean.MeditationClassDetailEntity
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiServiceAgreedUponUnTheMeditation
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers
import com.imoblife.now.util.EmptyUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-13
 * 描   述：我的约定冥想_Repository
 */
class MyAgreedUponInTheMeditationRepository : BaseRepository() {

    private var mPage = 1

    /**
     * 获取冥想班详情
     */
    fun getMeditationClassDetail(
        teamId: Int,
        initPage: Boolean = false,
        _meditationClassDetailEntity: MutableLiveData<UiStatus<MeditationClassDetailEntity>>,
    ) {
        if (initPage) mPage = 1
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .getMeditationClassDetail(teamId, mPage)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationClassDetailEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationClassDetailEntity>?) {
                    response?.result?.let {
                        if (EmptyUtils.isNotEmpty(it)) {
                            if (mPage == 1) {
                                _meditationClassDetailEntity.value =
                                    UiStatus(true, it, null, Status.REFRESHSUCCESS)
                            } else {
                                _meditationClassDetailEntity.value =
                                    UiStatus(true, it, null, Status.MORESUCCESS)
                            }
                            mPage++
                        } else {
                            checkStatus(_meditationClassDetailEntity)
                        }
                    } ?: checkStatus(_meditationClassDetailEntity)
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    if (mPage == 1) {
                        _meditationClassDetailEntity.value =
                            UiStatus(false, null, null, Status.FAILED)
                    } else {
                        _meditationClassDetailEntity.value =
                            UiStatus(false, null, null, Status.MOREFAIL)
                    }
                }
            })
    }

    private fun checkStatus(_meditationClassDetailEntity: MutableLiveData<UiStatus<MeditationClassDetailEntity>>) {
        if (mPage == 1) {
            _meditationClassDetailEntity.value = UiStatus(true, null, null, Status.EMPTYDATA)
        } else {
            _meditationClassDetailEntity.value = UiStatus(true, null, null, Status.NOMOREDATA)
        }
    }

    /**
     * 提前毕业
     */
    fun graduateEarly(_graduateEarly: MutableLiveData<UiStatus<Boolean>>) {
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .graduateEarly()
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<Boolean>>() {
                override fun onSuccess(response: BaseResult<Boolean>?) {
                    response?.result?.let {
                        _graduateEarly.value = UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    _graduateEarly.value = UiStatus(isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED)
                }
            })
    }

    /**
     * 毕业证书
     */
    fun getCertificate(
        team_id: Int,
        _meditationClassCertificateEntity: MutableLiveData<UiStatus<MeditationClassCertificateEntity>>,
    ) {
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .getCertificate(team_id)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationClassCertificateEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationClassCertificateEntity>?) {
                    response?.result?.let {
                        _meditationClassCertificateEntity.value =
                            UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    _meditationClassCertificateEntity.value = UiStatus(isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED)
                }
            })
    }

    /**
     * 加入组队
     */
    fun joinMeditationTeam(
        team_id: Int,
        _meditationClassCreateEntity: MutableLiveData<UiStatus<MeditationClassCreateEntity>>,
    ) {
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .joinMeditationTeam(team_id)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationClassCreateEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationClassCreateEntity>?) {
                    response?.result?.let {
                        _meditationClassCreateEntity.value =
                            UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    _meditationClassCreateEntity.value = UiStatus(isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED)
                }
            })
    }

}