package com.imoblife.now.activity.monitor.history

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.drakeet.multitype.MultiTypeAdapter
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.commlibrary.utils.ViewType
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.SleepMonitorViewModel
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarAdapter
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.AllSleepHistoryEntity
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcAllSleepHistoryBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.Status
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.EmptyUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/28
 * 描   述：所有睡眠记录历程
 */
class AllSleepHistoryActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, AllSleepHistoryActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcAllSleepHistoryBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private val mAdapter = MultiTypeAdapter()

    private var mItems = ArrayList<Any>()

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .statusBarColor(R.color.color_0C0E1A)
            .statusBarDarkFont(false)
            .fitsSystemWindows(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_all_sleep_history

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mLoadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_all_sleep_history),
            NavIconType.BACK_SLEEP_MONITORING_REPORT_COURSE,
            "",
            null,
            true,
        )
        mLoadingHelper.getAdapter<ToolbarAdapter>(ViewType.TITLE).setRightTitleColor(
            ContextCompat.getColor(this, R.color.white),
            ContextCompat.getColor(this, R.color.main_color)
        )
        mBind = mBinding as LayoutAcAllSleepHistoryBinding
        mAdapter.apply {
            register(AllSleepHistoryTitleDelegate())
            register(AllSleepHistoryContentDelegate())
            items = mItems
        }
        mBind.apply {
            recyclerView.addItemDecoration(CommonItemDecoration(8.dp, 8.dp, 0, 0, 0, 0))
            recyclerView.adapter = mAdapter
            smartRefreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    mViewModel.getMonthSleepList()
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    mViewModel.getMonthSleepListLoadMore()
                }
            })
            imgBtn.setOnClickListener { finish() }
        }
    }

    override fun initData() {
        mLoadingHelper.setOnReloadListener {
            DialogUtil.showWaitLoading()
            mViewModel.getMonthSleepList()
        }
        mViewModel.getMonthSleepList()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.SLEEP_RECORD_DELETE) {
            val sleepId = event.getResult<Int>()
            mItems.forEachIndexed { index, any ->
                if (any is AllSleepHistoryEntity.ListEntity) {
                    if (sleepId == any.id) {
                        mItems.removeAt(index)
                        mAdapter.notifyDataSetChanged()
                        if (mItems.size <= 1) {
                            mViewModel.getMonthSleepList()
                        }
                        return@forEachIndexed
                    }
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.monthSleepList.observe(this) {
            DialogUtil.hideWaitLoading()
            if (it.isSuccess) {
                when (it.status) {
                    Status.REFRESHSUCCESS -> {
                        it.successData?.apply {
                            if (EmptyUtils.isEmpty(this)) {
                                showRvView()
                            } else {
                                showRvView(true)
                                updateStartYourOneDayData(this, isRefresh = true)
                            }
                        }
                        mBind.smartRefreshLayout.finishRefresh()
                    }
                    Status.MORESUCCESS -> {
                        if (EmptyUtils.isNotEmpty(this)) {
                            it.successData?.let { entity ->
                                updateStartYourOneDayData(entity)
                            }
                        }
                        mBind.smartRefreshLayout.finishLoadMore()
                    }
                    Status.NOMOREDATA -> {
                        mBind.smartRefreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    Status.EMPTYDATA -> {
                        showRvView()
                        mBind.smartRefreshLayout.finishRefreshWithNoMoreData()
                    }
                    else -> {
                    }
                }
            } else {
                when (it.status) {
                    Status.FAILED -> {
                        mLoadingHelper.showErrorView()
                        mBind.smartRefreshLayout.finishRefresh(false)
                    }
                    Status.MOREFAIL -> {
                        mBind.smartRefreshLayout.finishLoadMore(false)
                    }
                    else -> {
                    }
                }
            }
        }
    }

    /**
     * 显示RvView
     *
     * @param bool Boolean
     */
    private fun showRvView(bool: Boolean = false) {
        mBind.apply {
            if (bool) {
                recyclerView.visibility = View.VISIBLE
                groupNoData.visibility = View.GONE
            } else {
                recyclerView.visibility = View.GONE
                groupNoData.visibility = View.VISIBLE
            }
        }
    }

    /**
     * 更新所有睡眠记录历程数据
     *
     * @param list 所有睡眠记录历程
     */
    @SuppressLint("NotifyDataSetChanged")
    fun updateStartYourOneDayData(list: List<AllSleepHistoryEntity>, isRefresh: Boolean = false) {
        if (isRefresh) mItems.clear()
        list.forEach { entity ->
            if (!TextUtils.isEmpty(entity.month_title)) {
                mItems.add(entity.month_title)
            }
            if (!entity.list.isNullOrEmpty()) {
                mItems.addAll(entity.list)
            }
        }
        mAdapter.notifyDataSetChanged()
    }

}