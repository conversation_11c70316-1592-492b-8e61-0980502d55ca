package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.MeditationClassCertificateEntity
import com.imoblife.now.bean.MeditationClassCreateEntity
import com.imoblife.now.bean.MeditationClassDetailEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-13
 * 描   述：我的约定冥想_ViewModel
 */
class MyAgreedUponInTheMeditationViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { MyAgreedUponInTheMeditationRepository() }

    // 冥想班详情
    private val _meditationClassDetailEntity =
        MutableLiveData<UiStatus<MeditationClassDetailEntity>>()
    val meditationClassDetailEntity: LiveData<UiStatus<MeditationClassDetailEntity>> =
        _meditationClassDetailEntity

    // 提前毕业
    private val _graduateEarly = MutableLiveData<UiStatus<Boolean>>()
    val graduateEarly: LiveData<UiStatus<Boolean>> = _graduateEarly

    // 毕业证书
    private val _meditationClassCertificateEntity =
        MutableLiveData<UiStatus<MeditationClassCertificateEntity>>()
    val meditationClassCertificateEntity: LiveData<UiStatus<MeditationClassCertificateEntity>> =
        _meditationClassCertificateEntity

    // 加入组队
    private val _meditationClassCreateEntity =
        MutableLiveData<UiStatus<MeditationClassCreateEntity>>()
    val meditationClassCreateEntity: LiveData<UiStatus<MeditationClassCreateEntity>> =
        _meditationClassCreateEntity

    /**
     * 获取冥想班详情
     *
     * @param team_id 组队id
     */
    fun getMeditationClassDetail(team_id: Int) {
        mRepository.getMeditationClassDetail(team_id, initPage = true, _meditationClassDetailEntity)
    }

    /**
     * 获取冥想班详情_排行榜
     *
     * @param team_id 组队id
     */
    fun getMoreMeditationClassRankList(team_id: Int) {
        mRepository.getMeditationClassDetail(team_id,
            initPage = false,
            _meditationClassDetailEntity)
    }

    /**
     * 提前毕业
     */
    fun graduateEarly() {
        mRepository.graduateEarly(_graduateEarly)
    }

    /**
     * 毕业证书
     *
     * @param team_id 组队id
     */
    fun getCertificate(team_id: Int) {
        mRepository.getCertificate(team_id, _meditationClassCertificateEntity)
    }

    /**
     * 加入组队
     *
     * @param team_id 组队id
     */
    fun joinMeditationTeam(team_id: Int) {
        mRepository.joinMeditationTeam(team_id, _meditationClassCreateEntity)
    }

}