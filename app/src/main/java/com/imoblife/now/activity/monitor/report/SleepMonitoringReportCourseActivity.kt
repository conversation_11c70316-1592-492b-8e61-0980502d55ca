package com.imoblife.now.activity.monitor.report

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Looper
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager.widget.ViewPager
import com.flyco.tablayout.listener.OnTabSelectListener
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.SleepMonitorViewModel
import com.imoblife.now.activity.monitor.sleep.SleepMonitorActivity
import com.imoblife.now.databinding.LayoutAcSleepMonitoringReportCourseBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.player.BGPlayerUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/27
 * 描   述：睡眠监测报告 - 历程
 */
class SleepMonitoringReportCourseActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, SleepMonitoringReportCourseActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcSleepMonitoringReportCourseBinding

    private val mFragmentList = mutableListOf<Fragment>()

    private val mTitle by lazy { resources.getStringArray(R.array.string_date_array_txt) }

    override fun initImmersionBar() {}

    override fun getLayoutResId() = R.layout.layout_ac_sleep_monitoring_report_course

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcSleepMonitoringReportCourseBinding
        mBind.apply {
            // title
            ImmersionBar
                .with(this@SleepMonitoringReportCourseActivity)
                .titleBar(toolbar)
                .statusBarDarkFont(false)
                .init()
            includeToolbar.apply {
                titleBackImg.setImageDrawable(
                    ContextCompat.getDrawable(
                        this@SleepMonitoringReportCourseActivity,
                        R.mipmap.src_sleep_course_simple_report_title_back
                    )
                )
                titleBackImg.setOnClickListener { finish() }
                titleContentText.text = getString(R.string.string_sleep_course)
                titleContentText.setTextColor(
                    ContextCompat.getColor(
                        this@SleepMonitoringReportCourseActivity,
                        R.color.white
                    )
                )
            }
            // fragment
            val dayFragment = SleepMonitoringReportCourseDayFragment.newInstance()
            val weekFragment = SleepMonitoringReportCourseWeekFragment.newInstance()
            val monthFragment = SleepMonitoringReportCourseMonthFragment.newInstance()
            val yearFragment = SleepMonitoringReportCourseYearFragment.newInstance()
            mFragmentList.add(dayFragment)
            mFragmentList.add(weekFragment)
            mFragmentList.add(monthFragment)
            mFragmentList.add(yearFragment)
            tabLayout.apply {
                setTabData(mTitle)
                setOnTabSelectListener(object : OnTabSelectListener {
                    override fun onTabSelect(position: Int) {
                        BGPlayerUtils.instance.pause()
                        viewPager.currentItem = position
                    }

                    override fun onTabReselect(position: Int) {
                    }
                })
            }
            viewPager.apply {
                adapter = @SuppressLint("WrongConstant")
                object :
                    FragmentPagerAdapter(
                        supportFragmentManager,
                        BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
                    ) {
                    override fun getCount() = mTitle.size

                    override fun getItem(position: Int) = mFragmentList[position]
                }
                offscreenPageLimit = 1
                addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                    override fun onPageScrolled(
                        position: Int,
                        positionOffset: Float,
                        positionOffsetPixels: Int
                    ) {
                    }

                    override fun onPageSelected(position: Int) {}

                    override fun onPageScrollStateChanged(state: Int) {}
                })
            }
            // 悬浮skuBtn
            stvBtn.setOnClickListener {
                SleepMonitorActivity.startSleepMonitor(this@SleepMonitoringReportCourseActivity)
            }
            Looper.myQueue().addIdleHandler {
                viewPager.offscreenPageLimit = mTitle.size
                false
            }
        }
    }

    override fun initData() {}

    /**
     * 是否显示 - 示例View & 悬浮Btn-开始体验
     *
     * @param mock_status true => 显示这是一份示例报告
     */
    fun showSampleView(mock_status: Boolean) {
        mBind.apply {
            if (mock_status) {
                groupSampleReport.visibility = View.VISIBLE
                stvBtn.visibility = View.VISIBLE
            } else {
                groupSampleReport.visibility = View.GONE
                stvBtn.visibility = View.GONE
            }
        }
    }

    override fun startObserve() {}

    override fun onStop() {
        super.onStop()
        BGPlayerUtils.instance.pause()
    }

}