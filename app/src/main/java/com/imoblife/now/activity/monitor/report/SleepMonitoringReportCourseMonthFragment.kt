package com.imoblife.now.activity.monitor.report

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.SleepMonitorViewModel
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.databinding.LayoutFmSleepMonitoringReportCourseMonthBinding
import com.imoblife.now.mvvm.BaseVMFragment
import com.imoblife.now.util.EmptyUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/28
 * 描   述：睡眠监测报告 - 历程 - 月
 */
class SleepMonitoringReportCourseMonthFragment : BaseVMFragment<SleepMonitorViewModel>() {

    companion object {

        fun newInstance(): SleepMonitoringReportCourseMonthFragment =
            SleepMonitoringReportCourseMonthFragment()

    }

    private lateinit var mBind: LayoutFmSleepMonitoringReportCourseMonthBinding

    // 上次请求服务器返回 —— 左加；右减
    private var mCursor = 0

    override fun getLayoutResId() = R.layout.layout_fm_sleep_monitoring_report_course_month

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutFmSleepMonitoringReportCourseMonthBinding
        mBind.apply {
            // 日期
            titleView.setData(
                Pair(
                    {
                        mViewModel.getSleepRecord(2, mCursor + 1)
                    },
                    {
                        mViewModel.getSleepRecord(2, mCursor - 1)
                    }
                )
            )
        }
    }

    override fun initData() {
        mViewModel.getSleepRecord(2, mCursor)
    }

    override fun startObserve() {
        mViewModel.sleepRecord.observe(viewLifecycleOwner) {
            if (it.isSuccess) {
                it.successData?.let { reportEntity ->
                    mCursor = reportEntity.cursor
                    mBind.apply {
                        // 日期
                        titleView.setTitleTime(reportEntity.report_time)
                        titleView.setShowOrHideImgArrowStart(reportEntity.isNext)
                        titleView.setShowOrHideImgArrowEnd(mCursor != 0)
                        // 睡眠时长
                        cumulativeSleepDurationView.setData(
                            updateMarginTop = true,
                            entity = reportEntity.report.total_sleep_duration
                        )
                        // 睡眠质量
                        sleepCourseSleepQualityView.setData(entity = reportEntity.report.sleep_quality)
                        // 鼾声分析
                        sleepCourseSnoreAnalysisView.setData(reportEntity.report.snoring_list)
                        // 熄灯分析
                        sleepCourseLampOutAnalysisView.setData(
                            entity = reportEntity.report.lights_off_time,
                            isLightsOffTime = true
                        )
                        // 起床时间
                        sleepCourseWakeUpTimeView.setData(
                            noDefaultHeight = true,
                            entity = reportEntity.report.wake_up_time,
                            isLightsOffTime = false
                        )
                        // 环境音
                        sleepCourseAmbientSoundView.setData(reportEntity.report.decibel_list)
                        // 为你推荐
                        if (EmptyUtils.isNotEmpty(reportEntity.report.ad_link)) {
                            SleepCourseRecommendView.visibility = View.VISIBLE
                            SleepCourseRecommendView.setData(reportEntity.report.ad_link)
                        } else {
                            SleepCourseRecommendView.visibility = View.GONE
                        }
                    }
                }
            }
        }
    }

}