package com.imoblife.now.activity.diary

import com.imoblife.now.mvvm.BaseVMActivity
import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.imoblife.now.R
import com.imoblife.now.adapter.DiaryListAdapter
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.Diary
import com.imoblife.now.bean.DiaryGroup
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.ActivityDiaryListBinding
import com.imoblife.now.util.DateUtil
import com.imoblife.now.util.SpUtil
import com.imoblife.now.util.getDecoration
import java.util.concurrent.CopyOnWriteArrayList


class DiaryListActivity : BaseVMActivity<DiaryViewModel>(), View.OnClickListener {
    companion object {
        @JvmStatic
        fun startDiaryListActivity(context: Context) {
            Intent(context, DiaryListActivity::class.java).let {
                context.startActivity(it)
            }
        }
    }

    private var mDiary = 0
    private var mShowDiaryCreateHint = false
    private lateinit var mBind:ActivityDiaryListBinding
    private val mDiaryGroup by lazy {
        CopyOnWriteArrayList<DiaryGroup>()
    }
    private val mDiaryListAdapter by lazy {
        DiaryListAdapter(this, mDiaryGroup,mViewModel)
    }
    private val decoration by lazy {
        getDecoration(this, margin = 16F, top = 16F, left = 16F, right = 16F)
    }

    override fun getLayoutResId() = R.layout.activity_diary_list

    override fun superInit(intent: Intent?) {}

    override fun initVM()=ViewModelProvider(this).get(DiaryViewModel::class.java)

    override fun initView() {
        mBind=mBinding as ActivityDiaryListBinding
        ToolbarUtils.setToolbar(this,getString(R.string.diary_title),NavIconType.BACK,false)
        mBind.ivDiaryCreate.setOnClickListener(this)
        mBind.flGoTop.setOnClickListener(this)
        mBind.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@DiaryListActivity)
            removeItemDecoration(decoration)
            addItemDecoration(decoration)
            adapter = mDiaryListAdapter
        }
        mBind.swipeLayout.apply {
            setOnRefreshListener { initData() }
            setOnLoadMoreListener { mViewModel.getDiary(mDiary) }
            setEnableLoadMore(true)
        }

        mShowDiaryCreateHint = SpUtil.getInstance()
            .getBoolenValue(ConsSp.SP_KEY_DIARY_CREATE_HINT, mShowDiaryCreateHint)
        if (!mShowDiaryCreateHint) {
            mBind.ivDiaryCreateHint.visibility = View.VISIBLE
        } else {
            mBind.ivDiaryCreateHint.visibility = View.INVISIBLE
        }
        mBind.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                //获得recyclerView的线性布局管理器
                val manager = recyclerView.layoutManager as? LinearLayoutManager
                manager?.let {
                    val firstVisibleItemPosition = it.findFirstVisibleItemPosition()
                    // 当不滚动时
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        if (firstVisibleItemPosition <= 10 || mDiaryGroup.size <= 0) {
                            mBind.flGoTop.visibility = View.GONE
                        } else {
                            mBind.flGoTop.visibility = View.VISIBLE
                        }
                    }
                }
            }
        })
    }

    override fun initData() {
        getData()
    }

    private fun getData() {
        mDiary = 0
        mDiaryGroup.clear()
        mViewModel.getDiary(mDiary)
    }

    override fun startObserve() {
        mViewModel.apply {
            mDiaryChangeLiveData.observe(this@DiaryListActivity, { diary->
                try {
                    if (diary is Diary) {
                        if (diary.id==0){
                            val createTime = diary.create_time
                            var hasGroup = false
                            mDiaryGroup.forEach { diaryGroup ->
                                if (DateUtil.formatChineseString(createTime) == DateUtil.formatChineseString(diaryGroup.create_time)) {
                                    diaryGroup.cell.add(0, diary)
                                    hasGroup = true
                                }
                            }
                            if (!hasGroup) {
                                val group = DiaryGroup()
                                group.create_time = diary.create_time
                                val call = CopyOnWriteArrayList<Diary>()
                                call.add(diary)
                                group.cell = call
                                mDiaryGroup.add(0,group)
                            }
                            mDiaryListAdapter.notifyDataChanged()
                            mBind.tvNoData.visibility = View.INVISIBLE
                            getData()
                        }else{
                            mDiaryGroup.forEach { diaryGroup ->
                                diaryGroup?.cell?.forEach {
                                    if (it.id==diary.id){
                                        it.content=diary.content
                                    }
                                }
                            }
                            mDiaryListAdapter.notifyDataChanged()
                        }

                    }
                }catch (e:Throwable){

                }
            })
            diaryLiveData.observe(this@DiaryListActivity, {
                if (it.isSuccess) {
                    val diaryList = it.successData
                    if (diaryList != null) {
                        if (mDiary == 0) {
                            mBind.swipeLayout.finishRefresh()
                        } else {
                            if (diaryList.daily == 0) {
                                mBind.swipeLayout.finishLoadMoreWithNoMoreData()
                            } else {
                                mBind.swipeLayout.finishLoadMore()
                            }
                        }
                        mDiary = diaryList.daily
                        mDiaryGroup.addAll(diaryList.list)
                        mDiaryListAdapter.notifyDataSetChanged()
                    }
                }
                if (mDiaryGroup.size > 0) {
                    mBind.tvNoData.visibility = View.INVISIBLE
                    mBind.swipeLayout.setEnableLoadMore(true)
                    mBind.swipeLayout.setEnableRefresh(true)
                } else {
                    mBind.tvNoData.visibility = View.VISIBLE
                    mBind.swipeLayout.setEnableLoadMore(false)
                    mBind.swipeLayout.setEnableRefresh(false)
                }
            })
            userDeleteDiary.observe(this@DiaryListActivity,{
                if (it.isSuccess){
                    it.successData?.apply {
                        mDiaryGroup.forEachIndexed {indexGroup,diaryGroup ->
                            diaryGroup.cell.forEachIndexed { index, diary ->
                                if (diary.id==first){
                                    diaryGroup.cell.removeAt(index)
                                }
                            }
                            if (diaryGroup.cell.isNullOrEmpty()){
                                mDiaryGroup.removeAt(indexGroup)
                            }
                        }
                        mDiaryListAdapter.notifyDataSetChanged()
                    }
                }
            })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mViewModel.mDiaryChangeLiveData.value=null
    }


    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_diary_create -> {
                DiaryCreateActivity.startDiaryCreateActivity(this)
                if (!mShowDiaryCreateHint) {
                    SpUtil.getInstance().saveBooleanToSp(ConsSp.SP_KEY_DIARY_CREATE_HINT, true)
                    mBind.ivDiaryCreateHint.visibility = View.INVISIBLE
                }
            }
            R.id.fl_go_top -> {
                mBind.recyclerView.scrollToPosition(0)
                mBind.flGoTop.visibility = View.GONE
            }
        }
    }
}