package com.imoblife.now.activity.member

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.net.http.SslError
import android.os.Build
import android.text.TextUtils
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.SslErrorHandler
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.appbar.AppBarLayout
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.OpenUrlHelper
import com.imoblife.now.ActivityStackManager
import com.imoblife.now.R
import com.imoblife.now.activity.ShareActivity
import com.imoblife.now.activity.category.AppBarStatus
import com.imoblife.now.activity.category.AppBarStatusChangeListener
import com.imoblife.now.activity.category.AppBarStatusCollapsed
import com.imoblife.now.activity.category.AppBarStatusExpanded
import com.imoblife.now.activity.category.AppBarStatusIdle
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.SubscribeAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.AdResourceBean
import com.imoblife.now.bean.CommonRouteEntity
import com.imoblife.now.bean.MemberBean
import com.imoblife.now.bean.SubTopEntity
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.constant.ConsUrl
import com.imoblife.now.databinding.ActivitySubscribeNewBinding
import com.imoblife.now.enums.PayWay
import com.imoblife.now.ext.startVipSkuActivity
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.AdResourceUtils
import com.imoblife.now.util.CommonUtil
import com.imoblife.now.util.DeviceUtil
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.util.XLog
import com.imoblife.now.viewmodel.AdViewModel
import com.imoblife.now.viewmodel.PaymentViewModel
import kotlin.math.abs

/**
 * 订阅中心
 */
class SubscribeActivity : BaseVMActivity<PaymentViewModel>() {

    private lateinit var mBind: ActivitySubscribeNewBinding
    private var courseId: Int = 0

    companion object {
        @JvmStatic
        fun openSubscribeActivity(context: Context) {
            openSubscribeActivity(context, 0, 0, 1)
        }

        @JvmStatic
        fun openSubscribeActivity(context: Context, courseId: Int = 0) {
            openSubscribeActivity(context, courseId, 0, 1)
        }

        @JvmStatic
        fun openSubscribeActivity(
            context: Context, courseId: Int = 0, pageOrigin: Int = 0, skuGroup: Int = 1
        ) {
            // 增加全屏跳转路由配置，Now会员课程、超级课程、一卡通课程点击的时候支持可以跳转到不同的全屏页面
            // vip - Now会员课程 - 全屏跳转路由配置
            val vipEntity = ConfigMgr.getInstance().config.vip_full_screen_route
            // sVip - 超级课程 - 全屏跳转路由配置
            val sVipEntity = ConfigMgr.getInstance().config.svip_full_screen_route
            // 一卡通课程 - 全屏跳转路由配置
            val oneCardEntity = ConfigMgr.getInstance().config.one_card_full_screen_route
            // 会员计划 - 全屏跳转路由配置
            val vipPlanEntity = ConfigMgr.getInstance().config.vip_plan_full_screen_route

            if (EmptyUtils.isNotEmpty(sVipEntity)) {
                sVipEntity.pre_course_id = courseId
            }
            if (EmptyUtils.isNotEmpty(vipEntity)) {
                vipEntity.pre_course_id = courseId
            }
            if (EmptyUtils.isNotEmpty(oneCardEntity)) {
                oneCardEntity.pre_course_id = courseId
            }
            if (EmptyUtils.isNotEmpty(vipPlanEntity)) {
                vipPlanEntity.pre_course_id = courseId
            }

            XLog.d(
                "yunyang",
                "yunyang\nvip|Now会员课程 = ${EmptyUtils.isNotEmpty(vipEntity)}\n" + "sVip|超级课程 = ${
                    EmptyUtils.isNotEmpty(sVipEntity)
                }\n" + "一卡通课程 = ${EmptyUtils.isNotEmpty(oneCardEntity)}\n" + "会员计划 = ${
                    EmptyUtils.isNotEmpty(
                        vipPlanEntity
                    )
                }\n" + "pageOrigin = ${pageOrigin}\n" + "skuGroup = ${skuGroup}\n" + "courseId = ${courseId}\n"
            )

            when (pageOrigin) {
                11 -> {
                    if (EmptyUtils.isNotEmpty(vipPlanEntity)) {
                        // 会员计划 - 不同权益的全屏样式配置
                        routeSVipFullScreen(vipPlanEntity)
                    } else goSkuAc(context, courseId, pageOrigin, skuGroup)
                }
                // 8 - 每日瑜伽
                8 -> {
                    if (EmptyUtils.isNotEmpty(sVipEntity) && skuGroup == 2) {
                        // sVip - 不同权益的全屏样式配置
                        routeSVipFullScreen(sVipEntity)
                    } else goSkuAc(context, courseId, pageOrigin, skuGroup)
                }
                // 9 - 拓展进阶「课程详情页 - ProductDetailActivity.java」
                9 -> {
                    if (courseId != 0) {
                        when {
                            EmptyUtils.isNotEmpty(vipEntity) && skuGroup == 1 -> {
                                routeSVipFullScreen(vipEntity)
                            }

                            EmptyUtils.isNotEmpty(sVipEntity) && skuGroup == 2 -> {
                                routeSVipFullScreen(sVipEntity)
                            }

                            else -> goSkuAc(context, courseId, pageOrigin, skuGroup)
                        }
                    } else goSkuAc(context, courseId, pageOrigin, skuGroup)
                }/*
                    16 - 顶部广告条 - 行动营
                    17 - 顶部广告条 - 读书
                    18 - 顶部广告条 - Now运动
                    19 - 顶部广告条 - 每日瑜伽
                    20 - 顶部广告条 - 拓展进阶
                */
                16, 17, 18, 19, 20 -> goSkuAc(context, courseId, pageOrigin, skuGroup)
                // 21 - 行动营详情页面 - 行动营
                21 -> {
                    if (EmptyUtils.isNotEmpty(oneCardEntity) && skuGroup == 2) {
                        // 一卡通 - 不同权益的全屏样式配置
                        routeSVipFullScreen(oneCardEntity)
                    } else goSkuAc(context, courseId, pageOrigin, skuGroup)
                }
                // Tapping
                24 -> {
                    if (ConfigMgr.getInstance().config.isUser_from_tapping) {
                        if (EmptyUtils.isNotEmpty(vipEntity)) {
                            routeSVipFullScreen(vipEntity)
                        } else {
                            goSkuAc(context, courseId, pageOrigin, 1)
                        }
                    } else {
                        if (EmptyUtils.isNotEmpty(sVipEntity)) {
                            routeSVipFullScreen(sVipEntity)
                        } else {
                            goSkuAc(context, courseId, pageOrigin, 2)
                        }
                    }
                }

                else -> {
                    when {
                        // Vip - 不同权益的全屏样式配置
                        EmptyUtils.isNotEmpty(vipEntity) && skuGroup == 1 && (pageOrigin != 0 || courseId != 0) -> {
                            routeSVipFullScreen(vipEntity)
                        }
                        // sVip - 全屏跳转路由配置
                        EmptyUtils.isNotEmpty(sVipEntity) && skuGroup == 2 && (pageOrigin != 0 || courseId != 0) -> {
                            routeSVipFullScreen(sVipEntity)
                        }

                        else -> goSkuAc(context, courseId, pageOrigin, skuGroup)
                    }
                }
            }
        }

        /**
         * 订阅中心路由
         *
         * @param context Context
         * @param courseId Int
         * @param pageOrigin Int
         * @param skuGroup Int
         */
        private fun goSkuAc(
            context: Context, courseId: Int, pageOrigin: Int, skuGroup: Int
        ) {
            if (!TextUtils.isEmpty(ConfigMgr.getInstance().config.ab_test_subscribe_url)) {
                CommonUtil.goNextBannerOrWebView(
                    context, CommonRouteEntity(
                        CommonUtil.TAG_SING,
                        ConfigMgr.getInstance().config.ab_test_subscribe_url,
                        ""
                    )
                )
            } else {
                // 1 - 竖版 2 - 横版
                if (ConfigMgr.getInstance().config.show_template == 1) {
                    Intent(context, SubscribeVerticalActivity::class.java).run {
                        putExtra(ConsIntent.BUNDLE_COURSE_ID, courseId)
                        putExtra(ConsIntent.BUNDLE_VIP_PAGE_ORIGIN, pageOrigin)
                        putExtra(ConsIntent.BUNDLE_VIP_SKU_GROUP, skuGroup)
                        context.startActivity(this)
                    }
                } else {
                    Intent(context, SubscribeActivity::class.java).run {
                        putExtra(ConsIntent.BUNDLE_COURSE_ID, courseId)
                        putExtra(ConsIntent.BUNDLE_VIP_PAGE_ORIGIN, pageOrigin)
                        putExtra(ConsIntent.BUNDLE_VIP_SKU_GROUP, skuGroup)
                        context.startActivity(this)
                    }
                }
            }
        }

        /**
         * 路由全屏
         *
         * @param entity AdResourceBean?
         */
        private fun routeSVipFullScreen(entity: AdResourceBean?) {
            if (EmptyUtils.isNotEmpty(entity)) {
                ActivityStackManager.getInstance().currentActivity.startVipSkuActivity(entity)
            }
        }

    }


    /**
     * 页面来源 =>
     *
     * 0 - 我的
     * 1 - 首页会员
     * 2 - 解压视频
     * 3 - 学习计划
     * 4 - 挑战赛
     * 5 - 睡眠监测底部条
     * 6 - 闹钟铃声设置
     * 7 - 呼吸练习
     * 8 - 每日瑜伽
     * 9 - 拓展进阶
     * 10 - 课程详情
     * 11 - 首页练习｜会员计划
     * 12 - 新课程播放完成页
     * 13 - 练习历程
     * 14 - 木鱼音色
     * 15 - 木鱼图片
     * 16 - 顶部广告条 - 行动营
     * 17 - 顶部广告条 - 读书
     * 18 - 顶部广告条 - Now运动
     * 19 - 顶部广告条 - 每日瑜伽
     * 20 - 顶部广告条 - 拓展进阶
     * 21 - 行动营详情页面 - 行动营
     * 22 - 自由练习
     * 23 - 正念干货
     * 24 - 顶部广告条 - tapping
     * 25 - 人脸检测
     */
    private var pageOrigin: Int = 0
    private var skuSelectGroup = 1
    private val skuGroupNow = 1
    private val skuGroupGather = 2
    private val subNowAdapter by lazy { SubscribeAdapter() }
    private val subUnitAdapter by lazy { SubscribeAdapter() }
    private val adViewModel by viewModels<AdViewModel>()

    //是否选择了联合会员气泡
    private var isSelectTipImg = false

    override fun getLayoutResId() = R.layout.activity_subscribe_new

    override fun superInit(intent: Intent?) {
        if (hasExtra(intent, ConsIntent.BUNDLE_COURSE_ID)) {
            courseId = intent!!.getIntExtra(ConsIntent.BUNDLE_COURSE_ID, 0)
        }
        if (hasExtra(intent, ConsIntent.BUNDLE_VIP_PAGE_ORIGIN)) {
            pageOrigin = intent!!.getIntExtra(ConsIntent.BUNDLE_VIP_PAGE_ORIGIN, 0)
        }
        if (hasExtra(intent, ConsIntent.BUNDLE_VIP_SKU_GROUP)) {
            skuSelectGroup = intent!!.getIntExtra(ConsIntent.BUNDLE_VIP_SKU_GROUP, skuGroupNow)
        }
        //如果用户是Now会员直接跳转PLU栏目
        if (UserMgr.getInstance().isHasNowVip) {
            skuSelectGroup = skuGroupGather
        }
    }

    override fun initVM() = ViewModelProvider(this)[PaymentViewModel::class.java]

    override fun initImmersionBar() {}

    @SuppressLint("SetJavaScriptEnabled")
    override fun initView() {
        pageScreen()
        mBind = mBinding as ActivitySubscribeNewBinding
        mBind.clickProxy = ClickProxy()
        initToolbar()
        mBind.banner.apply {
            addBannerLifecycleObserver(this@SubscribeActivity)
            setAdapter(BannerSubTopAdapter(listOf(SubTopEntity.ShowCardEntity().also { it.member_level = 3 })))
            setBannerGalleryEffect(20, 10, 10, 1.0F)
        }
        UserMgr.getInstance().user?.let {
            mBind.isLogin = true
            mBind.user = it
        } ?: let {
            mBind.isLogin = false
            mBind.noLoginMgs = ConfigMgr.getInstance().config.not_login_msg
        }
        mBind.subNowList.apply {
            addItemDecoration(
                CommonItemDecoration(
                    DisplayUtil.dip2px(12f),
                    DisplayUtil.dip2px(16f),
                    DisplayUtil.dip2px(13f),
                    0,
                    DisplayUtil.dip2px(13f),
                    0
                )
            )
            setItemViewCacheSize(10)
            layoutManager =
                LinearLayoutManager(this@SubscribeActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = subNowAdapter
        }
        mBind.subGatherList.apply {
            addItemDecoration(
                CommonItemDecoration(
                    DisplayUtil.dip2px(12f),
                    DisplayUtil.dip2px(16f),
                    DisplayUtil.dip2px(13f),
                    0,
                    DisplayUtil.dip2px(13f),
                    0
                )
            )
            setItemViewCacheSize(10)
            layoutManager =
                LinearLayoutManager(this@SubscribeActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = subUnitAdapter
        }
//        mBind.subProtocolPrivacy.subProtocolPrivacy()
        mBind.scrollView.setOnScrollChangeListener { _: NestedScrollView?, _: Int, scrollY: Int, _: Int, oldScrollY: Int ->
            val vipViewBottom: Int = mBind.imgBg.bottom
            val vipBottom: Int = mBind.shimmerViewContainer.bottom
            val scroll = scrollY - oldScrollY
            val hideBottom = vipBottom + vipViewBottom
            if (scroll > 0 && scrollY > hideBottom) {
                //向上滑动
                mBind.buySubTxt.visibility = View.VISIBLE
                mBind.buySubLayout.apply {
                    visibility = View.VISIBLE
                    startShimmer()
                }
            } else if (scroll < 0 && scrollY < hideBottom) {
                //向下滑动
                mBind.buySubTxt.visibility = View.GONE
                mBind.buySubLayout.apply {
                    visibility = View.GONE
                    stopShimmer()
                }
            }
        }
        subNowAdapter.setOnItemClickLister { setSkuData(it as Subscribe) }
        subUnitAdapter.setOnItemClickLister { setSkuData(it as Subscribe) }
        mBind.webView.settings.apply {
            userAgentString = userAgentString + "NowMeditation/" + DeviceUtil.getClientVersionName()
            javaScriptEnabled = true
            domStorageEnabled = true
        }
        mBind.webView.apply {
            addJavascriptInterface(AndroidJsInterface(), "android")
            webViewClient = MyWebViewClient()
        }
    }

    /**
     * 初始化Toolbar
     */
    private fun initToolbar() {
        mBind.apply {
            ImmersionBar
                .with(this@SubscribeActivity)
                .titleBar(toolbar)
                .statusBarDarkFont(false)
                .init()
            toolbar.title = null
            toolbarTitle.text = getString(R.string.string_vip_center_txt)
            toolbar.setNavigationOnClickListener { onBackPressed() }
            appBarLayout.addOnOffsetChangedListener(object : AppBarStatusChangeListener() {
                override fun onStateChanged(
                    appBarLayout: AppBarLayout?, state: AppBarStatus, verticalOffset: Int
                ) {
                    when (state) {
                        AppBarStatusCollapsed -> {
                            ImmersionBar
                                .with(this@SubscribeActivity)
                                .titleBar(toolbar)
                                .statusBarDarkFont(true)
                                .init()
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@SubscribeActivity, R.color.white
                            )
                            toolbar.alpha = 1F
                            toolbarTitle.alpha = 1F
                            toolbar.setNavigationIcon(R.mipmap.icon_back)
                            toolbarTitle.setTextColor(
                                ContextCompat.getColor(
                                    this@SubscribeActivity, R.color.color_333333
                                )
                            )
                        }

                        AppBarStatusExpanded -> {
                            ImmersionBar
                                .with(this@SubscribeActivity)
                                .titleBar(toolbar)
                                .statusBarDarkFont(false)
                                .init()
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@SubscribeActivity, R.color.transparent
                            )
                            toolbar.alpha = 1F
                            toolbarTitle.alpha = 1F
                            toolbar.setNavigationIcon(R.mipmap.icon_back_white)
                            toolbarTitle.setTextColor(
                                ContextCompat.getColor(
                                    this@SubscribeActivity, R.color.white
                                )
                            )
                        }

                        AppBarStatusIdle -> {
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@SubscribeActivity, R.color.transparent
                            )
                            val offset = abs(verticalOffset)
                            val max = appBarLayout?.totalScrollRange
                            max?.let {
                                val limit = (max / 2).toFloat()
                                if (offset <= limit) {
                                    toolbar.setNavigationIcon(R.mipmap.icon_back_white)
                                    toolbarTitle.setTextColor(
                                        ContextCompat.getColor(
                                            this@SubscribeActivity, R.color.white
                                        )
                                    )
                                    toolbar.alpha = 1F - offset / limit
                                    toolbarTitle.alpha = 1F - offset / limit
                                } else {
                                    val offsetUp = offset - limit
                                    toolbar.setNavigationIcon(R.mipmap.icon_back)
                                    toolbarTitle.setTextColor(
                                        ContextCompat.getColor(
                                            this@SubscribeActivity, R.color.color_333333
                                        )
                                    )
                                    toolbar.alpha = offsetUp / limit
                                    toolbarTitle.alpha = offsetUp / limit
                                }
                            }
                        }
                    }
                }
            })
        }
    }

    private fun pageScreen() {
        val vipPageOrigin = when {
            AdResourceUtils.advertisingId != 0 -> {
                getString(R.string.string_advertising_resources)
            }

            courseId != 0 -> {
                getString(R.string.string_course_page)
            }

            pageOrigin == 1 -> {
                getString(R.string.string_homepage_member)
            }

            pageOrigin == 2 -> {
                getString(R.string.string_decompress_video)
            }

            pageOrigin == 3 -> {
                getString(R.string.string_study_plan)
            }

            pageOrigin == 4 -> {
                getString(R.string.string_challenge_round)
            }

            pageOrigin == 5 -> getString(R.string.string_sleep_monitoring_bottom_bar)
            pageOrigin == 6 -> getString(R.string.string_set_alarm_clock_txt)
            pageOrigin == 7 -> getString(R.string.breath_title)
            pageOrigin == 8 -> getString(R.string.yoga_course)
            pageOrigin == 9 -> getString(R.string.teacher_course)
            pageOrigin == 10 -> getString(R.string.string_course_details)
            pageOrigin == 11 -> getString(R.string.string_homepage_exercise_txt)
            pageOrigin == 12 -> getString(R.string.string_new_course_playing_completion_page_txt)
            pageOrigin == 13 -> getString(R.string.practice_course)
            pageOrigin == 14 -> getString(R.string.string_timbre_of_wooden_fish)
            pageOrigin == 15 -> getString(R.string.string_wooden_fish_picture)
            pageOrigin == 16 -> getString(R.string.string_top_banner_training_camp)
            pageOrigin == 17 -> getString(R.string.string_top_banner_read)
            pageOrigin == 18 -> getString(R.string.string_top_banner_now_sports)
            pageOrigin == 19 -> getString(R.string.string_top_banner_yoga)
            pageOrigin == 20 -> getString(R.string.string_top_banner_expand_and_advance)
            pageOrigin == 21 -> getString(R.string.string_operation_camp_details_txt)
            pageOrigin == 22 -> getString(R.string.string_free_practice)
            pageOrigin == 23 -> getString(R.string.string_mindful_dry_goods_txt)
            pageOrigin == 24 -> "顶部广告条 - tapping"
            else -> getString(R.string.string_mine_page)
        }
        SensorsDataEvent.subscribeCenterScreen(vipPageOrigin)
    }

    private inner class MyWebViewClient : WebViewClient() {
        override fun shouldOverrideUrlLoading(view: WebView, temUrl: String): Boolean {
            return if (temUrl.startsWith("weixin://")) {
                OpenUrlHelper.openWechat(this@SubscribeActivity)
                true
            } else if (temUrl.startsWith("tel:")) {
                OpenUrlHelper.openPhone(this@SubscribeActivity, temUrl)
                true
            } else if (!temUrl.startsWith("http")) {
                OpenUrlHelper.goBrowser(this@SubscribeActivity, temUrl)
                true
            } else {
                view.loadUrl(temUrl)
                true
            }
        }

        override fun onReceivedSslError(view: WebView, handler: SslErrorHandler, error: SslError) {
            handler.proceed() // 接受所有网站的证书
        }

        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        override fun onReceivedHttpError(
            view: WebView, request: WebResourceRequest, errorResponse: WebResourceResponse
        ) {
            super.onReceivedHttpError(view, request, errorResponse)
            val statusCode = errorResponse.statusCode
            XLog.e("tag", "=========statusCode=======$statusCode")
            if (500 == statusCode) {
                view.loadUrl(ConsUrl.getTimeOutNetworkUrl())
            }
        }

        override fun onReceivedError(
            view: WebView, errorCode: Int, description: String, failingUrl: String
        ) {
            super.onReceivedError(view, errorCode, description, failingUrl)
            // 断网或者网络连接超时
            XLog.e("tag", "=========onReceivedError=======$errorCode")
            if (errorCode == ERROR_HOST_LOOKUP || errorCode == ERROR_CONNECT || errorCode == ERROR_TIMEOUT) {
                view.loadUrl(ConsUrl.getTimeOutNetworkUrl())
            }
        }
    }

    inner class AndroidJsInterface {
        @JavascriptInterface
        fun doLogin() {
            runOnUiThread { LoginCenter.getInstance().loginControl(this@SubscribeActivity) }
        }

        @JavascriptInterface
        fun htmlCallBack(tag: String, next: String) {
            if (TextUtils.isEmpty(tag)) {
                return
            }
            runOnUiThread {
                CommonUtil.goNextBannerOrWebView(
                    this@SubscribeActivity, CommonRouteEntity(tag, next, 0, 0)
                )
            }
        }

        @JavascriptInterface
        fun htmlCallShareUrl(title: String?, content: String?, url: String?, imgUrl: String?) {
            ShareActivity.doShareUrl(this@SubscribeActivity, url, title, content, imgUrl)
        }

        @JavascriptInterface
        fun htmlCallBackUserLogined() {
            if (UserMgr.getInstance().isLogin) {
                mBind.webView.post {
                    mBind.webView.loadUrl(
                        "javascript:userIsLogin('" + UserMgr.getInstance().loginUserId + "')"
                    )
                }
            } else {
                runOnUiThread { LoginCenter.getInstance().loginControl(this@SubscribeActivity) }
            }
        }

        @JavascriptInterface
        fun stopPage() {
            runOnUiThread { onBackPressed() }
        }

        @JavascriptInterface
        fun closePage() {
            runOnUiThread { finish() }
        }

        @JavascriptInterface
        fun refreshData() {
            runOnUiThread { mBind.webView.reload() }
        }
    }

    override fun initData() {
        mViewModel.getSubSkuList(pageOrigin = pageOrigin)
        adViewModel.getSubscribeAd()
    }

    override fun startObserve() {
        mViewModel.apply {
            subSkuList.distinctUntilChanged().observe(this@SubscribeActivity) {
                if (it.isSuccess) {
                    refreshSubscribeInfoUi(it.successData)
                }
            }
            // 订阅中心 - top - banner
            subTopBanner.observe(this@SubscribeActivity) {
                if (it.isSuccess) {
                    it.successData?.let { entity ->
                        if (EmptyUtils.isNotEmpty(entity.show_info)) {
                            ImageLoader.loadImageUrl(this@SubscribeActivity, entity.show_info.top_bg_image, mBind.imgBg)
                        }
                        if (!entity.show_card.isNullOrEmpty()) {
                            mBind.banner.apply {
                                addBannerLifecycleObserver(this@SubscribeActivity)
                                setAdapter(BannerSubTopAdapter(entity.show_card))
                                setBannerGalleryEffect(20, if (entity.show_card.size == 1) 10 else 60, 10, 1.0F)
                            }
                        }
                    }
                }
            }
        }
        adViewModel.subAdResource.observe(this) {
            if (it.isSuccess) {
                mBind.adView.setAdBannerData(it.successData)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBind.webView.post { mBind.webView.loadUrl("javascript:reload_page()") }
    }

    /**
     * 刷新订阅界面
     */
    private fun refreshSubscribeInfoUi(memberBean: MemberBean?) {
        memberBean?.let {
            subNowAdapter.data = it.product_list
            subUnitAdapter.data = it.union_list
            ImageLoader.loadImageUrl(this, it.super_member_redhot, mBind.subGatherTipImg)
            setSubSkuGroup(skuSelectGroup)
            UserMgr.getInstance().user?.let { user ->
                mBind.isLogin = true
                mBind.user = user
                if (UserMgr.getInstance().isVipForever) {
                    mBind.subNowTxt.visibility = View.GONE
                    mBind.subNowList.visibility = View.INVISIBLE
                    setSubSkuGroup(skuGroupGather)
                } else {
                    mBind.subNowTxt.visibility = View.VISIBLE
                    mBind.subNowList.visibility = View.VISIBLE
                    setSubSkuGroup(skuSelectGroup)
                }
            } ?: let {
                mBind.isLogin = false
                mBind.noLoginMgs = ConfigMgr.getInstance().config.not_login_msg
                setSubSkuGroup(skuSelectGroup)
            }
        }
    }

    /**
     * 购买订阅
     *
     * @param subscribe
     */
    private fun doSubscribePay(subscribe: Subscribe?) {
        if (subscribe == null) {
            return
        }
//        if (!UserMgr.getInstance().isLogin) {
//            LoginCenter.getInstance().loginControl(this)
//            return
//        }
        subscribe.params.course_id = courseId
        subscribe.pageOrigin = pageOrigin
        var clone = subscribe.clone()
        if (subscribe.pay_type == PayWay.ZX.value()) {
            clone.pay_type = mBind.payWayView.getPayWay()
        } else {
            clone.pay_type = subscribe.pay_type
        }
        mBind.subProtocolPrivacy.isAgreePrivacy(subscribe, "订阅中心") {
            PayCenter.getInstance().doSubmitPay(this, clone)
        }
    }

    private fun doSubmitPay() {
        if (skuSelectGroup == skuGroupNow) {
            subNowAdapter.selectSubscribe?.let {
                doSubscribePay(it)
            } ?: let {
                ToastUtils.showShortToastCenter("请选择一种订阅类型")
            }
        } else {
            subUnitAdapter.selectSubscribe?.let {
                doSubscribePay(it)
            } ?: let {
                ToastUtils.showShortToastCenter("请选择一种订阅类型")
            }
        }
    }

    private fun setSubSkuGroup(skuGroup: Int) {
        skuSelectGroup = skuGroup
        if (skuGroup == skuGroupNow) {
            if (!isSelectTipImg) {
                mBind.subGatherTipImg.visibility = View.VISIBLE
            }
            mBind.subNowList.visibility = View.VISIBLE
            mBind.subGatherList.visibility = View.INVISIBLE
            mBind.subNowTxt.apply {
                textSize = 16f
                typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                isShaderEnable = true
            }
            mBind.subGatherTxt.apply {
                textSize = 14f
                isShaderEnable = false
                typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }
            if (EmptyUtils.isNotEmpty(subNowAdapter.selectSubscribe)) {
                setSkuData(subNowAdapter.selectSubscribe)
            }
        } else if (skuGroup == skuGroupGather) {
            isSelectTipImg = true
            mBind.subGatherTipImg.visibility = View.GONE
            mBind.subNowList.visibility = View.INVISIBLE
            mBind.subGatherList.visibility = View.VISIBLE
            mBind.subNowTxt.apply {
                textSize = 14f
                isShaderEnable = false
                typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }

            mBind.subGatherTxt.apply {
                textSize = 16f
                typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                isShaderEnable = true
            }
            if (EmptyUtils.isNotEmpty(subUnitAdapter.selectSubscribe)) {
                setSkuData(subUnitAdapter.selectSubscribe)
            }
        }
    }

    /**
     * 设置 Sku 文案
     *
     * @param subscribe entity
     */
    private fun setSkuData(subscribe: Subscribe?) {
        subscribe?.let {
            if (!TextUtils.isEmpty(it.sku_tips)) {
                mBind.subNowVipDescription.visibility = View.VISIBLE
                mBind.subNowVipDescription.text = it.sku_tips
            } else {
                mBind.subNowVipDescription.visibility = View.GONE
            }
            if (!TextUtils.isEmpty(it.sku_img_equity)) {
                mBind.imgMembershipBenefits.visibility = View.VISIBLE
                ImageLoader.loadImageUrl(this, it.sku_img_equity, mBind.imgMembershipBenefits)
            } else {
                mBind.imgMembershipBenefits.visibility = View.GONE
            }
            if (!TextUtils.isEmpty(it.description_url)) {
                mBind.webView.loadUrl(it.description_url)
            }
            if (subscribe.pay_type == PayWay.ZX.value()) {
                mBind.payWayView.visibility = View.VISIBLE
                mBind.payWayView.setPayWay(subscribe.default_pay_way)
            } else {
                mBind.payWayView.visibility = View.GONE
            }
            mBind.subProtocolPrivacy.setAgreementCheckboxFalse()
            mBind.subProtocolPrivacy.setData(
                subscribe, ConfigMgr.getInstance().config.isAuto_vip_privacy_vip
            )
            // updateUI - 按钮文案
            mBind.buySubTxt.text = subscribe.pay_button_title
            mBind.buySubTopTxt.text = subscribe.pay_button_title
            // 选中sku - 刷新banner权益卡片
            mViewModel.getUserVipCard(subscribe.id)
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        when (event?.eventCode) {
            ConsEventCode.LOGIN_CHANGE_EVENT, ConsEventCode.CHANGE_SUBSCRIBE_EVENT -> {
                mViewModel.apply {
                    getSubSkuList(pageOrigin = pageOrigin)
                    // 选中sku - 刷新banner权益卡片
                    if (skuSelectGroup == skuGroupNow) {
                        subNowAdapter.selectSubscribe?.let { getUserVipCard(it.id) }
                    } else {
                        subUnitAdapter.selectSubscribe?.let { getUserVipCard(it.id) }
                    }
                }
            }
        }
    }

    inner class ClickProxy {
        fun onClickView(view: View) {
            when (view.id) {
                R.id.buy_sub_txt, R.id.buy_sub_top_txt -> {
                    doSubmitPay()
                }

                R.id.sub_now_txt -> {
                    setSubSkuGroup(skuGroupNow)
                }

                R.id.sub_gather_txt -> {
                    setSubSkuGroup(skuGroupGather)
                }

                else -> {}
            }
        }
    }

}


