package com.imoblife.now.activity.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.TabBean
import com.imoblife.now.bean.Version
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.repository.VersionRepository

/**
 * =======================================
 * 创建日期:2020/9/29 12:12
 * 作   者:Koala
 * 邮   箱:<EMAIL>
 * 描   述:
 * 使   用:
 * =======================================
 */
class MainModel : BaseViewModel<Any?>() {

    private val versionRepository by lazy { VersionRepository() }
    private val mainRepository by lazy { MainRepository() }
    private val _tabHost = MutableLiveData<UiStatus<List<TabBean>>>()
    val tabHost: LiveData<UiStatus<List<TabBean>>> = _tabHost

    // 升级版本信息
    private val _checkVersion = MutableLiveData<UiStatus<Version>>()
    val checkVersion: LiveData<UiStatus<Version>> = _checkVersion

    fun getTabHost() {
        mainRepository.getTabHost(_tabHost)
    }

    fun checkVersion() {
        versionRepository.checkVersion(_checkVersion)
    }

}
