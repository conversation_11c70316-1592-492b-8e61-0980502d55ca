package com.imoblife.now.activity.faq

import com.imoblife.now.mvvm.BaseViewModel
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.FAQTabBean

class FAQModel : BaseViewModel<Any?>() {
    private val faqRepository by lazy { FAQRepository() }
    val faq by lazy {
        MutableLiveData<UiStatus<MutableList<FAQTabBean>>>()
    }
    fun getFAQ() {
        faqRepository.getFAQ(faq)
    }
}