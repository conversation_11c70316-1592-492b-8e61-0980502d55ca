package com.imoblife.now.activity.breath

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.coorchice.library.SuperTextView
import com.imoblife.now.R
import com.imoblife.now.activity.member.SubscribeActivity
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.BreathModeEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutViewBreathPatternDialogBinding
import com.imoblife.now.databinding.LayoutViewBreathPatternDialogRvItemBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.getBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.SpUtil

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/8/8
 * 描   述：呼吸计时 - 呼吸模式BottomDialog
 */
class BottomBreathPatternDialog(
    mContext: Context,
    // adapter数据
    private var mList: MutableList<BreathModeEntity>? = null
) : Dialog(mContext, R.style.DialogBottom) {

    private var mBind: LayoutViewBreathPatternDialogBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        R.layout.layout_view_breath_pattern_dialog,
        null,
        false
    )

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) { BreathPatternAdapter() }

    // Item_id
    private var mSchemaId = 1

    // 临时数据 - adapterData
    private val mTempList by lazy(LazyThreadSafetyMode.NONE) { mutableListOf<BreathModeEntity>() }

    init {
        mBind.apply {
            setContentView(mBind.root)
            initView()
        }
    }

    private fun initView() {
        mBind.apply {
            clickProxy = ClickProxy()
            recyclerView.apply {
                addItemDecoration(CommonItemDecoration(24.dp, 12.dp, 24.dp, 26.dp, 24.dp, 16.dp))
                adapter = mAdapter
                mList?.let {
                    for (entity in it) {
                        mTempList.add(entity.clone())
                    }
                }
                mAdapter.setNewData(mTempList)
            }
        }

    }

    override fun show() {
        super.show()
        val attributes = window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        window?.setGravity(Gravity.BOTTOM)
        window?.attributes = attributes
        setCancelable(true)
    }

    private inner class BreathPatternAdapter :
        BaseQuickAdapter<BreathModeEntity, BaseViewHolder>(R.layout.layout_view_breath_pattern_dialog_rv_item) {
        @SuppressLint("NotifyDataSetChanged")
        override fun convert(holder: BaseViewHolder, item: BreathModeEntity) {
            holder.getBinding(LayoutViewBreathPatternDialogRvItemBinding::bind).let { layout ->
                layout.tvContent.apply {
                    text = item.title
                    /*
                        会员用户不展示标签，所有的按钮都是“确认”
                        非会员展示标签，免费的是“确认”按钮，付费的是“立即开通体验”按钮
                    */
                    layout.imgTag.visibility = if (item.is_vip == 1) {
                        View.VISIBLE
                    } else {
                        View.GONE
                    }
                    if (UserMgr.getInstance().isHasNowVip) layout.imgTag.visibility = View.GONE
                    if (item.isSelect && item.is_vip == 1 && !UserMgr.getInstance().isHasNowVip) {
                        mBind.updateBtnStatus(false)
                    }
                    if (item.isSelect) {
                        strokeColor = ContextCompat.getColor(mContext, R.color.color_28BDCF)
                        layout.imgEnd.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_breath_pattern_select
                            )
                        )
                    } else {
                        strokeColor = ContextCompat.getColor(mContext, R.color.color_EEEEEE)
                        layout.imgEnd.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_breath_pattern_no_select
                            )
                        )
                    }
                    ImageLoader.loadImageUrl(mContext, item.icon, layout.imgStart)
                    setOnClickListener {
                        data.forEach { entity ->
                            entity.isSelect = false
                        }
                        item.isSelect = true
                        mSchemaId = item.id
                        if (item.is_vip == 0 || UserMgr.getInstance().isHasNowVip) {
                            mBind.updateBtnStatus(true)
                        } else {
                            mBind.updateBtnStatus(false)
                        }
                        notifyDataSetChanged()
                    }
                }
            }
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 确认
                R.id.stvBtnConfirm -> {
                    mList?.clear()
                    mList?.addAll(mTempList)
                    SpUtil.getInstance().saveIntToSp(
                        "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_MODE_SELECT_STATUS}",
                        mSchemaId
                    )
                    if (mBind.stvBtnConfirm.text == context.getString(R.string.string_open_the_experience_now_txt)) {
                        SubscribeActivity.openSubscribeActivity(context, pageOrigin = 7)
                    }
                    dismiss()
                }
                else -> {}
            }
        }

    }

    /**
     * 更新btn - 状态
     */
    private fun LayoutViewBreathPatternDialogBinding.updateBtnStatus(bool: Boolean) {
        stvBtnConfirm.apply {
            if (bool) {
                isShaderEnable = false
                text = context.getString(R.string.string_confirm)
                setTextColor(ContextCompat.getColor(context, R.color.color_ffffff))
            } else {
                isShaderEnable = true
                shaderMode = SuperTextView.ShaderMode.LEFT_TO_RIGHT
                shaderStartColor =
                    ContextCompat.getColor(context, R.color.color_FAD7B1)
                shaderEndColor =
                    ContextCompat.getColor(context, R.color.color_F4B75A)
                text = context.getString(R.string.string_open_the_experience_now_txt)
                setTextColor(ContextCompat.getColor(context, R.color.color_7B4300))
            }
        }
    }

}