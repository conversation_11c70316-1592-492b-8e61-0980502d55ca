package com.imoblife.now.activity.monitor.report

import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Handler
import android.text.TextUtils
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.activity.ShareActivity
import com.imoblife.now.activity.monitor.SetSleepTimeDialogActivity
import com.imoblife.now.activity.monitor.SleepMonitorViewModel
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.MonitoringReportDayEntity
import com.imoblife.now.bean.MonitoringReportSoundCategoryItemEntity
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcSleepMonitoringReportBinding
import com.imoblife.now.databinding.LayoutViewSleepMonitoringShareBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.player.BGPlayerUtils
import com.imoblife.now.util.*
import com.yzq.zxinglibrary.encode.CodeCreator

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/26
 * 描   述：睡眠监测报告
 */
class SleepMonitoringReportActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, SleepMonitoringReportActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcSleepMonitoringReportBinding

    private lateinit var mLoadingHelper: LoadingHelper

    // 睡眠监测报告 - 睡眠声音记录 - Adapter
    private val mSleepSoundRecordingAdapter by lazy(LazyThreadSafetyMode.NONE) {
        SleepSoundRecordingAdapter(
            this
        )
    }

    // 睡眠监测报告 - 睡眠声音记录 - Decoration
    private val mSleepSoundRecordingDecoration by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(16.dp, 16.dp, 14.dp, 16.dp, 14.dp, 0)
    }

    // 睡眠监测报告 - 睡眠声音记录 - list
    private var mSleepSoundRecordingList = mutableListOf<MonitoringReportSoundCategoryItemEntity>()

    // 睡眠监测报告 - 分享 - view
    private var mBindShareView: LayoutViewSleepMonitoringShareBinding? = null

    // skuBtnData
    private var mSkuData: Subscribe? = null

    // 解锁 - 文案
    private var mSkuTitle = ""
    private var mSkuSubtitle = ""
    private var mSkuButton = ""

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .statusBarColor(R.color.color_0C0E1A)
            .statusBarDarkFont(false)
            .fitsSystemWindows(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_sleep_monitoring_report

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mLoadingHelper = LoadingHelper(this)
        mBind = mBinding as LayoutAcSleepMonitoringReportBinding
        ExpandableViewHoldersUtil.getInstance().init().setNeedExplanedOnlyOne(false)
        mBind.apply {
            clickProxy = ClickProxy()
            recyclerViewSleepSoundRecording.apply {
                removeItemDecoration(mSleepSoundRecordingDecoration)
                addItemDecoration(mSleepSoundRecordingDecoration)
                // 清空记录展开还是关闭的缓存数据
                ExpandableViewHoldersUtil.getInstance().resetExpanedList()
                adapter = mSleepSoundRecordingAdapter
            }
            imgBack.setOnClickListener {
                onBackPressed()
            }
        }
    }

    override fun initData() {
        mLoadingHelper.setOnReloadListener {
            DialogUtil.showWaitLoading()
            mViewModel.getMonitoringReport()
        }
        mViewModel.getMonitoringReport()
        mSleepSoundRecordingAdapter.setBlockAction { url, bool ->
            mSleepSoundRecordingList.map { entity ->
                if (!entity.list.isNullOrEmpty()) {
                    entity.list.map { item ->
                        if (item.url == url) {
                            item.isPlayState = bool
                        } else {
                            item.isPlayState = false
                        }
                    }
                }
            }
            mSleepSoundRecordingAdapter.setNewData(mSleepSoundRecordingList)
        }
    }

    override fun startObserve() {
        mViewModel.monitoringReport.observe(this) {
            DialogUtil.hideWaitLoading()
            if (it.isSuccess) {
                it.successData?.let { reportEntity ->
                    mLoadingHelper.showContentView()
                    mBind.apply {
                        // 解锁 - 文案
                        mSkuTitle = reportEntity.sku_title
                        mSkuSubtitle = reportEntity.sku_subtitle
                        mSkuButton = reportEntity.sku_button
                        // skuBtn
                        if (reportEntity.isVip_status) {
                            stvBtnVipUnlockSleepAnalysis.visibility = View.GONE
                        } else {
                            stvBtnVipUnlockSleepAnalysis.visibility = View.VISIBLE
                            mSkuData = reportEntity.sku
                        }
                        reportEntity.report.firstOrNull { itemEntity -> itemEntity.cursor == 0 }
                            ?.let { entity ->
                                entity.isSelectState = true
                                updateUIData(entity, Triple(mSkuTitle, mSkuSubtitle, mSkuButton))
                            }
                    }
                } ?: mLoadingHelper.showEmptyView()
            } else mLoadingHelper.showErrorView()
        }
    }

    /**
     * 选中 - 天 - 更新UIPage
     */
    private fun updateUIData(
        item: MonitoringReportDayEntity,
        triple: Triple<String, String, String>
    ) {
        mBind.apply {
            entity = item
            // 睡眠状态
            sleepCourseSleepStateView.setData(item.data.sleep_status)
            // 鼾声分析
            sleepCourseSnoreAnalysisView.setData(
                item.data.snoring_list,
                mSkuData,
                triple,
                isReportPage = true,
                showDataAnalysisView = false
            )
            // 睡眠声音记录
            if (!item.data.sound_list.data.isNullOrEmpty()) {
                rivSleepSoundRecordingImg.visibility = View.GONE
                recyclerViewSleepSoundRecording.visibility = View.VISIBLE
                mSleepSoundRecordingList.clear()
                mSleepSoundRecordingList.addAll(item.data.sound_list.data)
                mSleepSoundRecordingAdapter.setNewData(item.data.sound_list.data)
            } else {
                rivSleepSoundRecordingImg.visibility = View.VISIBLE
                recyclerViewSleepSoundRecording.visibility = View.GONE
            }
            // 分享
            if (!TextUtils.isEmpty(item.data.share_url) && UserMgr.getInstance().isLogin) {
                imgSleepQualityShare.visibility = View.VISIBLE
                prepareShareView(item)
            } else {
                imgSleepQualityShare.visibility = View.GONE
            }
            executePendingBindings()
        }
    }

    /**
     * 分享 - 睡眠监测 - prepare
     */
    private fun prepareShareView(item: MonitoringReportDayEntity) {
        mBindShareView = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.layout_view_sleep_monitoring_share,
            null,
            false
        )
        mBindShareView?.apply {
            sleepCourseSleepStateView.setData(entity = item.data.sleep_status, isReportPage = true)
            UserMgr.getInstance().user?.let {
                userEntity = it
                entity = item
                setQRData(item.data.share_url)
            }
        }
    }

    /**
     * 填充数据 - 二维码
     *
     * @receiver LayoutViewCommonShareBinding
     * @param urlQR 二维码
     */
    private fun LayoutViewSleepMonitoringShareBinding.setQRData(urlQR: String) {
        val logo = BitmapFactory.decodeResource(resources, R.mipmap.ic_launcher)
        val bitmap = CodeCreator.createQRCode(urlQR, 300, 300, logo)
        imgQR.setImageBitmap(bitmap)
        executePendingBindings()
        mBindShareView?.root?.apply {
            mBind.fmContainer.addView(this)
            mBind.fmContainer.visibility = View.INVISIBLE
            post {
                BitmapUtil.layoutView(
                    this,
                    DeviceUtil.getScreenSize(this@SleepMonitoringReportActivity)[0],
                    DeviceUtil.getScreenSize(this@SleepMonitoringReportActivity)[1]
                )
            }
        }
    }

    /**
     * 分享 - 睡眠监测
     */
    private fun sleepMonitoringShareView() {
        Handler().post {
            mBindShareView?.root?.let {
                val bitmap = BitmapUtil.loadBitmapFromView(it)
                bitmap?.apply {
                    ShareActivity.shareBitmap(
                        this@SleepMonitoringReportActivity,
                        bitmap,
                        false,
                        1
                    )
                }
            }
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 睡眠质量 - 分享
                R.id.imgSleepQualityShare -> sleepMonitoringShareView()
                // 解锁高级睡眠分析
                R.id.stvBtnVipUnlockSleepAnalysis -> mSkuData?.let {
                    PayCenter.getInstance().doSubmitPay(this@SleepMonitoringReportActivity, it)
                }
                // 睡眠评分 - 睡眠评分是如何得出的
                R.id.tvSleepQualityTxt -> {
                    SleepMonitoringReportCourseRuleDialog.showDialog(
                        this@SleepMonitoringReportActivity,
                        getString(R.string.string_sleep_score_txt),
                        getString(R.string.string_sleep_score_content_txt)
                    )
                }
                else -> {}
            }
        }

    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.CHANGE_SUBSCRIBE_EVENT) {
            mViewModel.getMonitoringReport()
        }
    }

    override fun onStop() {
        super.onStop()
        BGPlayerUtils.instance.pause()
    }

    override fun onBackPressed() {
        if (SpUtil.getInstance().getIntValue(ConsCommon.SET_SLEEP_STATUS, 0) == 0) {
            finish()
            val count =
                SpUtil.getInstance().getIntValue(ConsCommon.SET_SLEEP_STATUS_CLOSE_MECHANISM, 0)
            if (count < 3) {
                SpUtil.getInstance()
                    .saveIntToSp(ConsCommon.SET_SLEEP_STATUS_CLOSE_MECHANISM, count + 1)
                SetSleepTimeDialogActivity.startActivity(this)
                overridePendingTransition(R.anim.enter_anim_slide, R.anim.exit_anim_slide)
            }
        } else {
            super.onBackPressed()
        }
    }

}