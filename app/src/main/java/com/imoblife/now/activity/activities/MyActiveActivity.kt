package com.imoblife.now.activity.activities

import android.content.Context
import android.content.Intent
import android.os.Looper
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager.widget.ViewPager
import com.flyco.tablayout.listener.OnTabSelectListener
import com.imoblife.now.R
import com.imoblife.now.adapter.TitlePageAdapter
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.databinding.LayoutActivitysBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.mvvm.BaseVMActivity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/8/18
 * 描   述：我的活动 - 三切Fm => 待开始 & 进行中 & 已结束
 */
class MyActiveActivity : BaseVMActivity<ActiveViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(context: Context) {
            Intent(context, MyActiveActivity::class.java).let {
                context.startActivity(it)
            }
        }

    }

    private lateinit var mBind: LayoutActivitysBinding

    private val mTitle by lazy(LazyThreadSafetyMode.NONE) {
        resources.getStringArray(R.array.string_my_active_title).toList()
    }

    private val mTabFragmentList by lazy(LazyThreadSafetyMode.NONE) {
        listOf(
            // 未开始
            MyActiveFragment.newInstance(1),
            // 进行中
            MyActiveFragment.newInstance(2),
            // 已结束
            MyActiveFragment.newInstance(3)
        )
    }

    private val mPageAdapter by lazy(LazyThreadSafetyMode.NONE) {
        TitlePageAdapter(supportFragmentManager, mTabFragmentList, mTitle)
    }

    override fun getLayoutResId() = R.layout.layout_activitys

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(ActiveViewModel::class.java)

    override fun initView() {
        ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_my_active_txt),
            NavIconType.BACK,
            false
        )
        mBind = mBinding as LayoutActivitysBinding
        mBind.apply {
            viewPager.apply {
                offscreenPageLimit = 1
                adapter = mPageAdapter
                addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                    override fun onPageScrolled(
                        position: Int,
                        positionOffset: Float,
                        positionOffsetPixels: Int,
                    ) {
                    }

                    override fun onPageSelected(position: Int) {
                        slidingTabLayout.currentTab = position
                        for (i in 0 until mPageAdapter.count) {
                            val isSelect = i == position
                            slidingTabLayout.getTitleView(i).textSize = if (isSelect) 18F else 14F
                        }
                    }

                    override fun onPageScrollStateChanged(state: Int) {}
                })
            }
            slidingTabLayout.apply {
                indicatorWidth = 20.dp.toFloat()
                setViewPager(viewPager)
                setSnapOnTabClick(true)
                setOnTabSelectListener(object : OnTabSelectListener {
                    override fun onTabSelect(position: Int) {
                        viewPager.currentItem = position
                    }

                    override fun onTabReselect(position: Int) {}
                })
                getTitleView(0).textSize = 18F
            }
        }
        mBind.viewPager.currentItem = 1
        Looper.myQueue().addIdleHandler {
            mBind.viewPager.offscreenPageLimit = mTitle.size
            false
        }
    }

    override fun initData() {
    }

    override fun startObserve() {
    }

}