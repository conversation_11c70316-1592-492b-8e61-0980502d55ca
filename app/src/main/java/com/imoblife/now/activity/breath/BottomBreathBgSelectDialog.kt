package com.imoblife.now.activity.breath

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.graphics.drawable.Drawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.BreathBgEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutViewBreathBgSelectDialogBinding
import com.imoblife.now.databinding.LayoutViewBreathBgSelectDialogRvItemBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.getBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.SpUtil

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/8/8
 * 描   述：呼吸计时 - 背景选择BottomDialog
 */
class BottomBreathBgSelectDialog(
    mContext: Context,
    // adapter数据
    private val mList: MutableList<BreathBgEntity>? = null,
    // 确认按钮回调
    private val mBlock: ((imgUrl: String) -> Unit)? = null
) : Dialog(mContext, R.style.DialogBottom) {

    private var mBind: LayoutViewBreathBgSelectDialogBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        R.layout.layout_view_breath_bg_select_dialog,
        null,
        false
    )

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) { BreathPatternAdapter() }

    // item_imgUrl
    private var selectItemImgUrl = ""

    // Item_id
    private var mId = 1

    // 临时数据 - adapterData
    private val mTempList by lazy(LazyThreadSafetyMode.NONE) { mutableListOf<BreathBgEntity>() }

    init {
        mBind.apply {
            setContentView(mBind.root)
            initView()
        }
    }

    private fun initView() {
        mBind.apply {
            clickProxy = ClickProxy()
            recyclerView.apply {
                addItemDecoration(CommonItemDecoration(54.dp, 16.dp, 30.dp, 34.dp, 30.dp, 30.dp))
                adapter = mAdapter
                mList?.let {
                    for (entity in it) {
                        mTempList.add(entity.clone())
                    }
                }
                mAdapter.setNewData(mTempList)
            }
        }

    }

    override fun show() {
        super.show()
        val attributes = window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        window?.setGravity(Gravity.BOTTOM)
        window?.attributes = attributes
        setCancelable(true)
    }

    private inner class BreathPatternAdapter :
        BaseQuickAdapter<BreathBgEntity, BaseViewHolder>(R.layout.layout_view_breath_bg_select_dialog_rv_item) {
        @SuppressLint("NotifyDataSetChanged")
        override fun convert(holder: BaseViewHolder, item: BreathBgEntity) {
            holder.getBinding(LayoutViewBreathBgSelectDialogRvItemBinding::bind).let { layout ->
                layout.tvTitle.text = item.title
                layout.stvBg.apply {
                    if (item.isSelect) {
                        strokeWidth = 1.dp.toFloat()
                        strokeColor = ContextCompat.getColor(mContext, R.color.color_28BDCF)
                        isShowState2 = true
                        drawable2 =
                            ContextCompat.getDrawable(mContext, R.mipmap.src_breath_pattern_select)
                    } else {
                        strokeWidth = 0F
                        strokeColor = ContextCompat.getColor(mContext, R.color.transparent)
                        isShowState2 = false
                    }
                    Glide
                        .with(mContext)
                        .load(item.imageUrl)
                        .set<WebpFrameCacheStrategy>(
                            WebpFrameLoader.FRAME_CACHE_STRATEGY,
                            WebpFrameCacheStrategy.AUTO
                        )
                        .into(object : SimpleTarget<Drawable>() {
                            override fun onResourceReady(
                                resource: Drawable,
                                transition: Transition<in Drawable>?
                            ) {
                                drawable = resource
                            }
                        })
                    setOnClickListener {
                        data.forEach { entity ->
                            entity.isSelect = false
                        }
                        item.isSelect = true
                        mId = item.id
                        selectItemImgUrl = item.imageUrl
                        notifyDataSetChanged()
                    }
                }
            }
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 完成
                R.id.tvComplete -> {
                    mList?.clear()
                    mList?.addAll(mTempList)
                    SpUtil.getInstance().saveIntToSp(
                        "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_BG_SELECT_STATUS}",
                        mId
                    )
                    mBlock?.invoke(selectItemImgUrl)
                    dismiss()
                }
                else -> {}
            }
        }

    }

}