package com.imoblife.now.activity.monitor

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：睡眠闹钟 - CallBack
 */
interface StartSleepAlarmClockCallBack {

    /**
     * checkBox 选中状态
     *
     * @param isChecked 选中状态
     */
    fun checkBoxIsChecked(isChecked: Boolean)

    /**
     * seekBar 进度改变
     *
     * @param progress 进度
     */
    fun seekBarProgressChanged(progress: Int)

}

interface AudioFrequencyCallBack {

    /**
     * 闹钟铃声｜轻唤醒场景音
     * 选择某一音频
     */
    fun selectRingingTone(type: Int, url: String)

}