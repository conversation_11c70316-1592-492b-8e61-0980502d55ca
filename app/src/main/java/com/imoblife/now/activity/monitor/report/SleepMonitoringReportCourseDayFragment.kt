package com.imoblife.now.activity.monitor.report

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.SleepMonitorViewModel
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.MonitoringReportSoundCategoryItemEntity
import com.imoblife.now.databinding.LayoutFmSleepMonitoringReportCourseDayBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.mvvm.BaseVMFragment
import com.imoblife.now.player.BGPlayerUtils
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ExpandableViewHoldersUtil

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/27
 * 描   述：睡眠监测报告 - 历程 - 天
 */
class SleepMonitoringReportCourseDayFragment : BaseVMFragment<SleepMonitorViewModel>() {

    companion object {

        fun newInstance(): SleepMonitoringReportCourseDayFragment =
            SleepMonitoringReportCourseDayFragment()

    }

    private lateinit var mBind: LayoutFmSleepMonitoringReportCourseDayBinding

    // 睡眠监测报告 - 睡眠声音记录 - Adapter
    private val mSleepSoundRecordingAdapter by lazy(LazyThreadSafetyMode.NONE) {
        SleepSoundRecordingAdapter(
            this
        )
    }

    // 睡眠监测报告 - 睡眠声音记录 - Decoration
    private val mSleepSoundRecordingDecoration by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(16.dp, 16.dp, 14.dp, 16.dp, 14.dp, 0)
    }

    // 上次请求服务器返回 —— 左加；右减
    private var mCursor = 0

    // 睡眠监测报告 - 睡眠声音记录 - list
    private var mSleepSoundRecordingList = mutableListOf<MonitoringReportSoundCategoryItemEntity>()

    override fun getLayoutResId() = R.layout.layout_fm_sleep_monitoring_report_course_day

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutFmSleepMonitoringReportCourseDayBinding
        ExpandableViewHoldersUtil.getInstance().init().setNeedExplanedOnlyOne(true)
        mBind.apply {
            // 日期
            titleView.setData(
                Pair(
                    {
                        BGPlayerUtils.instance.pause()
                        mViewModel.getSleepRecord(0, mCursor + 1)
                    },
                    {
                        BGPlayerUtils.instance.pause()
                        mViewModel.getSleepRecord(0, mCursor - 1)
                    }
                )
            )
            recyclerViewSleepSoundRecording.apply {
                removeItemDecoration(mSleepSoundRecordingDecoration)
                addItemDecoration(mSleepSoundRecordingDecoration)
                // 清空记录展开还是关闭的缓存数据
                ExpandableViewHoldersUtil.getInstance().resetExpanedList()
                adapter = mSleepSoundRecordingAdapter
            }
        }
    }

    override fun initData() {
        mViewModel.getSleepRecord(0, mCursor)
        mSleepSoundRecordingAdapter.setBlockAction { url, bool ->
            mSleepSoundRecordingList.map { entity ->
                if (!entity.list.isNullOrEmpty()) {
                    entity.list.map { item ->
                        if (item.url == url) {
                            item.isPlayState = bool
                        } else {
                            item.isPlayState = false
                        }
                    }
                }
            }
            mSleepSoundRecordingAdapter.setNewData(mSleepSoundRecordingList)
        }
    }

    override fun onFragmentResume() {
        super.onFragmentResume()
        // 切换天、周、月、年时间节点，刷新睡眠声音播放状态
        if (mSleepSoundRecordingList.isNotEmpty()) mSleepSoundRecordingAdapter.setNewData(
            mSleepSoundRecordingList
        )
    }

    override fun startObserve() {
        mViewModel.sleepRecord.observe(viewLifecycleOwner) {
            if (it.isSuccess) {
                it.successData?.let { reportEntity ->
                    // 是否显示 - 示例View
                    (requireActivity() as SleepMonitoringReportCourseActivity).showSampleView(
                        reportEntity.isMock_status
                    )
                    mCursor = reportEntity.cursor
                    mBind.apply {
                        // 日期
                        titleView.setTitleTime(reportEntity.report_time)
                        titleView.setShowOrHideImgArrowStart(reportEntity.isNext)
                        titleView.setShowOrHideImgArrowEnd(mCursor != 0)
                        // 睡眠状态
                        sleepCourseSleepStateView.setData(
                            entity = reportEntity.report.sleep_status,
                            updateMarginTop = true
                        )
                        // 鼾声分析
                        sleepCourseSnoreAnalysisView.setData(reportEntity.report.snoring_list)
                        // 睡眠声音记录
                        if (!reportEntity.report.sound_list.data.isNullOrEmpty()) {
                            rivSleepSoundRecordingImg.visibility = View.GONE
                            recyclerViewSleepSoundRecording.visibility = View.VISIBLE
                            mSleepSoundRecordingList.clear()
                            mSleepSoundRecordingList.addAll(reportEntity.report.sound_list.data)
                            mSleepSoundRecordingAdapter.setNewData(reportEntity.report.sound_list.data)
                        } else {
                            rivSleepSoundRecordingImg.visibility = View.VISIBLE
                            recyclerViewSleepSoundRecording.visibility = View.GONE
                        }
                        // 为你推荐
                        if (EmptyUtils.isNotEmpty(reportEntity.report.ad_link)) {
                            SleepCourseRecommendView.visibility = View.VISIBLE
                            SleepCourseRecommendView.setData(reportEntity.report.ad_link)
                        } else {
                            SleepCourseRecommendView.visibility = View.GONE
                        }
                    }
                }
            }
        }
    }

}