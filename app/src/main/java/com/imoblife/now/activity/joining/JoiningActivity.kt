package com.imoblife.now.activity.joining

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.ConcatAdapter
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.adapter.JoiningAdapter
import com.imoblife.now.adapter.JoiningEmptyAdapter
import com.imoblife.now.adapter.JoiningTitleAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.FoundCourse
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityJoiningBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.DisplayUtil

/**
 * 版   权：纳沃科技@版权所有
 * 创建日期：2021/7/28 9:23
 * 创 建 者：TUS
 * 描   述：正在参加
 */
class JoiningActivity : BaseVMActivity<JoinViewModel>() {

    // 神策数据 - 当前页面id
    private var mPageId = 0

    // 神策数据 - 当前页面 - 分类id
    private var mCategoryId = 0

    companion object {

        @JvmStatic
        fun startActivity(context: Context, pageId: Int, categoryId: Int = 0) {
            Intent(context, JoiningActivity::class.java).let {
                it.putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId)
                it.putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId)
                context.startActivity(it)
            }
        }

    }

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
                it.let {
                    mPageId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0)
                }
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
                it.let {
                    mCategoryId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0)
                }
            }
        }
    }

    override fun initVM() = ViewModelProvider(
        this,
        ViewModelProvider.NewInstanceFactory()
    ).get(JoinViewModel::class.java)

    private lateinit var mBind: ActivityJoiningBinding

    override fun getLayoutResId() = R.layout.activity_joining

    private val myJoinData = mutableListOf<FoundCourse>()
    private val recommendData = mutableListOf<FoundCourse>()
    private val joiningEmptyAdapter by lazy { JoiningEmptyAdapter() }
    private val joiningTitleAdapter by lazy { JoiningTitleAdapter(mutableListOf(getString(R.string.string_all_join_txt))) }
    private val joiningAdapter = JoiningAdapter(myJoinData, "Joining")
    private val recommendAdapter = JoiningAdapter(recommendData, "Recommend")
    private val concatAdapter by lazy {
        ConcatAdapter(
            joiningEmptyAdapter,
            joiningAdapter,
            joiningTitleAdapter,
            recommendAdapter
        )
    }

    override fun initView() {
        mBind = mBinding as ActivityJoiningBinding
        ToolbarUtils.setToolbar(this, getString(R.string.taking_part_in), NavIconType.BACK, false)
        mBind.apply {
            swipeLayout.setOnRefreshListener { mViewModel.getUserJoining(mPageId, mCategoryId) }
            recyclerView.apply {
                adapter = concatAdapter
                addItemDecoration(
                    CommonItemDecoration(
                        0,
                        DisplayUtil.dip2px(13f),
                        DisplayUtil.dip2px(13f),
                        DisplayUtil.dip2px(13f),
                        DisplayUtil.dip2px(13f),
                        DisplayUtil.dip2px(13f)
                    )
                )
            }
        }
    }

    override fun initData() {
        mViewModel.getUserJoining(mPageId, mCategoryId)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.userJoiningActive.observe(this, Observer { uiStatus ->
            mBind.swipeLayout.finishRefresh()
            if (uiStatus.isSuccess) {
                uiStatus.successData?.apply {
                    if (my.isNullOrEmpty()) {
                        joiningEmptyAdapter.setNewData(mutableListOf("null"))
                    } else {
                        myJoinData.clear()
                        myJoinData.addAll(my)
                        joiningAdapter.notifyDataSetChanged()
                        joiningEmptyAdapter.setNewData(mutableListOf<String>())
                    }
                    recommend?.let {
                        recommendData.clear()
                        recommendData.addAll(recommend)
                        recommendAdapter.notifyDataSetChanged()
                    }
                }
            }
        })
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.LOGIN_CHANGE_EVENT ||
            event?.eventCode == ConsEventCode.CHANGE_SUBSCRIBE_EVENT
        ) {
            mViewModel.getUserJoining(mPageId, mCategoryId)
        }
    }

}