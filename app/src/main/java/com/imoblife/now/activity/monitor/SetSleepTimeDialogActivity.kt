package com.imoblife.now.activity.monitor

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.databinding.LayoutAcSetSleepTimeDialogBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import com.imoblife.now.util.DateUtil
import com.imoblife.now.util.NotificationUtil
import com.imoblife.now.util.SpUtil
import com.imoblife.now.view.dialog.OpenNotificationDialog

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/6/1
 * 描   述：睡眠监测 - 设置入睡时间 - dialog - activity
 */
class SetSleepTimeDialogActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, SetSleepTimeDialogActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcSetSleepTimeDialogBinding

    override fun getLayoutResId() = R.layout.layout_ac_set_sleep_time_dialog

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentBar()
            .init()
    }

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(NoViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcSetSleepTimeDialogBinding
        mBind.apply {
            clickProxy = ClickProxy()
            numberPickerStart.value = SpUtil.getInstance()
                .getIntValue(ConsCommon.SET_SLEEP_TIME_HOUR, DateUtil.getCurrentTimeH())
            numberPickerEnd.value = SpUtil.getInstance()
                .getIntValue(ConsCommon.SET_SLEEP_TIME_MINUTE, DateUtil.getCurrentTimeMinutes())
            checkBox.setOnCheckedChangeListener { _, isChecked ->
                stvBtn.isEnabled = isChecked
                if (isChecked) {
                    stvBtn.shaderStartColor =
                        ContextCompat.getColor(
                            this@SetSleepTimeDialogActivity,
                            R.color.color_2EE3FF
                        )
                    stvBtn.shaderEndColor =
                        ContextCompat.getColor(
                            this@SetSleepTimeDialogActivity,
                            R.color.color_58A8FB
                        )
                } else {
                    stvBtn.shaderStartColor =
                        ContextCompat.getColor(
                            this@SetSleepTimeDialogActivity,
                            R.color.color_7D7D7D
                        )
                    stvBtn.shaderEndColor =
                        ContextCompat.getColor(
                            this@SetSleepTimeDialogActivity,
                            R.color.color_6A6A6A
                        )
                }
            }
        }
    }

    override fun initData() {
    }

    override fun startObserve() {
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                R.id.tvCancel -> finish()
                R.id.stvBtn -> {
                    if (NotificationUtil.isNotificationEnable(this@SetSleepTimeDialogActivity)) {
                        saveBuzzer()
                    } else {
                        OpenNotificationDialog().showDialog(
                            this@SetSleepTimeDialogActivity,
                            ConsRequestCode.SEND_REQUEST_SETTING_NOTIFY
                        )
                    }
                }
                else -> {}
            }
        }

    }

    /**
     * 设置提醒
     */
    private fun saveBuzzer() {
        if (NotificationUtil.isNotificationEnable(this@SetSleepTimeDialogActivity)) {
            mBind.apply {
                val hour = numberPickerStart.value
                val minute = numberPickerEnd.value
                SpUtil.getInstance().saveIntToSp(ConsCommon.SET_SLEEP_TIME_HOUR, hour)
                SpUtil.getInstance().saveIntToSp(ConsCommon.SET_SLEEP_TIME_MINUTE, minute)
                SpUtil.getInstance().saveStringToSp(ConsCommon.SET_SLEEP_TIME,"${hour}:${minute}")
                UserMgr.getInstance().practiceTime(1, hour, minute, 2)
                finish()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == ConsRequestCode.SEND_REQUEST_SETTING_NOTIFY) {
            saveBuzzer()
        }
    }

}