package com.imoblife.now.activity.nature

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityNatureListBinding
import com.imoblife.now.fragment.sleep.NatureAdapter
import com.imoblife.now.fragment.sleep.SleepViewModel
import com.imoblife.now.util.DisplayUtil

/**
 * 版   权：纳沃科技@版权所有
 * 创建日期：2021/9/26 15:50
 * 创 建 者：TUS
 * 描   述：大自然声音
 */
class NatureListActivity : BaseVMActivity<SleepViewModel>() {

    // 神策数据 - 当前页面id
    private var mPageId = 0

    // 神策数据 - 当前页面 - 分类id
    private var mCategoryId = 0

    private lateinit var mBind:ActivityNatureListBinding
//    private val multiTypeAdapter by lazy { MultiTypeAdapter() }
    private val natureAdapter by lazy { NatureAdapter() }
    private lateinit var loadingHelper: LoadingHelper

    companion object {

        @JvmStatic
        fun openNatureActivity(context: Context, pageId: Int, categoryId: Int) {
            Intent(context, NatureListActivity::class.java).apply {
                putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId)
                putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId)
                context.startActivity(this)
            }
        }

    }

    override fun getLayoutResId() = R.layout.activity_nature_list

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
                it.let {
                    mPageId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0)
                }
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
                it.let {
                    mCategoryId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0)
                }
            }
        }
    }

    override fun initVM() = ViewModelProvider(
        this,
        ViewModelProvider.NewInstanceFactory()
    ).get(SleepViewModel::class.java)


    override fun initView() {
        loadingHelper=ToolbarUtils.setToolbar(this,"大自然声",NavIconType.BACK,true)
        mBind= mBinding as ActivityNatureListBinding
        mBind.swipeLayout.setOnRefreshListener {
            mViewModel.getNatureListData(mPageId, mCategoryId)
        }
        mBind.recycler.apply {
            addItemDecoration(CommonItemDecoration(
                DisplayUtil.dip2px(20f),
                DisplayUtil.dip2px(20f),
                DisplayUtil.dip2px(10f),
                DisplayUtil.dip2px(10f),
                DisplayUtil.dip2px(10f),
                DisplayUtil.dip2px(10f),
            ))
            layoutManager=GridLayoutManager(this@NatureListActivity,4)
            adapter=natureAdapter
        }
    }

    override fun initData() {
        loadingHelper.setOnReloadListener {
            mViewModel.getNatureListData(mPageId, mCategoryId)
        }
        loadingHelper.showLoadingView()
        mViewModel.getNatureListData(mPageId, mCategoryId)
    }

    override fun startObserve() {
        mViewModel.natureListData.observe(this) {
            mBind.swipeLayout.finishRefresh()
            loadingHelper.showContentView()
            if (it.isSuccess) {
                natureAdapter.setNewData(it.successData)
            } else {
                loadingHelper.showErrorView()
            }
        }
    }

}