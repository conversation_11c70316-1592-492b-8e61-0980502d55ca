<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>熊猫睡眠 隐私政策</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #3498db;
        }
        
        h3 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        
        .version-info {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        
        .important {
            font-weight: bold;
            color: #e74c3c;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        ul, ol {
            padding-left: 25px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        .contact-info {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 5px;
            margin-top: 30px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>熊猫睡眠 隐私政策</h1>
        
        <div class="version-info">
            <p><strong>此协议同时适用于Android、iOS、Harmony</strong></p>
            <p>版本发布日期：2025 年 7 月 9 日</p>
            <p>版本生效日期：2025 年 7 月 9 日</p>
        </div>

        <div class="highlight">
            <h3>【提示条款】</h3>
            <p>您的信任对我们非常重要，我们深知个人信息对您的重要性，我们将按法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：<span class="important">权责一致原则、目的明确原则、选择同意原则、最小必要原则、确保安全原则、主体参与原则、公开透明原则</span>等。</p>
            
            <p>请您在使用熊猫睡眠的各项功能或服务前，仔细阅读并透彻理解本政策，特别是以<span class="important">粗体/下划线</span>标识的条款，请您务必重点阅读，在确认充分理解并同意后再开始使用。</p>
        </div>

        <div class="section">
            <h2>第一部分 定义</h2>
            <ul>
                <li><strong>熊猫睡眠：</strong>指西安纳沃信息科技有限公司提供的互联网信息及软件技术产品和服务，包括熊猫睡眠APP以及随技术发展出现的新形态向您提供的各项产品和服务。</li>
                <li><strong>用户：</strong>指所有直接或间接获取和使用熊猫睡眠的使用者，包括自然人、法人或其他组织等。在本协议中称为"用户"或称"您"。</li>
                <li><strong>服务提供者：</strong>指熊猫睡眠的互联网信息及软件技术服务提供者。本产品的服务提供者为西安纳沃信息科技有限公司。</li>
                <li><strong>熊猫睡眠账户：</strong>指您创建的熊猫睡眠账户。</li>
                <li><strong>个人信息：</strong>指以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。</li>
                <li><strong>个人敏感信息：</strong>指包括<span class="important">身份证件号码、个人生物识别信息、银行账号、财产信息、行踪轨迹、健康生理信息、交易信息、18周岁以下（含）儿童信息</span>等的个人信息。</li>
            </ul>
        </div>

        <div class="section">
            <h2>第二部分 隐私政策</h2>
            
            <h3>一、我们如何收集和使用您的个人信息</h3>
            
            <h4>（一）基本功能服务</h4>
            <p>我们提供的基本功能服务包括：<strong>免费助眠音试听</strong>，我们需要收集您的设备信息，具体包括：</p>
            <ul>
                <li>设备名称、设备型号</li>
                <li>设备识别码（包含 IDFA、IDFV、AndroidID、MAC、OAID、Advertising ID、Serial Number、UAID、ICCID、硬件序列号UUID）</li>
                <li>移动应用列表</li>
                <li>操作系统和应用程序版本</li>
                <li>语言设置、唯一设备标识符</li>
                <li>运营商网络类型等软硬件特征信息</li>
                <li>网络信息</li>
            </ul>
            
            <h4>（二）扩展功能服务</h4>
            
            <h5>1、账户注册及登录</h5>
            <ul>
                <li><strong>手机号注册：</strong>您可以通过手机号码创建账号，并完善相关的个人资料（包括头像、昵称）</li>
                <li><strong>第三方登录：</strong>如您使用第三方账号（包括微信）进行注册、登录，您需授权我们获取您在第三方平台注册的公开信息（头像、昵称）</li>
            </ul>
            
            <h5>2、睡眠数据记录</h5>
            <p>当您在熊猫睡眠有睡眠记录时，我们会记录您的睡眠数据（包括<span class="important">入睡时间、起床时间、睡眠声音记录</span>），便于您查阅历史睡眠数据。</p>
            
            <h5>3、客户服务</h5>
            <p>当您联系我们的客服时，我们可能会收集：</p>
            <ul>
                <li>您与我们的沟通记录</li>
                <li>您的账户信息、订单信息</li>
                <li>您为了证明相关事实而提供的图片/视频/文字信息</li>
                <li>您的联系方式</li>
            </ul>

            <h5>4、支付功能</h5>
            <p>当您在熊猫睡眠购买商品或服务时，我们将会收集：</p>
            <ul>
                <li><strong>支付方式：</strong>微信支付、支付宝支付、苹果支付</li>
                <li><strong>支付信息：</strong>第三方支付账号信息、交易流水号、交易金额、交易时间</li>
            </ul>

            <h5>5、账户安全和系统运行安全</h5>
            <p>为了保障您的账户安全与系统运行安全，我们会收集：</p>
            <ul>
                <li><strong>设备信息：</strong>设备名称、设备型号、设备识别码等</li>
                <li><strong>日志信息：</strong>IP地址、设备识别码、搜索或浏览的信息等</li>
            </ul>

            <h4>（三）APP获取的权限</h4>

            <h5>1、存储空间权限</h5>
            <p>在您上传图片时，经过您的授权，我们会将您主动上传的视频、图片加密上传到服务器进行外置存储。</p>

            <h5>2、相册权限</h5>
            <p>您在上传照片时，可通过开启相册的权限进行操作，我们会将您主动上传的照片、视频加密上传到服务器进行存储。</p>

            <h5>3、相机权限</h5>
            <p>您在拍摄上传照片时，需要您授权开启设备的相机权限。</p>

            <h5>4、麦克风权限</h5>
            <p>您在使用客服的语音输入功能时，需要您授权开启您的麦克风权限。</p>

            <h5>5、网络权限</h5>
            <p>您开通该权限后，熊猫睡眠可以访问或获取WLAN和本地蜂窝网络，以保证您正常使用本软件。</p>

            <h5>6、通知权限</h5>
            <p>您开通该权限后，我们即可向您推送相关消息提醒。</p>
        </div>

        <div class="section">
            <h3>二、我们如何共享、转让、公开披露您的个人信息</h3>

            <h4>（一）委托处理</h4>
            <p>为了提高效率、降低成本，我们可能会委托关联方代表我们来处理信息。我们的关联方（受托方）包括：</p>
            <ul>
                <li><strong>应用内推送服务的授权合作伙伴：</strong>用于创建内部用户编号以帮助我们更好地进行精准的信息推送</li>
                <li><strong>统计服务的授权合作伙伴：</strong>用于帮助我们分析产品功能的使用情况，以更好地改进我们的产品和/或服务</li>
            </ul>

            <h4>（二）共享</h4>
            <p>我们不会与其他任何公司、组织和个人分享您的个人信息，除非获得您的明确同意。目前，我们需要您授权同意共享的情形如下：</p>
            <ol>
                <li>在获取明确同意的情况下共享</li>
                <li>在法定情形下的共享</li>
                <li>只有共享您的信息，才能实现我们的产品与/或服务的核心功能</li>
                <li>与授权合作伙伴共享</li>
            </ol>

            <p>目前，我们的合作伙伴包括以下类型：</p>
            <ul>
                <li><strong>第三方SDK类合作方</strong></li>
                <li><strong>商品或技术服务的供应商和其他合作伙伴</strong></li>
                <li><strong>广告服务的合作伙伴</strong></li>
            </ul>

            <h4>（三）转让</h4>
            <p>我们不会将您的个人信息转让给任何公司、组织和个人，但以下情况除外：</p>
            <ol>
                <li>在获取您明确同意的情况下转让</li>
                <li>如果我们合并、分立、解散或破产清算等原因需要转移个人信息的</li>
            </ol>

            <h4>（四）公开披露</h4>
            <p>我们仅会在以下情况下，公开披露您的个人信息：</p>
            <ol>
                <li>获得您明确同意或基于您的主动选择</li>
                <li>如果我们确定您出现违反法律法规或严重违反熊猫睡眠相关协议及规则的情况</li>
                <li>在法律、法律程序、诉讼或政府主管部门强制性要求的情况下</li>
            </ol>
        </div>

        <div class="section">
            <h3>三、我们如何存储您的个人信息</h3>

            <h4>（一）信息存储的地点</h4>
            <p>我们会按照法律法规规定，将从中华人民共和国境内收集的用户个人信息存储于中华人民共和国境内。</p>

            <h4>（二）信息存储的期限</h4>
            <p>我们只会在达成本政策所述目的所需的期限内保留您的个人信息，法律法规有强制性留存要求的情况除外。我们判断个人信息的存储期限主要依据以下标准：</p>
            <ol>
                <li>完成与您相关的交易目的、维护相应交易及业务记录</li>
                <li>保证我们为您提供服务的安全和质量</li>
                <li>您是否同意更长的留存期间</li>
                <li>根据诉讼时效的相关需要</li>
                <li>是否存在关于保留期限的其他特别约定或法律法规规定</li>
            </ol>
        </div>

        <div class="section">
            <h3>四、我们如何保护您的个人信息</h3>

            <p>我们已使用符合业界标准的安全防护措施保护您提供的个人信息，防止数据遭到未经授权访问、公开披露、使用、修改、损坏或丢失。我们采取的措施包括：</p>
            <ul>
                <li>对我们网站提供https安全浏览方式</li>
                <li>使用加密技术确保您个人信息相关数据的保密性</li>
                <li>使用受信赖的保护机制防止数据遭到恶意攻击</li>
                <li>部署访问控制机制，确保只有授权人员才可访问个人信息</li>
                <li>举办安全和隐私保护培训课程</li>
            </ul>

            <p class="important">我们已按照国家网络安全等级保护三级的要求完成测评要求并取得备案。</p>

            <h4>账户安全风险防范：</h4>
            <p>请您妥善保护自己的个人信息，除非必要，不向他人披露您的账号信息。<span class="important">无论何时,请不要向任何人(包括自称是熊猫睡眠客服的人士)透露您收到的验证码。</span></p>
        </div>

        <div class="section">
            <h3>五、您的权利</h3>

            <p>按照中国相关的法律、法规、标准，以及其他国家、地区的通行做法，我们保障您对自己的个人信息行使以下权利：</p>

            <h4>（一）访问您的个人信息</h4>
            <p>您可以在【个人】-【设置】-【个人信息】中访问、修改、管理您的头像、昵称、性别、年龄。</p>

            <h4>（二）更正您的个人信息</h4>
            <p>当您发现我们处理的关于您的个人信息有错误时，您有权要求我们做出更正。</p>

            <h4>（三）删除您的个人信息</h4>
            <p>在以下情形中，您可以向我们提出删除个人信息的请求：</p>
            <ol>
                <li>如果我们处理个人信息的行为违反法律法规</li>
                <li>如果我们收集、使用您的个人信息，却未征得您的同意</li>
                <li>如果我们处理个人信息的行为违反了与您的约定</li>
                <li>如果您不再使用我们的产品或服务，或您注销了账户</li>
                <li>如果我们不再为您提供产品或服务</li>
            </ol>

            <h4>（四）改变您授权同意的范围</h4>
            <p>您可以通过修改设备的个人设置中的授权项目改变您授权同意的范围。若您想要撤回您对本政策的同意授权，您可以通过【个人】-【关于】-【撤回隐私政策授权】自行操作。</p>

            <h4>（五）个人信息主体注销账户</h4>
            <p>您随时可注销账户，【个人】-【关于】-【注销账号】自行操作，您的账户将立即注销。<span class="important">您的账户一旦注销将无法恢复，请您谨慎操作。</span></p>

            <h4>（六）个人信息主体获取个人信息副本</h4>
            <p>您有权复制您的个人信息，我们将在15天内回复您的获取副本请求。</p>
        </div>

        <div class="section">
            <h3>六、第三方产品/服务提供者</h3>
            <p>为方便您的访问并丰富您的体验，可能会有第三方提供的产品或服务。但我们对于第三方提供的产品或服务没有控制权。您在使用第三方产品或服务过程中的信息保护问题，不适用于本政策的管理。</p>
        </div>

        <div class="section">
            <h3>七、我们如何使用 Cookie 和同类技术</h3>

            <h4>（一）Cookie</h4>
            <p>为确保网站正常运转，我们会在您的计算机或移动设备上存储名为 Cookie 的小数据文件。您可以清除熊猫睡眠上保存的所有 Cookie，大部分网络浏览器都设有阻止 Cookie 的功能。</p>

            <h4>（二）网站信标和像素标签</h4>
            <p>除Cookie外，我们还会在网站上使用网站信标和像素标签等其他同类技术，帮助我们了解您的功能或服务偏好并改善客户服务。</p>
        </div>

        <div class="section">
            <h3>八、我们如何处理未成年人的个人信息</h3>

            <p class="important">我们深知保护未成年人信息的重要性，我们仅对已满18周岁的成年人提供服务。我们不会故意收集未成年人的任何个人信息。不满18周岁的未成年人不得创建自己的熊猫睡眠账户。</p>

            <p>对于可能涉及的不满14周岁的儿童个人信息，这些信息属于个人敏感信息，我们进一步采取以下措施予以保护：</p>
            <ol>
                <li>严格遵循法律法规的要求进行存储、使用、披露</li>
                <li>指定专人负责儿童个人信息保护事宜</li>
                <li>您有权随时访问和更正您提供的儿童个人信息</li>
            </ol>
        </div>

        <div class="section">
            <h3>九、本隐私政策如何更新</h3>

            <p>我们的个人信息保护政策可能变更。您可以通过点击【个人】-【关于】查看最新的协议。</p>

            <p>未经您明确同意，我们不会削减您按照本个人信息保护政策所应享有的权利。对于重大变更，我们还会提供更为显著的通知。</p>

            <p>本政策所指的重大变更包括但不限于：</p>
            <ol>
                <li>我们的服务模式发生重大变化</li>
                <li>我们在所有权结构、组织架构等方面发生重大变化</li>
                <li>个人信息共享、转让或公开披露的主要对象发生变化</li>
                <li>您参与个人信息处理方面的权利及其行使方式发生重大变化</li>
                <li>我们负责处理个人信息安全的责任部门、联络方式及投诉渠道发生变化时</li>
                <li>个人信息安全影响评估报告表明存在高风险时</li>
            </ol>
        </div>

        <div class="contact-info">
            <h3>十、如何联系我们</h3>
            <p>如对本隐私政策和用户隐私保护相关事宜有任何问题，您可以通过以下任意一种方式进行反馈：</p>
            <ul>
                <li><strong>客服电话：</strong>(+86) 173 9162 7228</li>
                <li><strong>邮箱：</strong><EMAIL></li>
            </ul>
            <p>在收到您的反馈后，我们将在15个工作日内给您回复。</p>
        </div>

        <div class="section">
            <h3>十一、附则</h3>

            <p>本政策的解释及争议解决均应适用中华人民共和国法律。与本政策相关的任何纠纷，双方应经友好协商解决；协商不成的，您同意将纠纷或争议提交至西安仲裁委员会在西安仲裁解决。</p>

            <p>如果有管辖权的任何法院裁定或判决本政策的任何条款无效，则该条款将从本政策中移除，但该条款的无效不影响本政策其余条款的效力。</p>
        </div>

        <div class="footer">
            <p>© 2025 西安纳沃信息科技有限公司 版权所有</p>
            <p>本隐私政策最后更新时间：2025年7月9日</p>
        </div>
    </div>

    <script>
        // 添加一些交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为重要条款添加高亮效果
            const importantElements = document.querySelectorAll('.important');
            importantElements.forEach(function(element) {
                element.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#ffebee';
                    this.style.padding = '2px 4px';
                    this.style.borderRadius = '3px';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.padding = '0';
                });
            });

            // 添加目录导航功能
            const headers = document.querySelectorAll('h2, h3');
            let tocHtml = '<div style="position: fixed; top: 20px; right: 20px; background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 200px; z-index: 1000; display: none;" id="toc">';
            tocHtml += '<h4 style="margin-top: 0;">目录</h4>';

            headers.forEach(function(header, index) {
                const id = 'section-' + index;
                header.id = id;
                const level = header.tagName === 'H2' ? 0 : 1;
                const indent = level * 15;
                tocHtml += `<div style="margin-left: ${indent}px; margin-bottom: 5px;"><a href="#${id}" style="text-decoration: none; color: #3498db; font-size: 12px;">${header.textContent}</a></div>`;
            });

            tocHtml += '</div>';
            document.body.insertAdjacentHTML('beforeend', tocHtml);

            // 添加显示/隐藏目录的按钮
            const tocButton = document.createElement('button');
            tocButton.innerHTML = '目录';
            tocButton.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #3498db; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; z-index: 1001;';
            document.body.appendChild(tocButton);

            const toc = document.getElementById('toc');
            tocButton.addEventListener('click', function() {
                if (toc.style.display === 'none' || toc.style.display === '') {
                    toc.style.display = 'block';
                    tocButton.style.display = 'none';
                } else {
                    toc.style.display = 'none';
                }
            });

            // 点击目录外部时隐藏目录
            document.addEventListener('click', function(e) {
                if (!toc.contains(e.target) && e.target !== tocButton) {
                    toc.style.display = 'none';
                    tocButton.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>
