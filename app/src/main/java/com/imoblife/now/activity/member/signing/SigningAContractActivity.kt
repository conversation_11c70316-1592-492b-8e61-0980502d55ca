package com.imoblife.now.activity.member.signing

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutAcObSigningAContractBinding
import com.imoblife.now.ext.animAlphaShow
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.ext.signPulseAnimator
import com.imoblife.now.ext.startVipSkuTemplatedActivity
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import com.imoblife.now.util.DateUtil
import com.jaychang.st.SimpleText
import org.libpag.PAGFile
import org.libpag.PAGImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/7/5
 * 描   述：ob - 过渡 - 让我们签约
 */
class SigningAContractActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, SigningAContractActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcObSigningAContractBinding

    override fun getLayoutResId() = R.layout.layout_ac_ob_signing_a_contract

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .statusBarDarkFont(true)
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(NoViewModel::class.java)

    @SuppressLint("ClickableViewAccessibility")
    override fun initView() {
        mBind = mBinding as LayoutAcObSigningAContractBinding
        mBind.apply {
            // 签约
            groupSigningAContract.visibility = View.VISIBLE
            tvContentCenter.text = SimpleText
                .from(
                    getString(
                        R.string.string_necessary_measures_to_achieve_my_goals_txt,
                        DateUtil.getCurrentDate()
                    )
                )
                .first(DateUtil.getCurrentDate())
                .textColor(R.color.color_EC1154)
            val file = PAGFile.Load(assets, "extend_guide_contract.pag")
            file.setDuration(2_000_000)
            pagImg.composition = file
            pagImg.setRepeatCount(1)
            pagImg.addListener(object : PAGImageView.PAGImageViewListener {
                override fun onAnimationStart(p0: PAGImageView?) {
                }

                override fun onAnimationEnd(p0: PAGImageView?) {
                    p0?.let {
                        if (it.currentFrame() == it.numFrames() - 1) {
                            groupSigningAContract.visibility = View.GONE
                            pagImgCheckMark.animAlphaShow(durationTime = 50L)
                            pagImgCheckMark.play()
                            tvContent.animAlphaShow()
                            tvDesc.animAlphaShow(onEnd = {
                                pagImgCheckMark.visibility = View.GONE
                                tvContent.visibility = View.GONE
                                tvDesc.visibility = View.GONE
                                goSecondObSubAc()
                            }, durationTime = 1100L)
                        }
                    }
                }

                override fun onAnimationCancel(p0: PAGImageView?) {
                }

                override fun onAnimationRepeat(p0: PAGImageView?) {
                }

                override fun onAnimationUpdate(p0: PAGImageView?) {
                }
            })
            imgSkip.onDebounceClickListener {
                groupSigningAContract.visibility = View.GONE
                goSecondObSubAc()
            }
            val scaleY = ObjectAnimator.ofFloat(imgBtn, "scaleY", 1F, 1.1F, 1F)
            val scaleX = ObjectAnimator.ofFloat(imgBtn, "scaleX", 1F, 1.1F, 1F)
            scaleY.repeatCount = -1
            scaleX.repeatCount = -1
            val animSet = AnimatorSet()
            animSet.apply {
                playTogether(scaleY, scaleX)
                duration = 1000L
            }
            imgBtn.setOnLongClickListener {
                pagImg.play()
                animSet.start()
                true
            }

            // 做的好！
            pagImgCheckMark.composition = PAGFile.Load(assets, "extend_guide_well_done.pag")
            pagImgCheckMark.setRepeatCount(1)

            // 仅对你可见 即将开启体验！
            tvExperience.text = SimpleText
                .from(getString(R.string.string_day_experience_txt))
                .first(getString(R.string.string_experience_txt))
                .textColor(R.color.color_EC1154)
        }
    }

    /**
     * 路由第二次ob订阅页面
     *
     * @receiver LayoutAcObSigningAContractBinding
     */
    private fun LayoutAcObSigningAContractBinding.goSecondObSubAc() {
        tvExperience.animAlphaShow()
        tvExperience.signPulseAnimator {
            startVipSkuTemplatedActivity(isSecondOb = 1)
            finish()
        }
    }

    override fun initData() {}

    override fun startObserve() {}

    override fun onBackPressed() {}

}