package com.imoblife.now.activity.member

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.net.http.SslError
import android.os.Build
import android.text.TextUtils
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.SslErrorHandler
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.appbar.AppBarLayout
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.OpenUrlHelper
import com.imoblife.now.R
import com.imoblife.now.activity.ShareActivity
import com.imoblife.now.activity.category.AppBarStatus
import com.imoblife.now.activity.category.AppBarStatusChangeListener
import com.imoblife.now.activity.category.AppBarStatusCollapsed
import com.imoblife.now.activity.category.AppBarStatusExpanded
import com.imoblife.now.activity.category.AppBarStatusIdle
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.CommonRouteEntity
import com.imoblife.now.bean.MemberBean
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.constant.ConsUrl
import com.imoblife.now.databinding.LayoutAcSubscribeVerticalSkuBinding
import com.imoblife.now.enums.PayWay
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.AdResourceUtils
import com.imoblife.now.util.CommonUtil
import com.imoblife.now.util.DeviceUtil
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.util.XLog
import com.imoblife.now.viewmodel.AdViewModel
import com.imoblife.now.viewmodel.PaymentViewModel
import kotlin.math.abs

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/6/16
 * 描   述：订阅｜会员中心 - 竖SkuList
 */
class SubscribeVerticalActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        @JvmStatic
        fun openSubscribeActivity(context: Context) {
            openSubscribeActivity(context, 0, 0, 1)
        }

        @JvmStatic
        fun openSubscribeActivity(context: Context, courseId: Int = 0) {
            openSubscribeActivity(context, courseId, 0, 1)
        }

        @JvmStatic
        fun openSubscribeActivity(
            context: Context,
            courseId: Int = 0,
            pageOrigin: Int = 0,
            skuGroup: Int = 1
        ) {
            Intent(context, SubscribeVerticalActivity::class.java).run {
                putExtra(ConsIntent.BUNDLE_COURSE_ID, courseId)
                putExtra(ConsIntent.BUNDLE_VIP_PAGE_ORIGIN, pageOrigin)
                putExtra(ConsIntent.BUNDLE_VIP_SKU_GROUP, skuGroup)
                context.startActivity(this)
            }
        }

    }

    private var mMemberBean: MemberBean? = null

    private lateinit var mBind: LayoutAcSubscribeVerticalSkuBinding

    private var courseId: Int = 0

    /**
     * 页面来源 =>
     *
     * 0 - 我的
     * 1 - 首页会员
     * 2 - 解压视频
     * 3 - 学习计划
     * 4 - 挑战赛
     * 5 - 睡眠监测底部条
     * 6 - 闹钟铃声设置
     * 7 - 呼吸练习
     * 8 - 每日瑜伽
     * 9 - 拓展进阶
     * 10 - 课程详情
     * 11 - 首页练习｜会员计划
     * 12 - 新课程播放完成页
     * 13 - 练习历程
     * 14 - 木鱼音色
     * 15 - 木鱼图片
     * 16 - 顶部广告条 - 行动营
     * 17 - 顶部广告条 - 读书
     * 18 - 顶部广告条 - Now运动
     * 19 - 顶部广告条 - 每日瑜伽
     * 20 - 顶部广告条 - 拓展进阶
     * 21 - 行动营详情页面 - 行动营
     * 22 - 自由练习
     * 23 - 正念干货
     * 24 - 顶部广告条 - tapping
     * 25 - 人脸检测
     */
    private var pageOrigin: Int = 0
    private var skuSelectGroup = 1
    private val skuGroupNow = 1
    private val skuGroupGather = 2
    private val subNowAdapter by lazy { SubscribeVerticalAdapter() }
    private val subUnitAdapter by lazy { SubscribeVerticalAdapter() }
    private val adViewModel by viewModels<AdViewModel>()

    //是否选择了联合会员气泡
    private var isSelectTipImg = false

    // Now - 展示｜折叠状态
    private var mExpandNowState = false

    // NowPlus - 展示｜折叠状态
    private var mExpandNowPlusState = false

    private val mTitle by lazy(LazyThreadSafetyMode.NONE) { resources.getStringArray(R.array.string_vip_sku_txt) }

    override fun getLayoutResId() = R.layout.layout_ac_subscribe_vertical_sku

    override fun superInit(intent: Intent?) {
        if (hasExtra(intent, ConsIntent.BUNDLE_COURSE_ID)) {
            courseId = intent!!.getIntExtra(ConsIntent.BUNDLE_COURSE_ID, 0)
        }
        if (hasExtra(intent, ConsIntent.BUNDLE_VIP_PAGE_ORIGIN)) {
            pageOrigin = intent!!.getIntExtra(ConsIntent.BUNDLE_VIP_PAGE_ORIGIN, 0)
        }
        if (hasExtra(intent, ConsIntent.BUNDLE_VIP_SKU_GROUP)) {
            skuSelectGroup = intent!!.getIntExtra(ConsIntent.BUNDLE_VIP_SKU_GROUP, skuGroupNow)
        }
        //如果用户是Now会员直接跳转PLUS栏目
        if (UserMgr.getInstance().isHasNowVip) {
            skuSelectGroup = skuGroupGather
        }
    }

    override fun initVM() = ViewModelProvider(this).get(PaymentViewModel::class.java)

    override fun initImmersionBar() {}

    @SuppressLint("SetJavaScriptEnabled")
    override fun initView() {
        pageScreen()
        mBind = mBinding as LayoutAcSubscribeVerticalSkuBinding
        mBind.clickProxy = ClickProxy()
        initToolbar()
        UserMgr.getInstance().user?.let {
            mBind.isLogin = true
            mBind.user = it
        } ?: let {
            mBind.isLogin = false
            mBind.noLoginMgs = ConfigMgr.getInstance().config.not_login_msg
        }
        mBind.subNowList.apply {
            addItemDecoration(
                CommonItemDecoration(
                    DisplayUtil.dip2px(4f),
                    DisplayUtil.dip2px(4f),
                    DisplayUtil.dip2px(13f),
                    0,
                    DisplayUtil.dip2px(13f),
                    DisplayUtil.dip2px(10f)
                )
            )
            layoutManager =
                LinearLayoutManager(
                    this@SubscribeVerticalActivity,
                    LinearLayoutManager.VERTICAL,
                    false
                )
            adapter = subNowAdapter
        }
        mBind.subGatherList.apply {
            addItemDecoration(
                CommonItemDecoration(
                    DisplayUtil.dip2px(4f),
                    DisplayUtil.dip2px(4f),
                    DisplayUtil.dip2px(13f),
                    0,
                    DisplayUtil.dip2px(13f),
                    DisplayUtil.dip2px(10f)
                )
            )
            layoutManager =
                LinearLayoutManager(
                    this@SubscribeVerticalActivity,
                    LinearLayoutManager.VERTICAL,
                    false
                )
            adapter = subUnitAdapter
        }
//        mBind.subProtocolPrivacy.subProtocolPrivacy()
        subNowAdapter.setOnItemClickLister { data, position ->
            setSkuData(data as Subscribe, position)
        }
        subUnitAdapter.setOnItemClickLister { data, position ->
            setSkuData(data as Subscribe, position)
        }
        mBind.webView.settings.apply {
            userAgentString = userAgentString + "NowMeditation/" + DeviceUtil.getClientVersionName()
            javaScriptEnabled = true
            domStorageEnabled = true
        }
        mBind.webView.apply {
            addJavascriptInterface(AndroidJsInterface(), "android")
            webViewClient = MyWebViewClient()
        }
    }

    /**
     * 初始化Toolbar
     */
    private fun initToolbar() {
        mBind.apply {
            ImmersionBar
                .with(this@SubscribeVerticalActivity)
                .titleBar(toolbar)
                .statusBarDarkFont(true)
                .init()
            toolbar.title = null
            toolbarTitle.text = getString(R.string.string_vip_center_txt)
            toolbar.setNavigationOnClickListener { onBackPressed() }
            appBarLayout.addOnOffsetChangedListener(object : AppBarStatusChangeListener() {
                override fun onStateChanged(
                    appBarLayout: AppBarLayout?,
                    state: AppBarStatus,
                    verticalOffset: Int
                ) {
                    when (state) {
                        AppBarStatusCollapsed -> {
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@SubscribeVerticalActivity,
                                R.color.white
                            )
                            toolbar.alpha = 1F
                            toolbarTitle.alpha = 1F
                            toolbar.setNavigationIcon(R.mipmap.icon_back)
                            toolbarTitle.setTextColor(
                                ContextCompat.getColor(
                                    this@SubscribeVerticalActivity,
                                    R.color.color_333333
                                )
                            )
                        }
                        AppBarStatusExpanded -> {
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@SubscribeVerticalActivity,
                                R.color.transparent
                            )
                            toolbar.alpha = 1F
                            toolbarTitle.alpha = 1F
                            toolbar.setNavigationIcon(R.mipmap.icon_back_white)
                            toolbarTitle.setTextColor(
                                ContextCompat.getColor(
                                    this@SubscribeVerticalActivity,
                                    R.color.white
                                )
                            )
                        }
                        AppBarStatusIdle -> {
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@SubscribeVerticalActivity,
                                R.color.transparent
                            )
                            val offset = abs(verticalOffset)
                            val max = appBarLayout?.totalScrollRange
                            max?.let {
                                val limit = (max / 2).toFloat()
                                if (offset <= limit) {
                                    toolbar.setNavigationIcon(R.mipmap.icon_back_white)
                                    toolbarTitle.setTextColor(
                                        ContextCompat.getColor(
                                            this@SubscribeVerticalActivity,
                                            R.color.white
                                        )
                                    )
                                    toolbar.alpha = 1F - offset / limit
                                    toolbarTitle.alpha = 1F - offset / limit
                                } else {
                                    val offsetUp = offset - limit
                                    toolbar.setNavigationIcon(R.mipmap.icon_back)
                                    toolbarTitle.setTextColor(
                                        ContextCompat.getColor(
                                            this@SubscribeVerticalActivity,
                                            R.color.color_333333
                                        )
                                    )
                                    toolbar.alpha = offsetUp / limit
                                    toolbarTitle.alpha = offsetUp / limit
                                }
                            }
                        }
                    }
                }
            })
        }
    }

    private fun pageScreen() {
        val vipPageOrigin = when {
            AdResourceUtils.advertisingId != 0 -> {
                getString(R.string.string_advertising_resources)
            }
            courseId != 0 -> {
                getString(R.string.string_course_page)
            }
            pageOrigin == 1 -> {
                getString(R.string.string_homepage_member)
            }
            pageOrigin == 2 -> {
                getString(R.string.string_decompress_video)
            }
            pageOrigin == 3 -> {
                getString(R.string.string_study_plan)
            }
            pageOrigin == 4 -> {
                getString(R.string.string_challenge_round)
            }
            pageOrigin == 5 -> getString(R.string.string_sleep_monitoring_bottom_bar)
            pageOrigin == 6 -> getString(R.string.string_set_alarm_clock_txt)
            pageOrigin == 7 -> getString(R.string.breath_title)
            pageOrigin == 8 -> getString(R.string.yoga_course)
            pageOrigin == 9 -> getString(R.string.teacher_course)
            pageOrigin == 10 -> getString(R.string.string_course_details)
            pageOrigin == 11 -> getString(R.string.string_homepage_exercise_txt)
            pageOrigin == 12 -> getString(R.string.string_new_course_playing_completion_page_txt)
            pageOrigin == 13 -> getString(R.string.practice_course)
            pageOrigin == 14 -> getString(R.string.string_timbre_of_wooden_fish)
            pageOrigin == 15 -> getString(R.string.string_wooden_fish_picture)
            pageOrigin == 16 -> getString(R.string.string_top_banner_training_camp)
            pageOrigin == 17 -> getString(R.string.string_top_banner_read)
            pageOrigin == 18 -> getString(R.string.string_top_banner_now_sports)
            pageOrigin == 19 -> getString(R.string.string_top_banner_yoga)
            pageOrigin == 20 -> getString(R.string.string_top_banner_expand_and_advance)
            pageOrigin == 21 -> getString(R.string.string_operation_camp_details_txt)
            pageOrigin == 22 -> getString(R.string.string_free_practice)
            pageOrigin == 23 -> getString(R.string.string_mindful_dry_goods_txt)
            pageOrigin == 24 -> "顶部广告条 - tapping"
            else -> getString(R.string.string_mine_page)
        }
        SensorsDataEvent.subscribeCenterScreen(vipPageOrigin)
    }

    private inner class MyWebViewClient : android.webkit.WebViewClient() {
        override fun shouldOverrideUrlLoading(view: WebView, temUrl: String): Boolean {
            return if (temUrl.startsWith("weixin://")) {
                OpenUrlHelper.openWechat(this@SubscribeVerticalActivity)
                true
            } else if (temUrl.startsWith("tel:")) {
                OpenUrlHelper.openPhone(this@SubscribeVerticalActivity, temUrl)
                true
            } else if (!temUrl.startsWith("http")) {
                OpenUrlHelper.goBrowser(this@SubscribeVerticalActivity, temUrl)
                true
            } else {
                view.loadUrl(temUrl)
                true
            }
        }

        override fun onReceivedSslError(view: WebView, handler: SslErrorHandler, error: SslError) {
            handler.proceed() // 接受所有网站的证书
        }

        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        override fun onReceivedHttpError(
            view: WebView,
            request: WebResourceRequest,
            errorResponse: WebResourceResponse
        ) {
            super.onReceivedHttpError(view, request, errorResponse)
            val statusCode = errorResponse.statusCode
            XLog.e("tag", "=========statusCode=======$statusCode")
            if (500 == statusCode) {
                view.loadUrl(ConsUrl.getTimeOutNetworkUrl())
            }
        }

        override fun onReceivedError(
            view: WebView,
            errorCode: Int,
            description: String,
            failingUrl: String
        ) {
            super.onReceivedError(view, errorCode, description, failingUrl)
            // 断网或者网络连接超时
            XLog.e("tag", "=========onReceivedError=======$errorCode")
            if (errorCode == ERROR_HOST_LOOKUP || errorCode == ERROR_CONNECT || errorCode == ERROR_TIMEOUT) {
                view.loadUrl(ConsUrl.getTimeOutNetworkUrl())
            }
        }
    }

    inner class AndroidJsInterface {
        @JavascriptInterface
        fun doLogin() {
            runOnUiThread { LoginCenter.getInstance().loginControl(this@SubscribeVerticalActivity) }
        }

        @JavascriptInterface
        fun htmlCallBack(tag: String, next: String) {
            if (TextUtils.isEmpty(tag)) {
                return
            }
            runOnUiThread {
                CommonUtil.goNextBannerOrWebView(
                    this@SubscribeVerticalActivity,
                    CommonRouteEntity(tag, next, 0, 0)
                )
            }
        }

        @JavascriptInterface
        fun htmlCallShareUrl(title: String?, content: String?, url: String?, imgUrl: String?) {
            ShareActivity.doShareUrl(this@SubscribeVerticalActivity, url, title, content, imgUrl)
        }

        @JavascriptInterface
        fun htmlCallBackUserLogined() {
            if (UserMgr.getInstance().isLogin) {
                mBind.webView.post {
                    mBind.webView.loadUrl(
                        "javascript:userIsLogin('" + UserMgr.getInstance().loginUserId + "')"
                    )
                }
            } else {
                runOnUiThread {
                    LoginCenter.getInstance().loginControl(this@SubscribeVerticalActivity)
                }
            }
        }

        @JavascriptInterface
        fun stopPage() {
            runOnUiThread { onBackPressed() }
        }

        @JavascriptInterface
        fun closePage() {
            runOnUiThread { finish() }
        }

        @JavascriptInterface
        fun refreshData() {
            runOnUiThread { mBind.webView.reload() }
        }
    }

    override fun initData() {
        mViewModel.getSubSkuList(pageOrigin = pageOrigin)
        adViewModel.getSubscribeAd()
    }

    override fun startObserve() {
        mViewModel.apply {
            subSkuList.distinctUntilChanged().observe(this@SubscribeVerticalActivity) {
                if (it.isSuccess) {
                    refreshSubscribeInfoUi(it.successData)
                }
            }
        }
        adViewModel.subAdResource.observe(this) {
            if (it.isSuccess) {
                mBind.adView.setAdBannerData(it.successData)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBind.webView.post { mBind.webView.loadUrl("javascript:reload_page()") }
    }

    /**
     * 刷新订阅界面
     */
    private fun refreshSubscribeInfoUi(memberBean: MemberBean?) {
        memberBean?.let {
            mMemberBean = memberBean
            if (!it.product_list.isNullOrEmpty() && it.product_list.size > 2) {
                subNowAdapter.data = it.product_list.take(2)
            } else {
                subNowAdapter.data = it.product_list
            }
            if (!it.union_list.isNullOrEmpty() && it.union_list.size > 2) {
                subUnitAdapter.data = it.union_list.take(2)
            } else {
                subUnitAdapter.data = it.union_list
            }
            UserMgr.getInstance().user?.let { user ->
                mBind.isLogin = true
                mBind.user = user
                if (UserMgr.getInstance().isVipForever) {
                    mBind.subNowTxt.visibility = View.GONE
                    mBind.stvBg.visibility = View.INVISIBLE
                    mBind.subNowList.visibility = View.GONE
                    mBind.imgExpandNowState.visibility = View.GONE
                    setSubSkuGroup(skuGroupGather)
                } else {
                    mBind.subNowTxt.visibility = View.VISIBLE
                    mBind.stvBg.visibility = View.VISIBLE
                    mBind.subNowList.visibility = View.VISIBLE
                    mBind.imgExpandNowState.visibility = View.VISIBLE
                    setSubSkuGroup(skuSelectGroup)
                }
            } ?: let {
                mBind.isLogin = false
                mBind.noLoginMgs = ConfigMgr.getInstance().config.not_login_msg
                setSubSkuGroup(skuSelectGroup)
            }
        }
    }

    /**
     * 购买订阅
     *
     * @param subscribe
     */
    private fun doSubscribePay(subscribe: Subscribe?) {
        if (subscribe == null) {
            return
        }
//        if (!UserMgr.getInstance().isLogin) {
//            LoginCenter.getInstance().loginControl(this)
//            return
//        }
        subscribe.params.course_id = courseId
        subscribe.pageOrigin = pageOrigin
        var clone = subscribe.clone()
        if (subscribe.pay_type == PayWay.ZX.value()) {
            clone.pay_type = mBind.payWayView.getPayWay()
        } else {
            clone.pay_type = subscribe.pay_type
        }

        mBind.subProtocolPrivacy.isAgreePrivacy(subscribe,"订阅中心") {
            PayCenter.getInstance().doSubmitPay(this, clone)
        }
    }

    private fun doSubmitPay() {
        if (skuSelectGroup == skuGroupNow) {
            subNowAdapter.selectSubscribe?.let {
                doSubscribePay(it)
            } ?: let {
                ToastUtils.showShortToastCenter("请选择一种订阅类型")
            }
        } else {
            subUnitAdapter.selectSubscribe?.let {
                doSubscribePay(it)
            } ?: let {
                ToastUtils.showShortToastCenter("请选择一种订阅类型")
            }
        }
    }

    private fun setSubSkuGroup(skuGroup: Int) {
        skuSelectGroup = skuGroup
        if (skuGroup == skuGroupNow) {
            if (!isSelectTipImg) {
                mBind.subGatherTipImg.visibility = View.VISIBLE
            }
            ImageLoader.loadImageLocal(
                this@SubscribeVerticalActivity,
                R.mipmap.img_vt_subscribe_sku_bg_label,
                mBind.imgBgLabel
            )
            mBind.subNowList.visibility = View.VISIBLE
            mBind.subGatherList.visibility = View.GONE
            mBind.imgExpandNowState.visibility = View.VISIBLE
            mBind.imgExpandNowPlusState.visibility = View.GONE
            // stvBtn - sku
            mBind.subNowTxt.apply {
                textSize = 16f
                typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                solid = ContextCompat.getColor(this@SubscribeVerticalActivity, R.color.color_F0CA89)
            }
            mBind.subGatherTxt.apply {
                textSize = 14f
                solid = ContextCompat.getColor(
                    this@SubscribeVerticalActivity,
                    R.color.color_transparent
                )
                typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }
            if (EmptyUtils.isNotEmpty(subNowAdapter.selectSubscribe)) {
                setSkuData(subNowAdapter.selectSubscribe, subNowAdapter.selectPosition)
            }
        } else if (skuGroup == skuGroupGather) {
            isSelectTipImg = true
            ImageLoader.loadImageLocal(
                this@SubscribeVerticalActivity,
                R.mipmap.img_vt_subscribe_vip_sku_bg_label,
                mBind.imgBgLabel
            )
            mBind.subNowList.visibility = View.GONE
            mBind.subGatherList.visibility = View.VISIBLE
            mBind.imgExpandNowState.visibility = View.GONE
            mBind.imgExpandNowPlusState.visibility = View.VISIBLE
            // stvBtn - sku
            mBind.subNowTxt.apply {
                textSize = 14f
                solid = ContextCompat.getColor(
                    this@SubscribeVerticalActivity,
                    R.color.color_transparent
                )
                typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }

            mBind.subGatherTxt.apply {
                textSize = 16f
                typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                solid = ContextCompat.getColor(this@SubscribeVerticalActivity, R.color.color_F0CA89)
            }
            if (EmptyUtils.isNotEmpty(subUnitAdapter.selectSubscribe)) {
                setSkuData(subUnitAdapter.selectSubscribe, subUnitAdapter.selectPosition)
            }
        }
    }

    /**
     * 设置 Sku 文案
     *
     * @param subscribe entity
     * @param position 位置
     */
    private fun setSkuData(subscribe: Subscribe?, position: Int) {
        subscribe?.let {
            if (!TextUtils.isEmpty(it.description_url)) {
                mBind.webView.loadUrl(it.description_url)
            }
            if (subscribe.pay_type == PayWay.ZX.value()) {
                mBind.payWayView.visibility = View.VISIBLE
                mBind.payWayView.setPayWay(subscribe.default_pay_way)
            } else {
                mBind.payWayView.visibility = View.GONE
            }
            // updateUI - 按钮文案
            mBind.buySubTxt.text = subscribe.pay_button_title
            mBind.apply {
                // img - header
                if (UserMgr.getInstance().isLogin) {
                    ImageLoader.loadImageUrl(
                        this@SubscribeVerticalActivity,
                        UserMgr.getInstance().user.avatar,
                        circleImageView
                    )
                    if (skuSelectGroup == 1) {
                        tvContent.setTextColor(
                            ContextCompat.getColor(
                                this@SubscribeVerticalActivity,
                                R.color.color_white
                            )
                        )
                        tvContentBottom.setTextColor(
                            ContextCompat.getColor(
                                this@SubscribeVerticalActivity,
                                R.color.color_856026
                            )
                        )
                    } else {
                        tvContent.setTextColor(
                            ContextCompat.getColor(
                                this@SubscribeVerticalActivity,
                                R.color.color_CCAB76
                            )
                        )
                        tvContentBottom.setTextColor(
                            ContextCompat.getColor(
                                this@SubscribeVerticalActivity,
                                R.color.color_CAAC76
                            )
                        )
                    }
                    tvContent.text = UserMgr.getInstance().user.nickname
                    tvContentBottom.text = subscribe.membership_time_profit
                } else {
                    circleImageView.setImageDrawable(
                        ContextCompat.getDrawable(
                            this@SubscribeVerticalActivity,
                            R.mipmap.icon_default_user_img
                        )
                    )
                    tvContent.setTextColor(
                        ContextCompat.getColor(
                            this@SubscribeVerticalActivity,
                            R.color.color_white
                        )
                    )
                    if (skuSelectGroup == 1) {
                        tvContentBottom.setTextColor(
                            ContextCompat.getColor(
                                this@SubscribeVerticalActivity,
                                R.color.color_856026
                            )
                        )
                    } else {
                        tvContentBottom.setTextColor(
                            ContextCompat.getColor(
                                this@SubscribeVerticalActivity,
                                R.color.color_CAAC76
                            )
                        )
                    }
                    tvContent.text = getString(R.string.string_just_login_txt)
                    tvContentBottom.text = getString(R.string.string_not_vip_txt)
                }
                imgBgLabel.onDebounceClickListener {
                    if (!UserMgr.getInstance().isLogin) LoginCenter.getInstance()
                        .loginControl(this@SubscribeVerticalActivity)
                }
                mBind.subProtocolPrivacy.setAgreementCheckboxFalse()
                mBind.subProtocolPrivacy.setData(subscribe,ConfigMgr.getInstance().config.isAuto_vip_privacy_vip)

            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        when (event?.eventCode) {
            ConsEventCode.LOGIN_CHANGE_EVENT,
            ConsEventCode.CHANGE_SUBSCRIBE_EVENT -> {
                mViewModel.getSubSkuList(pageOrigin = pageOrigin)
            }
        }
    }

    inner class ClickProxy {

        fun onClickView(view: View) {
            when (view.id) {
                R.id.buy_sub_txt -> doSubmitPay()
                R.id.sub_now_txt -> setSubSkuGroup(skuGroupNow)
                R.id.sub_gather_txt -> setSubSkuGroup(skuGroupGather)
                R.id.imgExpandNowState -> {
                    mMemberBean?.let {
                        if (!mExpandNowState) {
                            mExpandNowState = true
                            ImageLoader.loadImageLocal(
                                this@SubscribeVerticalActivity,
                                R.drawable.ic_baseline_keyboard_arrow_up_24,
                                mBind.imgExpandNowState
                            )
                            if (!it.product_list.isNullOrEmpty()) {
                                subNowAdapter.data = it.product_list
                            }
                        } else {
                            mExpandNowState = false
                            ImageLoader.loadImageLocal(
                                this@SubscribeVerticalActivity,
                                R.drawable.ic_baseline_keyboard_arrow_down_24,
                                mBind.imgExpandNowState
                            )
                            if (!it.product_list.isNullOrEmpty() && it.product_list.size > 2) {
                                subNowAdapter.data = it.product_list.take(2)
                                mBind.scrollView.smoothScrollTo(0, 0)
                            }
                        }
                    }
                }
                R.id.imgExpandNowPlusState -> {
                    mMemberBean?.let {
                        if (!mExpandNowPlusState) {
                            mExpandNowPlusState = true
                            ImageLoader.loadImageLocal(
                                this@SubscribeVerticalActivity,
                                R.drawable.ic_baseline_keyboard_arrow_up_24,
                                mBind.imgExpandNowPlusState
                            )
                            if (!it.union_list.isNullOrEmpty()) {
                                subUnitAdapter.data = it.union_list
                            }
                        } else {
                            mExpandNowPlusState = false
                            ImageLoader.loadImageLocal(
                                this@SubscribeVerticalActivity,
                                R.drawable.ic_baseline_keyboard_arrow_down_24,
                                mBind.imgExpandNowPlusState
                            )
                            if (!it.union_list.isNullOrEmpty() && it.union_list.size > 2) {
                                subUnitAdapter.data = it.union_list.take(2)
                                mBind.scrollView.smoothScrollTo(0, 0)
                            }
                        }
                    }
                }
                else -> {}
            }
        }

    }

}


