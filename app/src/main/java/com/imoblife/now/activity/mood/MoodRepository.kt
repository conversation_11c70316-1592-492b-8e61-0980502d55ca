package com.imoblife.now.activity.mood

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.CalendarWeekDay
import com.imoblife.now.bean.Course
import com.imoblife.now.bean.DayMoodEntity
import com.imoblife.now.bean.Mood
import com.imoblife.now.bean.MoodLog
import com.imoblife.now.bean.MoodTag
import com.imoblife.now.bean.WeekDayMood
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiServiceMood
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers
import com.imoblife.now.util.CalendarUtils
import com.imoblife.now.util.PaperCache

class MoodRepository :BaseRepository() {
    fun getMoodFace(){
        ApiClient.getInstance()
            .createService(ApiServiceMood::class.java)
            .moodFace
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<Mood>>>() {
                override fun onSuccess(response: BaseResult<List<Mood>>?) {
                    response?.result?.let {
                        PaperCache.write(ConsCommon.MOOD_DIARY_EMOJI_JSON, it)
                    }
                }
            })
    }
    fun getMoodTag(){
        ApiClient.getInstance().createService(ApiServiceMood::class.java)
            .moodTag
            .compose(RxSchedulers.composeIO2IO())
            .subscribe(object : BaseObserver<BaseResult<List<MoodTag>>>() {
                override fun onSuccess(response: BaseResult<List<MoodTag>>?) {
                    response?.result?.let {
                        PaperCache.write(ConsCommon.MOOD_DIARY_TAGS_JSON, it)
                    }
                }
            })
    }
    fun saveMoodContent(liveData: MutableLiveData<UiStatus<Boolean>>, faceId:Int, tagIds:String,content:String){
        ApiClient.getInstance().createService(ApiServiceMood::class.java)
            .saveMood(faceId,tagIds,content)
            .compose(RxSchedulers.composeIO2IO())
            .subscribe ( object :BaseObserver<BaseResult<Boolean>>(){
                override fun onSuccess(response: BaseResult<Boolean>?) {
                    response?.result?.let {
                        liveData.postValue(UiStatus(true,it,"",Status.REFRESHSUCCESS))
                    }?:let {
                        liveData.postValue(UiStatus(true,null,"",Status.FAILED))
                    }
                }
                override fun onFailure(msg: String?) {
                    liveData.postValue(UiStatus(true,null,"",Status.REFRESHSUCCESS))
                }
            } )
    }
    fun getMoodCourse(liveData: MutableLiveData<UiStatus<List<Course>>>, moodId:Int){
        ApiClient.getInstance()
            .createService(ApiServiceMood::class.java)
            .getMoodCourse(moodId, ConsCommon.PAGE_MOOD_RECORD_COURSE)
            .compose(RxSchedulers.compose())
            .subscribe(object :BaseObserver<BaseResult<List<Course>>>(){
                override fun onSuccess(response: BaseResult<List<Course>>?) {
                    liveData.value=UiStatus(true,response?.result,"",Status.REFRESHSUCCESS)
                }
            })
    }
    fun getWeekMood(liveData: MutableLiveData<UiStatus<List<WeekDayMood>>>,year:Int,week:Int){
        ApiClient.getInstance().createService(ApiServiceMood::class.java)
            .getWeekMoodLog(year,week)
            .compose(RxSchedulers.composeIO2IO())
            .subscribe(object : BaseObserver<BaseResult<List<MoodLog>>>() {
                override fun onSuccess(response: BaseResult<List<MoodLog>>?) {
                    setWeekDay(liveData,CalendarUtils.getWeekDay(),response)
                }
            })
    }
    fun getMoodMonthCalendar(weekDayMonthMood:MutableLiveData<UiStatus<List<WeekDayMood>>>, year: Int, month: Int){
        val list= mutableListOf<WeekDayMood>()
        CalendarUtils.getMonthDay(year,month).forEach {
            val weekDayMood=WeekDayMood(it.year,it.month,it.day,it.data)
            list.add(weekDayMood)
        }
        weekDayMonthMood.postValue(UiStatus(true,list))
    }
    fun getMoodWeekCalendar(weekDayMonthMood:MutableLiveData<UiStatus<List<WeekDayMood>>>, year: Int, month: Int){
        val list= mutableListOf<WeekDayMood>()
        CalendarUtils.getMonthDay(year,month).forEach {
            val weekDayMood=WeekDayMood(it.year,it.month,it.day,it.data)
            list.add(weekDayMood)
        }
        weekDayMonthMood.postValue(UiStatus(true,list))
    }

    fun getMonthMood(
        liveData: MutableLiveData<UiStatus<List<WeekDayMood>>>,
        detailMonthData: MutableLiveData<UiStatus<List<MoodLog>>>,
        year: Int,
        month: Int
    ) {
        var monthStr=if (month<10){"0$month"}else{"$month"}
        ApiClient.getInstance().createService(ApiServiceMood::class.java)
            .getMonthMoodLog(year,monthStr)
            .compose(RxSchedulers.composeIO2IO())
            .subscribe ( object : BaseObserver<BaseResult<List<MoodLog>>>() {
                override fun onSuccess(response: BaseResult<List<MoodLog>>?) {
                    setWeekDay(
                        liveData,
                        CalendarUtils.getMonthDay(year, month),
                        response,
                        detailMonthData
                    )
                }
            })
    }

    fun deleteMoodLog(
        liveData: MutableLiveData<UiStatus<Boolean>>,
        moodLogId: Int,
        contentType: String
    ) {
        ApiClient.getInstance().createService(ApiServiceMood::class.java)
            .deleteMoodLog(moodLogId, contentType)
            .compose(RxSchedulers.composeIO2IO())
            .subscribe(object :BaseObserver<BaseResult<Boolean>>(){
                override fun onSuccess(response: BaseResult<Boolean>?) {
                    liveData.postValue(UiStatus(true,response?.result,"",Status.REFRESHSUCCESS))
                }
                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    liveData.postValue(UiStatus(false,null,msg,Status.FAILED))
                }
            })
    }

    private fun setWeekDay(
        liveData: MutableLiveData<UiStatus<List<WeekDayMood>>>,
        weekDayMood: MutableList<CalendarWeekDay>,
        response: BaseResult<List<MoodLog>>?,
        detailMonthData: MutableLiveData<UiStatus<List<MoodLog>>>? = null
    ) {
        val list= mutableListOf<WeekDayMood>()
        weekDayMood?.forEach { weekDayMood ->
            val weekDay=WeekDayMood(weekDayMood.year,weekDayMood.month,weekDayMood.day,weekDayMood.data)
            response?.result?.also { list ->
                list.forEach mood@{
                    if (weekDayMood.data == it.create_date) {
                        weekDay.icon = it.calendar_img
                        weekDay.create_date=it.create_date
                        return@also
                    }
                }
                detailMonthData?.postValue(UiStatus(true, list, "", Status.REFRESHSUCCESS))
            }
            list.add(weekDay)
        }
        liveData.postValue(UiStatus(true, list, "", Status.REFRESHSUCCESS))
    }

    /**
     * 心情记录 - 总天数
     */
    fun getEssayDays(_essayDays: MutableLiveData<UiStatus<DayMoodEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiServiceMood::class.java)
            .essayDays
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<DayMoodEntity>>() {
                override fun onSuccess(response: BaseResult<DayMoodEntity>?) {
                    _essayDays.value = UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }

                override fun onFailure(msg: String?) {
                    _essayDays.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 编辑保存我的心情记录
     */
    fun editSaveDiary(
        _editSaveDiary: MutableLiveData<UiStatus<Boolean>>,
        diaryId: Int,
        type: String,
        content: String
    ) {
        ApiClient
            .getInstance()
            .createService(ApiServiceMood::class.java)
            .editSaveDiary(diaryId, content, type)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<Boolean>>() {
                override fun onSuccess(response: BaseResult<Boolean>?) {
                    _editSaveDiary.value =
                        UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }

                override fun onFailure(msg: String?) {
                    _editSaveDiary.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

}