package com.imoblife.now.activity.breath

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.activity.ShareActivity
import com.imoblife.now.bean.BreathShareEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutAcBreathCompleteBinding
import com.imoblife.now.databinding.LayoutViewBreathCompleteContentBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.BitmapUtil
import com.imoblife.now.util.DeviceUtil
import com.imoblife.now.util.SpUtil
import com.makeramen.roundedimageview.RoundedImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/8/16
 * 描   述：呼吸练习 - 完成
 */
class BreathCompleteActivity : BaseVMActivity<BreathViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(context: Context, onlyId: String) {
            Intent(context, BreathCompleteActivity::class.java).apply {
                putExtra(ConsCommon.INTENT_TYPE_BREATHING_ONLY_ID, onlyId)
                context.startActivity(this)
            }
        }

    }

    private lateinit var mBind: LayoutAcBreathCompleteBinding

    // 呼吸练习 - 完成分享 - view
    private var mBindShareView: LayoutViewBreathCompleteContentBinding? = null

    // 呼吸背景
    private var mImgBgUrl = ""

    // onlyId
    private var mOnlyId: String = ""

    override fun getLayoutResId() = R.layout.layout_ac_breath_complete

    override fun initImmersionBar() {}

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsCommon.INTENT_TYPE_BREATHING_ONLY_ID)) {
                mOnlyId = it.getStringExtra(ConsCommon.INTENT_TYPE_BREATHING_ONLY_ID) ?: ""
            }
        }
    }

    override fun initVM() = ViewModelProvider(this).get(BreathViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcBreathCompleteBinding
        mBind.apply {
            clickProxy = ClickProxy()
            ImmersionBar
                .with(this@BreathCompleteActivity)
                .titleBar(toolbar)
                .statusBarDarkFont(false)
                .init()
            includeToolbar.apply {
                titleBackImg.setImageDrawable(
                    ContextCompat.getDrawable(
                        this@BreathCompleteActivity,
                        R.mipmap.icon_back_white
                    )
                )
                titleBackImg.setOnClickListener { finish() }
            }
            mImgBgUrl = SpUtil.getInstance().getStringValue(
                "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_BG_SELECT_IMG}",
                ""
            )
            updateImgUrl(mBind.shareView.imgBg)
        }
    }

    override fun initData() {
        mViewModel.getBreathShareData(mOnlyId)
    }

    override fun startObserve() {
        mViewModel.shareData.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    mBind.entity = entity
                    prepareShareView(entity)
                }
            }
        }
    }

    /**
     * 分享 - prepare
     */
    private fun prepareShareView(entity: BreathShareEntity) {
        mBindShareView = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.layout_view_breath_complete_content,
            null,
            false
        )
        mBindShareView?.apply {
            updateImgUrl(imgBg)
            this.entity = entity
            executePendingBindings()
        }
        mBindShareView?.root?.apply {
            mBind.fmContainer.addView(this)
            mBind.fmContainer.visibility = View.INVISIBLE
            post {
                BitmapUtil.layoutView(
                    this,
                    DeviceUtil.getScreenSize(this@BreathCompleteActivity)[0],
                    DeviceUtil.getScreenSize(this@BreathCompleteActivity)[1]
                )
            }
        }
    }

    /**
     * 更新img
     *
     * @param img RoundedImageView
     */
    private fun updateImgUrl(img: RoundedImageView) {
        Glide
            .with(this@BreathCompleteActivity)
            .asBitmap()
            .load(mImgBgUrl.ifEmpty { R.mipmap.breath_bg })
            .error(R.mipmap.breath_bg)
            .set<WebpFrameCacheStrategy>(
                WebpFrameLoader.FRAME_CACHE_STRATEGY,
                WebpFrameCacheStrategy.AUTO
            )
            .into(img)
    }

    /**
     * 分享
     */
    private fun shareView() {
        Handler().post {
            mBindShareView?.root?.let {
                val bitmap = BitmapUtil.loadBitmapFromView(it)
                bitmap?.apply {
                    ShareActivity.shareBitmap(
                        this@BreathCompleteActivity,
                        bitmap,
                        false,
                        1
                    )
                }
            }
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                R.id.stvBtn -> shareView()
                else -> {}
            }
        }

    }

}