package com.imoblife.now.activity.found

import android.content.Context
import android.content.Intent
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.mvvm.BaseActivity
import com.imoblife.now.R
import com.imoblife.now.constant.ConsIntent

/**
 * 畅谈
 */
class TalkAboutActivity : BaseActivity() {

    // 神策数据 - 当前页面id
    private var mPageId = 0

    // 神策数据 - 当前页面 - 分类id
    private var mCategoryId = 0

    private var index:Int=-1

    companion object {

        @JvmStatic
        fun start(context: Context, index: Int = -1, pageId: Int = 0, categoryId: Int = 0) {
            Intent(context, TalkAboutActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                putExtra(ConsIntent.BUNDLE_TALK_ABOUT_INDEX, index)
                putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId)
                putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId)
                context.startActivity(this)
            }
        }

    }

    override fun initImmersionBar() {
        ImmersionBar.with(this)
            .transparentStatusBar()
            .statusBarDarkFont(true)
            .fitsSystemWindows(false)
            .init()
    }

    override fun getLayoutResId()= R.layout.activity_talk_about
    override fun superInit(intent: Intent?) {
        if (hasExtra(ConsIntent.BUNDLE_TALK_ABOUT_INDEX)){
            index= intent?.getIntExtra(ConsIntent.BUNDLE_TALK_ABOUT_INDEX,-1)!!
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
            intent?.let {
                mPageId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0)
            }
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
            intent?.let {
                mCategoryId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0)
            }
        }
    }
    fun getCurrentIndex(): Int { return index }

    fun getCurrentPageId(): Int { return mPageId }

    fun getCurrentCategoryId(): Int { return mCategoryId }

    override fun initView() {}

    override fun initData() {}

}