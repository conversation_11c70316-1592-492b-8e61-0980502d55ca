package com.imoblife.now.activity.activities

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.FoundCourse
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiService
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-09-14
 * 描   述：ActivitiesRepository
 */
class ActiveRepository : BaseRepository() {

    private var mPage = 1

    fun getActiveData(
        _foundCourse: MutableLiveData<UiStatus<List<FoundCourse>>>,
        type: Int,
        initPage: Boolean = false,
    ) {
        if (initPage) mPage = 1
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .getActiveList(type, mPage)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<FoundCourse>>>() {
                override fun onSuccess(response: BaseResult<List<FoundCourse>>?) {
                    response?.result?.let {
                        if (it.isNotEmpty()) {
                            if (mPage == 1) {
                                _foundCourse.value = UiStatus(true, it, null, Status.REFRESHSUCCESS)
                            } else {
                                _foundCourse.value = UiStatus(true, it, null, Status.MORESUCCESS)
                            }
                            mPage++
                        } else {
                            checkStatus(_foundCourse)
                        }
                    } ?: checkStatus(_foundCourse)
                }

                override fun onFailure(msg: String?) {
                    if (mPage == 1) {
                        _foundCourse.value = UiStatus(false, null, null, Status.FAILED)
                    } else {
                        _foundCourse.value = UiStatus(false, null, null, Status.MOREFAIL)
                    }
                }
            })
    }

    private fun checkStatus(_foundCourse: MutableLiveData<UiStatus<List<FoundCourse>>>) {
        if (mPage == 1) {
            _foundCourse.value = UiStatus(true, null, null, Status.EMPTYDATA)
        } else {
            _foundCourse.value = UiStatus(true, null, null, Status.NOMOREDATA)
        }
    }

}