package com.imoblife.now.activity.memberchallenge

import android.app.Dialog
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpDrawable
import com.bumptech.glide.integration.webp.decoder.WebpDrawableTransformation
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterInside
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.imoblife.now.R
import com.imoblife.now.bean.ChallengeToastEntity
import com.imoblife.now.databinding.LayoutViewMemberChallengeSuccessDialogBinding
import com.imoblife.now.ext.fadeInUpAnimator

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/7/22
 * 描   述：会员挑战 - 挑战成功
 */
class MemberChallengeSuccessDialog(
    mContext: Context,
    private val mEntity: ChallengeToastEntity.ToastEntity? = null,
    private val mStartChallenge: (() -> Unit)? = null,
    private val mActionShare: (() -> Unit)? = null,
) : Dialog(mContext, R.style.dialog) {

    private lateinit var mBind: LayoutViewMemberChallengeSuccessDialogBinding

    companion object {

        fun showDialog(
            context: Context,
            entity: ChallengeToastEntity.ToastEntity,
            startChallenge: (() -> Unit)? = null,
            actionShare: (() -> Unit)? = null
        ) {
            MemberChallengeSuccessDialog(context, entity, startChallenge, actionShare).showDialog()
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBind = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.layout_view_member_challenge_success_dialog,
            null,
            false
        )
        setContentView(mBind.root)
        mBind.clickProxy = ClickProxy()
        initWebpImg()
        setCancelable(false)
    }

    /**
     * 加载webp动图 - 1次
     */
    private fun initWebpImg() {
        mEntity?.let {
            val transformation: Transformation<Bitmap> = CenterInside()
            Glide
                .with(context)
                .load(it.success_bg_img)
                .optionalTransform(
                    WebpDrawable::class.java,
                    WebpDrawableTransformation(transformation)
                )
                .addListener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>?,
                        isFirstResource: Boolean
                    ) = false

                    override fun onResourceReady(
                        resource: Drawable?,
                        model: Any?,
                        target: Target<Drawable>?,
                        dataSource: DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        val webpDrawable = resource as WebpDrawable
                        webpDrawable.apply {
                            loopCount = 1
                            registerAnimationCallback(object :
                                Animatable2Compat.AnimationCallback() {
                                override fun onAnimationEnd(drawable: Drawable?) {
                                    super.onAnimationEnd(drawable)
                                    unregisterAnimationCallback(this)
                                    mBind.stvStartBtn.fadeInUpAnimator()
                                    mBind.stvEndBtn.fadeInUpAnimator()
                                }
                            })
                        }
                        return false
                    }
                })
                .into(mBind.img)
        }
    }

    fun showDialog() {
        try {
            if (isShowing) {
                dismiss()
            }
            super.show()
            val attributes = window?.attributes
            attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
            attributes?.height = ViewGroup.LayoutParams.MATCH_PARENT
            window?.attributes = attributes
        } catch (i: Throwable) {
            i.printStackTrace()
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 开启挑战 -
                R.id.stvStartBtn -> {
                    mStartChallenge?.invoke()
                    dismiss()
                }
                // 炫耀一下 - 拉起分享
                R.id.stvEndBtn -> mActionShare?.invoke()
                R.id.imgClose -> dismiss()
                else -> {}
            }
        }

    }

}