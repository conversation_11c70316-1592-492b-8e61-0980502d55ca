package com.imoblife.now.activity.member

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.adapter.PurchaseMemberBannerAdapter
import com.imoblife.now.bean.AdResourceBean
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcDialogPurchaseMemberCouponBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import com.imoblife.now.util.AdResourceUtils
import com.imoblife.now.util.ImageLoader
import com.youth.banner.Banner

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/4/24
 * 描   述：首页弹窗 - 购买会员 - 优惠券
 */
class PurchaseMemberDialogActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, PurchaseMemberDialogActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcDialogPurchaseMemberCouponBinding

    private var mAdResourceBean: AdResourceBean? = null

    override fun getLayoutResId() = R.layout.layout_ac_dialog_purchase_member_coupon

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentBar()
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(NoViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcDialogPurchaseMemberCouponBinding
    }

    override fun initData() {
        mBind.apply {
            mAdResourceBean?.let {
                AdResourceUtils.adResourceShowStatistical(it)
                ImageLoader.loadImageUrl(this@PurchaseMemberDialogActivity, it.bg_img, imgBg)
                ImageLoader.loadImageUrl(this@PurchaseMemberDialogActivity, it.top_img, imgTop)
                ImageLoader.loadImageUrl(
                    this@PurchaseMemberDialogActivity,
                    it.center_img,
                    imgCenter
                )
                ImageLoader.loadImageUrl(
                    this@PurchaseMemberDialogActivity,
                    it.bottom_img,
                    imgBottom
                )
                bottomVipTimerView.setPayButtonData(it.button)
                banner
                    .addBannerLifecycleObserver(lifecycleOwner)
                    .setAdapter(PurchaseMemberBannerAdapter(it.order_list))
                    .setOrientation(Banner.VERTICAL)
                    .setUserInputEnabled(false)
            }
            imgClose.onDebounceClickListener { finish() }
            imgBottom.onDebounceClickListener {
                AdResourceUtils.adResourceClick(this@PurchaseMemberDialogActivity, mAdResourceBean)
                finish()
            }
        }
    }

    override fun startObserve() {}

    override fun onStop() {
        super.onStop()
        mBind.bottomVipTimerView.cancelTimer()
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        event?.let {
            if (it.eventCode == ConsEventCode.PURCHASE_MEMBER_COUPONS_EVENT) {
                mAdResourceBean = it.getResult()
            }
        }
    }

}