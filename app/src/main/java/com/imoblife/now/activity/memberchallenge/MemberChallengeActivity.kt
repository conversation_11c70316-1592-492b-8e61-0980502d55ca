package com.imoblife.now.activity.memberchallenge

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.AdResourceBean
import com.imoblife.now.bean.MemberChallengeEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.LayoutAcMemberChallengeBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.view.custom.AdBottomView
import com.imoblife.now.view.dialog.PopupActivityRulesAlterDialog
import com.imoblife.now.viewmodel.AdViewModel
import org.greenrobot.eventbus.EventBus

/**
 * 加入挑战赛
 */
fun MemberChallengeViewModel.joinSignChallenge(
    position: Int, context: Context, entity: MemberChallengeEntity.ListEntity
) {
    DialogUtil.showWaitLoading()
    joinSignChallenge(entity.id)
    if (entity.status == 1) {
        SensorsDataEvent.joinTheMemberChallenge(
            position, entity.id, entity.title, context.getString(R.string.string_join_txt)
        )
    } else if (entity.status == 3) {
        SensorsDataEvent.joinTheMemberChallenge(
            position, entity.id, entity.title, context.getString(R.string.string_join_again_txt)
        )
    }
}

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/19
 * 描   述：会员挑战 - 挑战未知的自己
 */
class MemberChallengeActivity : BaseVMActivity<MemberChallengeViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(
            context: Context, pageId: Int = 0, categoryId: Int = 0, page_from_name: String? = ""
        ) {
            val intent = Intent(context, MemberChallengeActivity::class.java)
            intent.apply {
                putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId)
                putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId)
                putExtra(ConsIntent.BUNDLE_PAGE_FROM_NAME, page_from_name)
                context.startActivity(this)
            }
        }

    }

    // 神策数据 - 当前页面id
    private var mPageId = 0

    // 神策数据 - 当前页面 - 分类id
    private var mCategoryId = 0

    // 神策数据 - 当前页面 - 页面来源
    private var mPageFromName = ""

    private lateinit var mBind: LayoutAcMemberChallengeBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) { MemberChallengeAdapter() }

//    private var mSkuSubscribe: Subscribe? = null

    private var mRuleH5String: String? = null

    private val mItemDecoration by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(
            DisplayUtil.dip2px(20F),
            DisplayUtil.dip2px(14F),
            DisplayUtil.dip2px(20F),
            DisplayUtil.dip2px(20F),
            DisplayUtil.dip2px(20F),
            DisplayUtil.dip2px(20F)
        )
    }

    // 懒加载底部广告布局
    private var mLazyAdBottomView: AdBottomView? = null

    private val mAdViewModel: AdViewModel by viewModels()

    // 点击加入/再次挑战选中 - ItemEntity
    private var mItemEntity: MemberChallengeEntity.ListEntity? = null

    override fun getLayoutResId() = R.layout.layout_ac_member_challenge

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
                mPageId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0)
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
                mCategoryId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0)
            }
            if (hasExtra(ConsIntent.BUNDLE_PAGE_FROM_NAME)) {
                mPageFromName = intent.getStringExtra(ConsIntent.BUNDLE_PAGE_FROM_NAME) ?: ""
            }
        }
    }

    override fun initImmersionBar() {}

    override fun initVM() = ViewModelProvider(this).get(MemberChallengeViewModel::class.java)

    override fun initView() {
        mLoadingHelper = LoadingHelper(this)
        mBind = mBinding as LayoutAcMemberChallengeBinding
        SensorsDataEvent.membershipChallengePageDisplay(mPageFromName)
        mBind.let {
            ImmersionBar.with(this@MemberChallengeActivity).titleBar(it.toolbar)
                .statusBarDarkFont(false).init()
            it.includeToolbar.apply {
                titleBackImg.setImageDrawable(
                    ContextCompat.getDrawable(
                        this@MemberChallengeActivity, R.mipmap.icon_back_white
                    )
                )
                titleBackImg.setOnClickListener { finish() }
                titleContentText.text = getString(R.string.string_challenge_the_unknown_self)
                titleContentText.setTextColor(
                    ContextCompat.getColor(
                        this@MemberChallengeActivity, R.color.white
                    )
                )
                titleMoreImg.setImageDrawable(
                    ContextCompat.getDrawable(
                        this@MemberChallengeActivity, R.mipmap.src_member_challenge_warning
                    )
                )
                titleMoreImg.setOnClickListener {
                    mRuleH5String?.let { rule ->
                        PopupActivityRulesAlterDialog(this@MemberChallengeActivity).setContentString(
                            rule
                        )
                    }
                }
            }
            it.recyclerView.apply {
                addItemDecoration(mItemDecoration)
                adapter = mAdapter
            }
            it.smartRefreshLayout.setOnRefreshListener {
                mViewModel.getChallengeList(mPageId, mCategoryId)
            }
//            mAdapter.setClickListener {
//                mSkuSubscribe?.let { subscribe ->
//                    MemberChallengeSkuDialog(subscribe).show(this)
//                }
//            }
            mAdapter.setClickJoinListener { position, entity ->
                mItemEntity = entity
                if (EmptyUtils.isNotEmpty(mAdapter.data)) {
                    var flag = false
                    mAdapter.data.forEach { listEntity ->
                        if (listEntity.status == 2) {
                            flag = true
                            return@forEach
                        }
                    }
                    if (flag) {
                        MemberChallengeStartNewDialog { type ->
                            if (type == 1) {
                                mViewModel.joinSignChallenge(position, this, entity)
                            }
                        }.show(this)
                    } else {
                        mViewModel.joinSignChallenge(position, this, entity)
                    }
                }
            }
        }
    }

    override fun initData() {
        Glide.with(this).load(R.mipmap.src_member_challenge_head_portrait_anim)
            .set<WebpFrameCacheStrategy>(
                WebpFrameLoader.FRAME_CACHE_STRATEGY, WebpFrameCacheStrategy.AUTO
            ).into(mBind.img)
        mLoadingHelper.apply {
            showLoadingView()
            setOnReloadListener {
                showLoadingView()
                mViewModel.getChallengeList(mPageId, mCategoryId)
            }
        }
        mViewModel.getChallengeList(mPageId, mCategoryId)
        mAdViewModel.getCampaignTriggerAds(ConsCommon.MEMBER_CHALLENGE_BOTTOM_RESOURCE_BIT)
    }

    override fun startObserve() {
        mViewModel.apply {
            listData.observe(this@MemberChallengeActivity) {
                DialogUtil.hideWaitLoading()
                mBind.smartRefreshLayout.finishRefresh()
                if (it.isSuccess) {
                    it.successData?.let { entity ->
                        mLoadingHelper.showContentView()
                        if (EmptyUtils.isNotEmpty(entity.list)) {
                            mAdapter.setNewData(entity.list.map { item ->
                                item.isFlipped = false
                                item
                            })
                        }
//                        if (EmptyUtils.isNotEmpty(entity.sku)) {
//                            mSkuSubscribe = entity.sku
//                        }
                        if (EmptyUtils.isNotEmpty(entity.rule)) {
                            mRuleH5String = entity.rule
                        }
                        if (EmptyUtils.isNotEmpty(entity.sign_user)) {
                            mBind.tvTitle.text = entity.sign_user.num.toString()
                            mBind.tvSubTitle.text = entity.sign_user.subtitle
                        }
                    } ?: mLoadingHelper.showEmptyView()
                } else mLoadingHelper.showErrorView()
            }

            joinSignChallenge.observe(this@MemberChallengeActivity) {
                if (it.isSuccess) {
                    it.successData?.let { bool ->
                        if (bool) {
                            EventBus.getDefault()
                                .post(BaseEvent(ConsEventCode.REFRESH_MEMBER_CHALLENGE))
                            getChallengeList(mPageId, mCategoryId)
                            mItemEntity?.let { itemEntity ->
                                MemberChallengeJoinDialog.showDialog(
                                    this@MemberChallengeActivity, itemEntity.title
                                )
                            }
                        }
                    }
                }
            }
        }
        //  活动触发广告 - 广告位置 - 会员挑战赛 - 底部资源位
        mAdViewModel.campaignTriggerAds.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { ad ->
                    showAdBottomView(ad)
                } ?: hideAdBottomView()
            }
        }
    }

    private fun hideAdBottomView() {
        if (mLazyAdBottomView != null) {
            mLazyAdBottomView?.visibility = View.GONE
        }
    }

    private fun showAdBottomView(it: AdResourceBean) {
        if (mLazyAdBottomView == null) {
            mLazyAdBottomView =
                mBind.fragmentBottomNavigationAdBottomView.viewStub?.inflate() as? AdBottomView
        }
        mLazyAdBottomView?.apply {
            visibility = View.VISIBLE
            setAdBannerData(it)
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.LOGIN_CHANGE_EVENT) {
            mViewModel.getChallengeList()
        }
    }

}