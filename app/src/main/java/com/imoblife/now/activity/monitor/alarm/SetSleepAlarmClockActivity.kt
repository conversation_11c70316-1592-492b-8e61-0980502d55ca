package com.imoblife.now.activity.monitor.alarm

import android.content.Context
import android.content.Intent
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.ConcatAdapter
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.commlibrary.utils.ViewType
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.*
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarAdapter
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcSetSleepAlarmClockBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.player.BGPlayerUtils
import com.imoblife.now.util.SpUtil
import com.imoblife.now.view.dialog.OnClickViewListener
import org.greenrobot.eventbus.EventBus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：设置 - 睡眠闹钟
 */
class SetSleepAlarmClockActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object {

        // 铃声
        const val RINGING_TONE_TYPE = 0

        // 轻唤醒场景音
        const val SCENE_SOUND_TYPE = 1

        fun startActivity(context: Context) {
            val intent = Intent(context, SetSleepAlarmClockActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcSetSleepAlarmClockBinding

    private lateinit var mLoadingHelper: LoadingHelper

    // 开启 - 睡眠闹钟 - Adapter
    private val mStartSleepAlarmClockAdapter by lazy(LazyThreadSafetyMode.NONE) { StartSleepAlarmClockAdapter() }

    // 铃声 - 睡眠闹钟 - Adapter
    private val mRingingToneSleepAlarmClockAdapter by lazy(LazyThreadSafetyMode.NONE) {
        RingingToneAndSceneSoundAdapter(
            RINGING_TONE_TYPE, object : AudioFrequencyCallBack {
                override fun selectRingingTone(type: Int, url: String) {
                    setRingingToneAndSceneSound(type, url)
                }
            }
        )
    }

    // 轻唤醒 - 睡眠闹钟 - Adapter
    private val mLightAwakeningSleepAlarmClockAdapter by lazy(LazyThreadSafetyMode.NONE) {
        LightAwakeningSleepAlarmClockAdapter {
            BottomSetTheWakeUpTimeDialog().show(this, object : OnClickViewListener {
                override fun onImgConfirmClick(item: String?) {
                    item?.let { setLightAwakeningSleepAlarmClockMinute(it) }
                }
            })
        }
    }

    // 轻唤醒场景音 - 睡眠闹钟 - Adapter
    private val mSceneSoundSleepAlarmClockAdapter by lazy(LazyThreadSafetyMode.NONE) {
        RingingToneAndSceneSoundAdapter(
            SCENE_SOUND_TYPE, object : AudioFrequencyCallBack {
                override fun selectRingingTone(type: Int, url: String) {
                    setRingingToneAndSceneSound(type, url)
                }
            }
        )
    }

//    private val mSleepRemindAdapter by lazy { SleepRemindAdapter() }


    // 组合适配器
    private val mConcatAdapter by lazy(LazyThreadSafetyMode.NONE) {
        ConcatAdapter(
            mStartSleepAlarmClockAdapter,
            mRingingToneSleepAlarmClockAdapter,
            mLightAwakeningSleepAlarmClockAdapter,
            mSceneSoundSleepAlarmClockAdapter
        )
    }

    // 闹钟音频大小
    private var mSoundSize: Float = 0.5F

    // 轻唤醒分钟数
    private var mLightAwakeningMinute = 30

    // 闹钟铃声｜轻唤醒场景音 - 某一音频
    private var mRingingTone = ""
    private var mSceneSound = ""

    // 开启睡眠闹钟
    private var mEnableSleepAlarmClock = true

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .statusBarColor(R.color.color_212121)
            .statusBarDarkFont(false)
            .fitsSystemWindows(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_set_sleep_alarm_clock

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mLoadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_sleep_alarm_clock),
            NavIconType.BACK_SLEEP_ALARM_CLOCK,
            getString(R.string.string_completed),
            { clickActionComplete() },
            true,
        )
        mLoadingHelper.getAdapter<ToolbarAdapter>(ViewType.TITLE).setRightTitleColor(
            ContextCompat.getColor(this, R.color.white),
            ContextCompat.getColor(this, R.color.main_color)
        )
        mBind = mBinding as LayoutAcSetSleepAlarmClockBinding
    }

    override fun initData() {
        // 睡眠闹钟 - checkBox - state
        mEnableSleepAlarmClock = SpUtil.getInstance()
            .getBoolenValue(ConsCommon.SLEEP_ALARM_CLOCK_CHECK_STATE, mEnableSleepAlarmClock)
        // 闹钟音频声音大小
        mSoundSize =
            SpUtil.getInstance().getFloatValue(ConsCommon.SLEEP_ALARM_CLOCK_SOUND_SIZE, mSoundSize)
        // 轻唤醒分钟数
        mLightAwakeningMinute = SpUtil.getInstance()
            .getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_MINUTE, mLightAwakeningMinute)
        // 闹钟铃声｜轻唤醒场景音 - 某一音频
        mRingingTone = SpUtil.getInstance()
            .getStringValue(
                ConsCommon.SLEEP_ALARM_CLOCK_RINGING_TONE,
                ConsCommon.SLEEP_ALARM_CLOCK_RINGING_TONE_URL
            )
        mSceneSound = SpUtil.getInstance()
            .getStringValue(
                ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING,
                ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_URL
            )
        mStartSleepAlarmClockAdapter.setStartSleepAlarmClockCallBack(object :
            StartSleepAlarmClockCallBack {
            override fun checkBoxIsChecked(isChecked: Boolean) {
                mEnableSleepAlarmClock = isChecked
            }

            override fun seekBarProgressChanged(progress: Int) {
                mSoundSize = progress / 100F
            }
        })
        mBind.recyclerView.adapter = mConcatAdapter
        mViewModel.getAlarmList()
    }

    override fun startObserve() {
        mViewModel.alarmList.observe(this) { stateEntity ->
            if (stateEntity.isSuccess) {
                stateEntity.successData?.let { entity ->
                    mBind.recyclerView.apply {
                        // 设置闹钟铃声 - 选中状态
                        entity.alarm.forEach {
                            it.isSelect = it.url == mRingingTone
                        }
                        // 设置轻唤醒铃声 - 选中状态
                        entity.wake_up_lightly.forEach {
                            it.isSelect = it.url == mSceneSound
                        }
                        // 开启 - 睡眠闹钟
                        mStartSleepAlarmClockAdapter.setNewData(mutableListOf(getString(R.string.string_sleep_alarm_clock)))
                        // 睡眠闹钟
                        mRingingToneSleepAlarmClockAdapter.setNewData(mutableListOf(entity))
                        // 轻唤醒 - 分钟
                        mLightAwakeningSleepAlarmClockAdapter.setNewData(
                            mutableListOf(
                                mLightAwakeningMinute
                            )
                        )
                        // 轻唤醒场景音
                        mSceneSoundSleepAlarmClockAdapter.setNewData(mutableListOf(entity))
                    }
                }
            }
        }
    }

    /**
     * 触发完成事件
     * 描述：
     * 1、设置闹钟音频声音大小
     * 2、设置轻唤醒分钟数
     * 3、闹钟铃声｜轻唤醒场景音 - 某一音频
     * 4、睡眠闹钟 - 设置闹钟 时｜分
     */
    private fun clickActionComplete() {
        // 睡眠闹钟 - checkBox - state
        SpUtil.getInstance()
            .saveBooleanToSp(ConsCommon.SLEEP_ALARM_CLOCK_CHECK_STATE, mEnableSleepAlarmClock)
        EventBus.getDefault()
            .post(BaseEvent(ConsEventCode.SLEEP_ALARM_CLOCK_CHANGE, mEnableSleepAlarmClock))
        // 闹钟音频声音大小
        SpUtil.getInstance()
            .saveFloatToSp(ConsCommon.SLEEP_ALARM_CLOCK_SOUND_SIZE, mSoundSize)
        // 轻唤醒分钟数
        SpUtil.getInstance()
            .saveIntToSp(ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_MINUTE, mLightAwakeningMinute)
        // 闹钟铃声｜轻唤醒场景音 - 某一音频
        SpUtil.getInstance()
            .saveStringToSp(ConsCommon.SLEEP_ALARM_CLOCK_RINGING_TONE, mRingingTone)
        SpUtil.getInstance()
            .saveStringToSp(ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING, mSceneSound)
        // 关闭页面
        finish()
    }

    /**
     * 设置轻唤醒分钟数
     *
     * @param item 分钟数
     */
    fun setLightAwakeningSleepAlarmClockMinute(item: String) {
        mLightAwakeningMinute = item.toInt()
        mLightAwakeningSleepAlarmClockAdapter.setNewData(mutableListOf(mLightAwakeningMinute))
    }

    /**
     * 设置闹钟铃声｜轻唤醒场景音 - 某一音频
     *
     * @param type 闹钟铃声｜轻唤醒场景音
     * @param url 某一音频
     */
    private fun setRingingToneAndSceneSound(type: Int, url: String) {
        if (type == 0) {
            mRingingTone = url
        } else {
            mSceneSound = url
        }
    }

    override fun onStop() {
        super.onStop()
        BGPlayerUtils.instance.pause()
    }

}