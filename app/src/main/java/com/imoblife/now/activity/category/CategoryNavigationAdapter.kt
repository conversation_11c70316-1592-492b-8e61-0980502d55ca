package com.imoblife.now.activity.category

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.commlibrary.view.BetterGesturesRecyclerView
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.helper.AdapterHelper
import com.imoblife.now.adapter.home.HomeAdAdapter
import com.imoblife.now.adapter.home.HomeRecommendVideoAdapter
import com.imoblife.now.bean.CategoryNavigationEntity
import com.imoblife.now.bean.SmallVideo
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.EmptyUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航
 */
class CategoryNavigationAdapter(private val isShowItemDecoration: Boolean = true) :
    BaseQuickAdapter<CategoryNavigationEntity.CategoryListEntity, BaseViewHolder>(R.layout.layout_snap_view) {

    private val mDecorationVStartEnd by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(
            DisplayUtil.dip2px(13f),
            DisplayUtil.dip2px(13f),
            DisplayUtil.dip2px(13f),
            0,
            DisplayUtil.dip2px(13f),
            0
        )
    }

    private val mDecorationV by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(0, DisplayUtil.dip2px(13f))
    }

    private val mDecorationHV by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(
            DisplayUtil.dip2px(14F),
            DisplayUtil.dip2px(14F),
            DisplayUtil.dip2px(20F),
            0,
            DisplayUtil.dip2px(20F),
            0
        )
    }

    private val decorationV by lazy {
        CommonItemDecoration(
            0,
            DisplayUtil.dip2px(13f),
            DisplayUtil.dip2px(13f),
            DisplayUtil.dip2px(13f),
            DisplayUtil.dip2px(13f),
            0
        )
    }

    override fun convert(
        holder: BaseViewHolder,
        item: CategoryNavigationEntity.CategoryListEntity?
    ) {
        item?.apply {
            val mSubTitleTxt = holder.getView<TextView>(R.id.sub_tile_name_txt)
            val mSubMoreTxt = holder.getView<TextView>(R.id.sub_title_more_txt)
            val mSubRecycler = holder.getView<BetterGesturesRecyclerView>(R.id.sub_recycler)

            if (!isShowItemDecoration) {
                val layoutParamsSubTitleTxt: ConstraintLayout.LayoutParams =
                    mSubTitleTxt.layoutParams as ConstraintLayout.LayoutParams
                layoutParamsSubTitleTxt.marginStart = DisplayUtil.dip2px(20F)
                mSubTitleTxt.layoutParams = layoutParamsSubTitleTxt
            }
            mSubRecycler.removeItemDecoration(mDecorationV)
            mSubRecycler.removeItemDecoration(mDecorationHV)
            mSubRecycler.removeItemDecoration(mDecorationVStartEnd)
            mSubRecycler.removeItemDecoration(decorationV)
            mSubRecycler.isNestedScrollingEnabled = false
            if (!TextUtils.isEmpty(item.title)) {
                mSubTitleTxt.text = item.title
                mSubTitleTxt.visibility = View.VISIBLE
            } else {
                mSubTitleTxt.visibility = View.GONE
            }
            if (EmptyUtils.isNotEmpty(item.more)) {
                val moreEntity = item.more
                if (!TextUtils.isEmpty(moreEntity.title)) {
                    mSubMoreTxt.text = moreEntity.title
                    mSubMoreTxt.visibility = View.VISIBLE
                    mSubMoreTxt.setOnClickListener {
                        CategoryNavigationItemActivity.startActivity(
                            mContext,
                            moreEntity.type ?: "",
                            moreEntity.cat_id,
                            title,
                            moreEntity.page_id,
                            moreEntity.category_id
                        )
                    }
                } else {
                    mSubMoreTxt.visibility = View.GONE
                }
            } else {
                mSubMoreTxt.visibility = View.GONE
            }
            when (item.tag) {
                // 首页 - 金刚区 - 分类导航 - course - 课程
                ConsCommon.CATEGORY_TYPE_COURSE -> {
                    if (item.listItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.HORIZONTAL,
                                false
                            ).also {
                                it.initialPrefetchItemCount = 4
                            }
                        mSubRecycler.setHasFixedSize(true)
                        CategoryCourseAdapter().apply {
                            if (isShowItemDecoration) {
                                mSubRecycler.addItemDecoration(mDecorationVStartEnd)
                            } else {
                                mSubRecycler.addItemDecoration(mDecorationHV)
                            }
                            mSubRecycler.adapter = this
                            setNewData(item.listItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - book - 读书
                ConsCommon.CATEGORY_TYPE_BOOK -> {
                    if (item.listItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.VERTICAL,
                                false
                            ).also {
                                it.initialPrefetchItemCount = 4
                            }
                        mSubRecycler.setHasFixedSize(true)
                        CategoryBookAdapter().apply {
                            mSubRecycler.addItemDecoration(mDecorationV)
                            mSubRecycler.adapter = this
                            setNewData(item.listItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - article - 阅读
                ConsCommon.CATEGORY_TYPE_ARTICLE -> {
                    if (item.listItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
                        CategoryReadAdapter().apply {
                            mSubRecycler.addItemDecoration(mDecorationVStartEnd)
                            mSubRecycler.adapter = this
                            setNewData(item.listItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - excellent - 拓展进阶
                ConsCommon.CATEGORY_TYPE_EXCELLENT -> {
                    if (item.listItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.VERTICAL,
                                false
                            ).also {
                                it.initialPrefetchItemCount = 4
                            }
                        mSubRecycler.setHasFixedSize(true)
                        CategoryExcellentAdapter().apply {
                            mSubRecycler.addItemDecoration(mDecorationV)
                            mSubRecycler.adapter = this
                            setNewData(item.listItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - train - 行动营
                ConsCommon.CATEGORY_TYPE_TRAIN -> {
                    if (item.listItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.VERTICAL,
                                false
                            ).also {
                                it.initialPrefetchItemCount = 4
                            }
                        mSubRecycler.setHasFixedSize(true)
                        CategoryTrainAdapter().apply {
                            mSubRecycler.addItemDecoration(mDecorationVStartEnd)
                            mSubRecycler.adapter = this
                            setNewData(item.listItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - active - 活动 & 首页 - 金刚区 - 分类导航 - workshop - 正念研习社
                ConsCommon.CATEGORY_TYPE_ACTIVE, ConsCommon.CATEGORY_TYPE_WORKSHOP -> {
                    if (item.listItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.VERTICAL,
                                false
                            ).also {
                                it.initialPrefetchItemCount = 4
                            }
                        mSubRecycler.setHasFixedSize(true)
                        CategoryWorkShopAndActiveAdapter().apply {
                            mSubRecycler.addItemDecoration(mDecorationV)
                            mSubRecycler.adapter = this
                            setNewData(item.listItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - quick_link - 呼吸计时
                ConsCommon.CATEGORY_TYPE_QUICK_LINK -> {
                    if (item.listTabItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager = GridLayoutManager(mContext, 2).also {
                            it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                                override fun getSpanSize(position: Int): Int {
                                    return if (item.listTabItemsEntity[position].isExpand) 2 else 1
                                }
                            }
                        }
                        CategoryBreathTimingAdapter().apply {
                            mSubRecycler.addItemDecoration(mDecorationVStartEnd)
                            mSubRecycler.adapter = this
                            setNewData(item.listTabItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - question - 常见问题
                ConsCommon.CATEGORY_TYPE_QUESTION -> {
                    if (item.listTabItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.HORIZONTAL,
                                false
                            ).also {
                                it.initialPrefetchItemCount = 4
                            }
                        mSubRecycler.setHasFixedSize(true)
                        CategoryCommonProblemAdapter().apply {
                            mSubRecycler.addItemDecoration(mDecorationVStartEnd)
                            mSubRecycler.adapter = this
                            setNewData(item.listTabItemsEntity)
                        }
                    }
                }
                // 首页 - 金刚区 - 分类导航 - decompression - 解压视频
                ConsCommon.CATEGORY_TYPE_DECOMPRESSION -> {
                    if (listItemsEntity.isNullOrEmpty()) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.HORIZONTAL,
                                false
                            ).also {
                                it.initialPrefetchItemCount = 4
                            }
                        mSubRecycler.setHasFixedSize(true)
                        mSubRecycler.addItemDecoration(mDecorationVStartEnd)
                        val homeRecommendVideoAdapter = HomeRecommendVideoAdapter()
                        mSubRecycler.adapter = homeRecommendVideoAdapter
                        homeRecommendVideoAdapter.setNewData(listItemsEntity.map {
                            SmallVideo().apply {
                                id = it.id
                                title = it.title
                                course_tag = it.course_tag
                                thumb = it.thumb
                                media_path = it.media_path
                                isIs_new = it.isIs_new
                                page_id = it.page_id
                                category_id = it.category_id
                                page_title = it.page_title
                                category_title = category_title
                            }
                        })
                    }
                }
                // 广告
                ConsCommon.TYPE_RECOMMEND_AD -> {
                    if (EmptyUtils.isEmpty(ad)) {
                        AdapterHelper.setVisibility(holder.itemView, false)
                    } else {
                        AdapterHelper.setVisibility(holder.itemView, true)
                        mSubRecycler.layoutManager =
                            LinearLayoutManager(
                                mContext,
                                LinearLayoutManager.HORIZONTAL,
                                false
                            )
                        mSubRecycler.setHasFixedSize(true)
                        mSubRecycler.addItemDecoration(decorationV)
                        mSubRecycler.adapter = HomeAdAdapter(mutableListOf(ad))
                    }
                }

                else -> {
                    AdapterHelper.setVisibility(holder.itemView, false)
                    mSubTitleTxt.visibility = View.GONE
                    mSubMoreTxt.visibility = View.GONE
                    mSubRecycler.visibility = View.GONE
                }
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

}