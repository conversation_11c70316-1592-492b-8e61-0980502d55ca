package com.imoblife.now.activity.mood

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.adapter.MoodDayAdapter
import com.imoblife.now.adapter.MoodLogAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.databinding.ActivityMoodHistoryBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.CalendarUtils

class MoodHistoryActivity : BaseVMActivity<MoodModel>() {

    private val moodDayAdapter by lazy(LazyThreadSafetyMode.NONE) { MoodDayAdapter() }
    private lateinit var mBind: ActivityMoodHistoryBinding

    // adapter - 心情记录 - 历史 - log列表Item
    private val mMoodLogAdapter by lazy(LazyThreadSafetyMode.NONE) { MoodLogAdapter() }

    private val mItemDecoration by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(16.dp, 16.dp, 0, 0, 0, 20.dp)
    }

    // 当前时间 - 年
    private var mYear: Int? = null

    // 当前时间 - 月
    private var mMonth: Int? = null

    companion object {

        @JvmStatic
        fun startHistoryMood(context: Context, bundle: Bundle?) {
            context.startActivity(Intent(context, MoodHistoryActivity::class.java), bundle)
        }

    }

    override fun getLayoutResId() = R.layout.activity_mood_history

    override fun initImmersionBar() = ImmersionBar.with(this).transparentStatusBar().init()

    override fun superInit(intent: Intent?) {}

    override fun initVM() =
        ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(MoodModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityMoodHistoryBinding
        mBind.apply {
            moodTitle.apply {
                leftImg.setOnClickListener { onBackPressed() }
                tvTitle.text = getString(R.string.string_mood_history_txt)
            }
            moodLogRecycler.adapter = moodDayAdapter
            recyclerView.addItemDecoration(mItemDecoration)
            recyclerView.adapter = mMoodLogAdapter
        }
    }

    override fun initData() {
        mViewModel.apply {
            mBind.moodModel = this
            getWeekDayMonthMood()
            getEssayDays()
        }
    }

    override fun startObserve() {
        mViewModel.apply {
            yearMonth.observe(this@MoodHistoryActivity) {
                mBind.apply {
                    mYear = it.first
                    mMonth = it.second
                    yearMonthDay = "${it.first}年${it.second}月"
                    if (CalendarUtils.isCurrentMonth(it.first, it.second)) {
                        nextMonth.isEnabled = false
                        nextMonth.setImageDrawable(
                            ContextCompat.getDrawable(
                                this@MoodHistoryActivity, R.mipmap.icon_next_month_right_no_enable
                            )
                        )
                    } else {
                        nextMonth.isEnabled = true
                        nextMonth.setImageDrawable(
                            ContextCompat.getDrawable(
                                this@MoodHistoryActivity, R.mipmap.icon_next_month_right
                            )
                        )
                    }
                }
            }
            weekDayMonthMood.observe(this@MoodHistoryActivity) {
                if (it.isSuccess) {
                    it.successData?.let { list -> moodDayAdapter.setNewData(list) }
                }
            }
            // adapter - 心情记录 - 历史 - log列表Item
            detailMonthData.observe(this@MoodHistoryActivity) {
                if (it.isSuccess) {
                    mBind.apply {
                        if (!it.successData.isNullOrEmpty()) {
                            recyclerView.visibility = View.VISIBLE
                            containerEmpty.visibility = View.GONE
                            mMoodLogAdapter.setNewData(it.successData)
                        } else {
                            recyclerView.visibility = View.GONE
                            containerEmpty.visibility = View.VISIBLE
                            containerEmpty.setOnClickListener {
                                finish()
                                MoodActivity.startMood(this@MoodHistoryActivity)
                            }
                        }
                    }
                }
            }
            // 心情记录 - 总天数
            essayDays.observe(this@MoodHistoryActivity) {
                mBind.tvContent.apply {
                    if (it.isSuccess) {
                        it.successData?.let { entity ->
                            text = getString(R.string.string_total_days_of_mood_recording, entity.times)
                            setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
                        } ?: run {
                            text = getString(R.string.string_record_your_mood_at_the_moment)
                            setCompoundDrawablesWithIntrinsicBounds(
                                null, null, ContextCompat.getDrawable(
                                    this@MoodHistoryActivity, R.mipmap.img_mood_history_edit_tv
                                ), null
                            )
                        }
                    } else {
                        text = getString(R.string.string_record_your_mood_at_the_moment)
                        setCompoundDrawablesWithIntrinsicBounds(
                            null, null, ContextCompat.getDrawable(
                                this@MoodHistoryActivity, R.mipmap.img_mood_history_edit_tv
                            ), null
                        )
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == ConsRequestCode.SEND_REQUEST_MOOD_DETAIL && resultCode == Activity.RESULT_OK) {
            refreshData()
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.REFRESH_MOOD_HISTORY_MOOD_LOG) {
            refreshData()
        }
    }

    /**
     * 刷新页面数据
     */
    private fun refreshData() {
        mViewModel.apply {
            if (mYear != null && mMonth != null) {
                setCurrentDate(Pair(mYear!!, mMonth!!))
            } else {
                getWeekDayMonthMood()
            }
            getEssayDays()
        }
    }

}