package com.imoblife.now.activity.member.signing

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutAcObTransformationGoalBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/7/4
 * 描   述：ob - 过渡 - 蜕变之旅即将开启，你已经向目标迈进了1步
 */
class TransformationGoalActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, TransformationGoalActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcObTransformationGoalBinding

    override fun getLayoutResId() = R.layout.layout_ac_ob_transformation_goal

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(NoViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcObTransformationGoalBinding
        lifecycleScope.launch {
            delay(2000)
            SigningAContractActivity.startActivity(this@TransformationGoalActivity)
            finish()
        }
    }

    override fun initData() {}

    override fun startObserve() {}

    override fun onBackPressed() {}

}