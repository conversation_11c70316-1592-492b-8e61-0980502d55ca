package com.imoblife.now.activity.monitor.sleep

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.tabs.TabLayout
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.ActivitySleepAssitBinding
import com.imoblife.now.fragment.sleep.SleepTabAdapter
import com.imoblife.now.fragment.sleep.SleepViewModel
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.DisplayUtil

/**
 * 睡眠内容辅助页面
 */
class SleepAssistActivity : BaseVMActivity<SleepViewModel>() {
    companion object{
        fun startSleepAssist(context: Context){
            Intent(context,SleepAssistActivity::class.java).run {
                context.startActivity(this)
            }
        }
    }
    private val sleepTabAdapter by lazy { SleepTabAdapter(mIsSleepAssist = true) }
    private lateinit var mLinearLayoutManager: LinearLayoutManager
    private lateinit var mBind:ActivitySleepAssitBinding
    private var isScroll = false
    override fun getLayoutResId(): Int {
        return R.layout.activity_sleep_assit
    }
    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentBar().init()
    }
    override fun initVM() = ViewModelProvider(this).get(SleepViewModel::class.java)
    override fun superInit(intent: Intent?) {}
    override fun initView() {
        ConfigMgr.getInstance().isFromSleepPage=true
        mBind=mBinding as ActivitySleepAssitBinding
        mLinearLayoutManager = LinearLayoutManager(this)
        mBind.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                //点击tab的时候，RecyclerView自动滑到该tab对应的item位置
                val position = tab!!.position
                if (!isScroll) {
                    mLinearLayoutManager.scrollToPositionWithOffset(position, 0)
                }
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
        mBind.toolbar.setNavigationOnClickListener { finish() }
        mBind.recycler.apply {
            setHasFixedSize(true)
            (itemAnimator as DefaultItemAnimator).supportsChangeAnimations = false
            layoutManager = mLinearLayoutManager
            addItemDecoration(
                CommonItemDecoration(
                    DisplayUtil.dip2px(15f),
                    DisplayUtil.dip2px(15f), DisplayUtil.dip2px(15f),
                    DisplayUtil.dip2px(15f),
                    DisplayUtil.dip2px(15f), DisplayUtil.dip2px(15f)
                )
            )
            adapter = sleepTabAdapter
        }
        mBind.recycler.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                isScroll = newState != RecyclerView.SCROLL_STATE_IDLE
            }
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                //滑动RecyclerView list的时候，根据最上面一个Item的position来切换tab
                val position = mLinearLayoutManager.findFirstVisibleItemPosition()
                setCurrentTabPosition(position)
            }
        })
    }
    override fun initData() {
        mViewModel.getSleepTab()
    }
    override fun startObserve() {
        mViewModel.apply {
            sleepDataTab.observe(this@SleepAssistActivity) { uiStatus ->
                DialogUtil.hideWaitLoading()
                if (uiStatus.isSuccess) {
                    uiStatus.successData?.apply {
                        mBind.tabLayout.removeAllTabs()
                        forEach {
                            if (ConsCommon.COURSE_TYPE_NATURE_COURSE == it.category_type) {
                                mBind.tabLayout.addTab(
                                    mBind.tabLayout.newTab().setText("全部").setTag(it.category_type)
                                )
                            } else {
                                mBind.tabLayout.addTab(
                                    mBind.tabLayout.newTab().setText(it.category_name)
                                        .setTag(it.category_type)
                                )
                            }
                        }
                        sleepTabAdapter.setNewData(this)
                    }
                }
            }
        }
    }

    fun setCurrentTabPosition(position: Int, content: String = "") {
        if (position == -1) {
            for (index in 0..mBind.tabLayout.tabCount) {
                val tabAt: TabLayout.Tab? = mBind.tabLayout.getTabAt(index)
                if (tabAt?.tag == content) {
                    if (!tabAt.isSelected) {
                        tabAt.select()
                        return
                    }
                }
            }
        } else {
            val tabAt: TabLayout.Tab? = mBind.tabLayout.getTabAt(position)
            if (tabAt != null && !tabAt.isSelected) {
                tabAt.select()
            }
        }
    }
}