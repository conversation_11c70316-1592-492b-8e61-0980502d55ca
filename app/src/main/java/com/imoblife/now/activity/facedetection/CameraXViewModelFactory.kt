package com.imoblife.now.activity.facedetection

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.CreationExtras

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - CameraXViewModelFactory
 */
class CameraXViewModelFactory(private val application: Application) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>, extras: CreationExtras): T {
        if (modelClass.isAssignableFrom(CameraXViewModel::class.java)) {
            return CameraXViewModel(application) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }

}