package com.imoblife.now.activity.category

import android.content.Context
import androidx.constraintlayout.widget.ConstraintLayout
import com.drakeet.multitype.ViewDelegate
import com.imoblife.now.bean.CategoryNavigationItemEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/11
 * 描   述：首页 - 金刚区 - 分类导航 - 分类Item - delegate
 */
class CategoryNavigationItemDelegate :
    ViewDelegate<CategoryNavigationItemEntity, CategoryNavigationItemItemView>() {

    override fun onBindView(
        view: CategoryNavigationItemItemView,
        item: CategoryNavigationItemEntity
    ) {
        view.setItemData(item, view.layoutPosition)
    }

    override fun onCreateView(context: Context) = CategoryNavigationItemItemView(context).also {
        val constraintLayout =
            ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
        it.layoutParams = constraintLayout
    }

}