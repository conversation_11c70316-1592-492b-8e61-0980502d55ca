package com.imoblife.now.activity.activities

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager.widget.ViewPager
import com.flyco.tablayout.listener.OnTabSelectListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.TitlePageAdapter
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.databinding.LayoutActivitysBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-09-14
 * 描   述：活动
 */
class ActiveActivity : BaseVMActivity<ActiveViewModel>() {

    private lateinit var mBind: LayoutActivitysBinding

    private lateinit var loadingHelper: LoadingHelper

    private val mTitle = listOf("活动", "我参加的")

    private val mTabFragmentList by lazy {
        listOf(ActiveFragment.newInstance(0), ActiveFragment.newInstance(1))
    }

    private val mPageAdapter by lazy {
        TitlePageAdapter(supportFragmentManager, mTabFragmentList, mTitle)
    }

    companion object {

        @JvmStatic
        fun startActiveActivity(context: Context) {
            Intent(context, ActiveActivity::class.java).let {
                context.startActivity(it)
            }
        }

    }

    override fun getLayoutResId() = R.layout.layout_activitys

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this,
        ViewModelProvider.NewInstanceFactory()).get(ActiveViewModel::class.java)

    override fun initView() {
        loadingHelper = ToolbarUtils.setToolbar(this, "活动", NavIconType.BACK, true)
        mBind = mBinding as LayoutActivitysBinding
        mBind.apply {
            viewPager.apply {
                offscreenPageLimit = mTabFragmentList.size
                adapter = mPageAdapter
                addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                    override fun onPageScrolled(
                        position: Int,
                        positionOffset: Float,
                        positionOffsetPixels: Int,
                    ) {
                    }

                    override fun onPageSelected(position: Int) {
                        slidingTabLayout.currentTab = position
                        for (i in 0 until mPageAdapter.count) {
                            val isSelect = i == position
                            slidingTabLayout.getTitleView(i).textSize = if (isSelect) 18f else 13f
                        }
                    }

                    override fun onPageScrollStateChanged(state: Int) {}
                })
            }
            slidingTabLayout.apply {
                setViewPager(viewPager)
                setSnapOnTabClick(true)
                setOnTabSelectListener(object : OnTabSelectListener {
                    override fun onTabSelect(position: Int) {
                        viewPager.currentItem = position
                    }

                    override fun onTabReselect(position: Int) {}
                })
                getTitleView(0).textSize = 18f
            }
        }
    }

    override fun initData() {}

    override fun startObserve() {}

}