package com.imoblife.now.activity.main

import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.adapter.CourseAdapterCommentUtils
import com.imoblife.now.bean.Course
import com.imoblife.now.databinding.LayoutViewPopularCoursesBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.util.ImageLoader

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/8/28
 * 描   述：热门课程Top10 - adapter
 */
class PopularCoursesAdapter :
    BaseQuickAdapter<Course, BaseViewHolder>(R.layout.layout_view_popular_courses) {

    override fun convert(holder: BaseViewHolder, item: Course) {
        holder.getBinding(LayoutViewPopularCoursesBinding::bind).apply {
            val position = holder.layoutPosition
            ImageLoader.loadImageUrl(mContext, item.thumb_img, img)
            if (position < 3) {
                imgLabel.visibility = View.VISIBLE
                ImageLoader.loadImageLocal(
                    mContext, when (position) {
                        0 -> R.mipmap.img_popular_courses_content_label_gold
                        1 -> R.mipmap.img_popular_courses_content_label_red
                        2 -> R.mipmap.img_popular_courses_content_label_silver
                        else -> R.mipmap.img_popular_courses_content_label_gold
                    }, imgLabel
                )
            } else {
                imgLabel.visibility = View.GONE
            }
            tvTitle.text = item.title
            tvSubTitle.text = item.subtitle_new
            CourseAdapterCommentUtils.setCourseSession(tvSeries, item)
            holder.itemView.onDebounceClickListener {
                CourseAdapterCommentUtils.clickOpenCourseDetail(mContext, item, "")
            }
        }
    }

}