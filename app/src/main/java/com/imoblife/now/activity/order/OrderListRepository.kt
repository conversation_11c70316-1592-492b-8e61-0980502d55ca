package com.imoblife.now.activity.order

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.OrderListEntity
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiService
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers
import com.imoblife.now.util.EmptyUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/10/17
 * 描   述：订单列表 - Repository
 */
class OrderListRepository : BaseRepository() {

    // 分页页码
    private var mCursorPage: Int = 1

    /**
     * 获取订单列表
     */
    fun getOnSiteNotifications(
        initPage: Boolean,
        liveData: MutableLiveData<UiStatus<List<OrderListEntity>>>
    ) {
        if (initPage) mCursorPage = 1
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .getOrderList(mCursorPage)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<OrderListEntity>>>() {
                override fun onSuccess(response: BaseResult<List<OrderListEntity>>?) {
                    response?.result?.let {
                        if (EmptyUtils.isNotEmpty(it)) {
                            if (mCursorPage == 1) {
                                liveData.value = UiStatus(true, it, null, Status.REFRESHSUCCESS)
                            } else {
                                liveData.value = UiStatus(true, it, null, Status.MORESUCCESS)
                            }
                            mCursorPage++
                        } else {
                            checkStatus(liveData)
                        }
                    } ?: checkStatus(liveData)
                }

                override fun onFailure(msg: String?) {
                    if (mCursorPage == 1) {
                        liveData.value = UiStatus(false, null, null, Status.FAILED)
                    } else {
                        liveData.value = UiStatus(false, null, null, Status.MOREFAIL)
                    }
                }
            })
    }

    /**
     * 检查 - 订单列表 - cursor 状态
     */
    private fun checkStatus(liveData: MutableLiveData<UiStatus<List<OrderListEntity>>>) {
        if (mCursorPage == 1) {
            liveData.value = UiStatus(true, null, null, Status.EMPTYDATA)
        } else {
            liveData.value = UiStatus(true, null, null, Status.NOMOREDATA)
        }
    }

}