package com.imoblife.now.activity.mood

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.core.content.ContextCompat
import androidx.core.transition.doOnEnd
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.ActivityStackManager
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.MoodLogTagItemAdapter
import com.imoblife.now.bean.Mood
import com.imoblife.now.bean.MoodTag
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.ActivityMoodInputBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.PaperCache
import com.imoblife.now.util.SpUtil
import com.imoblife.now.util.ToastUtils
import com.kongzue.dialog.v3.WaitDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MoodInputActivity : BaseVMActivity<MoodModel>() {

    private var inputContent = ""

    private var mood: Mood? = null

    // adapter - 心情记录 - 历史 - log列表Item - tabItem
    private val mMoodLogTagItemAdapter by lazy(LazyThreadSafetyMode.NONE) {
        MoodLogTagItemAdapter(
            true
        )
    }

    companion object {
        @JvmStatic
        fun startInputMood(context: Context, mood: Mood, bundle: Bundle) {
            context.startActivity(
                Intent(context, MoodInputActivity::class.java).putExtra(
                    ConsIntent.BUNDLE_MOOD,
                    mood
                ), bundle
            )
        }
    }

    private lateinit var mBind: ActivityMoodInputBinding

    override fun getLayoutResId() = R.layout.activity_mood_input

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .keyboardEnable(true)
            .setOnKeyboardListener { isPopup: Boolean, _: Int ->
                mBind.motionLayout.apply {
                    if (isPopup) {
                        setTransition(R.id.end, R.id.hideKeyBoard)
                        transitionToEnd()

                    } else {
                        setTransition(R.id.hideKeyBoard, R.id.end)
                        transitionToEnd()
                    }
                }
            }
            .init()
    }

    override fun superInit(intent: Intent?) {
        if (hasExtra(intent, ConsIntent.BUNDLE_MOOD)) {
            intent?.getSerializableExtra(ConsIntent.BUNDLE_MOOD)
                ?.let { mViewModel.setSelectMood(it as Mood) }
        }
    }

    override fun initVM() =
        ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(MoodModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityMoodInputBinding
        mBind.apply {
            superTextView.apply {
                shaderStartColor = Color.parseColor(
                    SpUtil.getInstance()
                        .getStringValue(ConsSp.SP_KEY_MOOD_GRADIENT_START_COLOR, "#D4949E")
                )
                shaderEndColor = Color.parseColor(
                    SpUtil.getInstance()
                        .getStringValue(ConsSp.SP_KEY_MOOD_GRADIENT_END_COLOR, "#BF8CAB")
                )
            }
            moodToolbar.apply {
                tvTitle.text = getString(R.string.string_edit_journal_txt)
                leftImg.apply {
                    isEnabled = false
                    leftImg.setImageResource(R.mipmap.icon_back_white)
                    setOnClickListener { finish() }
                }
                rightTitle.apply {
                    isEnabled = false
                    text = getString(R.string.string_completed)
                    setTextColor(
                        ContextCompat.getColor(
                            this@MoodInputActivity,
                            R.color.color_719CD7
                        )
                    )
                    solid = ContextCompat.getColor(this@MoodInputActivity, R.color.white)
                    setOnClickListener {
                        if (UserMgr.getInstance().isLogin) {
                            saveMood()
                        } else {
                            ToastUtils.showShortToastCenter(context.getString(R.string.string_save_mood_tips))
                            LoginCenter.getInstance()
                                .loginControl(this@MoodInputActivity, LoginCenter.LoginStyleDialog)
                        }
                    }
                }
            }
            moodContentEdit.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {}
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    inputContent = moodContentEdit.text.toString()
                }
            })
            recyclerView.layoutManager =
                FlexboxLayoutManager(this@MoodInputActivity).also { flexboxLayoutManager ->
                    flexboxLayoutManager.flexWrap = FlexWrap.WRAP
                    flexboxLayoutManager.flexDirection = FlexDirection.ROW
                    flexboxLayoutManager.justifyContent = JustifyContent.FLEX_START
                }
            recyclerView.adapter = mMoodLogTagItemAdapter
            lifecycleScope.launch(Dispatchers.Main) {
                val moodTagList = withContext(Dispatchers.IO) {
                    (PaperCache.read<List<MoodTag>>(ConsCommon.MOOD_DIARY_TAGS_JSON))
                }
                mMoodLogTagItemAdapter.setNewData(moodTagList)
            }
            var first = true
            window?.sharedElementEnterTransition?.doOnEnd {
                if (first) {
                    motionLayout.apply {
                        post {
                            setTransition(R.id.start, R.id.end)
                            transitionToEnd {
                                setTransition(R.id.end, R.id.emojiRotate)
                                transitionToEnd {
                                    moodContentEdit.isEnabled = true
                                    moodToolbar.leftImg.isEnabled = true
                                    moodToolbar.rightTitle.isEnabled = true
                                    first = false
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun saveMood() {
        WaitDialog.show(this, getString(R.string.loading))
        val selectedTagIds = StringBuffer()
        mMoodLogTagItemAdapter.data.map {
            if (it.isChecked) {
                selectedTagIds.append(it.id.toString().plus(","))
            }
        }
        mood?.id?.let { mViewModel.saveSelectedMood(it, selectedTagIds.toString(), inputContent) }
    }

    override fun initData() {
        mViewModel.apply { getMoodTag() }
    }

    override fun startObserve() {
        mViewModel.apply {
            moodSelected.observe(this@MoodInputActivity) {
                mood = it
                (mBinding as ActivityMoodInputBinding).mood = it
            }
            moodSaved.observe(this@MoodInputActivity) {
                WaitDialog.dismiss()
                if (it.isSuccess) {
                    mood?.apply {
                        ActivityStackManager.getInstance()
                            .finishSingleActivityByClass(MoodActivity::class.java)
                        MoodRecordActivity.startRecordMood(this@MoodInputActivity, id)
                        finish()
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == ConsRequestCode.SEND_REQUEST_LOGIN_SAVE_MOOD) {
                saveMood()
            }
        }
    }

    override fun onBackPressed() {}

}