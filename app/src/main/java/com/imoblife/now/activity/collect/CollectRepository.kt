package com.imoblife.now.activity.collect

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.*
import com.imoblife.now.bean.Collection
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.model.CourseMgr
import com.imoblife.now.model.TrackMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.net.*
import io.reactivex.Observable
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe
import io.reactivex.functions.Function
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class CollectRepository : BaseRepository() {
    fun addCollectCourse(course:Course,liveData: MutableLiveData<UiStatus<Boolean>>) {
        ApiClient.getInstance().createService(ApiService::class.java)
                .addCollectUserCourse(course.id).enqueue(object : Callback<ApiResult<*>?> {
                    override fun onResponse(call: Call<ApiResult<*>?>, response: Response<ApiResult<*>?>) {
                        if (response.body() != null) {
                            val collect = response.body()?.collect
                            val isCollect = "true" == collect
                            course.isIs_collect = isCollect
                            CourseMgr.getInstance().replaceCourse(course)
                            liveData.postValue(UiStatus(true,isCollect,null,null))
                        }
                    }
                    override fun onFailure(call: Call<ApiResult<*>?>, t: Throwable) {
                        liveData.postValue(UiStatus( false, null,  t.message, null))
                    }
                })

    }
    fun addCollectTrack(courseId:Int,trackId:Int,liveData:MutableLiveData<UiStatus<Boolean>>){
        ApiClient.getInstance()
                .createService(ApiService::class.java)
                .addCollectTrack(courseId, trackId)
                .compose(RxSchedulers.compose())
                .subscribe(object : BaseObserver<BaseResult<CollectTrack?>?>() {
                    override fun onSuccess(response: BaseResult<CollectTrack?>?) {
                        try {
                            response?.result?.isCollect?.let { TrackMgr.getInstance().setTrackIsLiked(trackId, it) }
                            liveData.value= UiStatus(true,response?.result?.isCollect,null,null)
                        } catch (e: Throwable) {
                            e.printStackTrace()
                        }
                    }
                })
    }
    fun getCollectCourse(isReadCache: Boolean,liveData:MutableLiveData<UiStatus<List<Course>>>): MutableLiveData<UiStatus<List<Course>>> {
//        var liveData=MutableLiveData<UiStatus<List<Course>>>()
        ApiClient.getInstance().createService(ApiService::class.java)
                .getCollection(ConsCommon.PAGE_COLLECTION_COURSE)
//                .onErrorResumeNext { _: Throwable? -> Observable.never() }
//                .map(saveCollectToDb())
//                .publish(PublishFunction.getFunction(getCacheObservable<Any>(modelId, isReadCache)))
                .compose(RxSchedulers.compose())
                .subscribe(object : BaseObserver<BaseResult<Collection?>?>() {
                    override fun onFailure(msg: String?) {
                        liveData.value= UiStatus(false,null,msg,Status.FAILED)
                    }
                    override fun onSuccess(response: BaseResult<Collection?>?) {
                         liveData.value= UiStatus(true,response?.result?.plan,null,Status.REFRESHSUCCESS)
                    }
                })
        return liveData
    }
    fun getCollectTrack(liveData: MutableLiveData<UiStatus<List<CollectTrackBean>>>): MutableLiveData<UiStatus<List<CollectTrackBean>>> {
        ApiClient.getInstance().createService(ApiService::class.java)
                .getCollectTracks(ConsCommon.PAGE_COLLECTION_COURSE)
                .compose(RxSchedulers.compose())
                .subscribe(object : BaseObserver<BaseResult<List<CollectTrackBean>?>?>() {
                    override fun onFailure(msg: String?) {
                        liveData.value=UiStatus(false,null,msg,Status.FAILED)
                    }
                    override fun onSuccess(response: BaseResult<List<CollectTrackBean>?>?) {
                        val result=response?.result
                        liveData.value= UiStatus(true,result,null,Status.REFRESHSUCCESS)
                    }
                })
        return liveData
    }


    private fun <T> saveCollectToDb(): Function<T, T?>? {
        return Function { t: T? ->
            if (t != null && t is BaseResult<*>) {
                val baseResult = t as BaseResult<Collection?>?
                if (baseResult != null && baseResult.isSuccess && baseResult.result != null) {
                    val courses = baseResult.result!!.plan
                    Observable.fromIterable(courses).subscribe { course: Course ->
                        course.isIs_collect = true
                        CourseMgr.getInstance().replaceCourse(course)
                    }
                }
            }
            t
        }
    }

    private fun <S> getCacheObservable(modelId: Int, isReadCache: Boolean): Observable<*>? {
        return Observable.create(ObservableOnSubscribe { emitter: ObservableEmitter<S> ->
            try {
                val baseResult: BaseResult<Collection?> = BaseResult()
                if (isReadCache && UserMgr.getInstance().isLogin) {
                    if (modelId == 0) {
                        val courses = CourseMgr.getInstance().listByFavorite
                        val collection = Collection()
                        if (courses != null) {
                            collection.plan = courses
                            baseResult.code = 200
                            baseResult.result = collection
                        }
                    } else {
                        baseResult.setCode(1000)
                    }
                }
                emitter.onNext(baseResult as S)
            } catch (ignored: Exception) {
            }
        } as ObservableOnSubscribe<S>)
    }
}