package com.imoblife.now.activity.diary

import com.imoblife.now.mvvm.BaseViewModel
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.DiaryList

class DiaryViewModel : BaseViewModel<Any?>() {
    private val mDiaryRepository by lazy {
        DiaryRepository()
    }
    val mDiaryChangeLiveData = DiaryChangeLiveData.liveData
    val diaryLiveData = MutableLiveData<UiStatus<DiaryList>>()
    val userActionLog = MutableLiveData<UiStatus<Any>>()
    val userDeleteDiary = MutableLiveData<UiStatus<Triple<Int,Int,Int>>>()

    fun getDiary(diary: Int) {
        mDiaryRepository.getDiary(diary, diaryLiveData)
    }

    fun userActionLog(
        action: String,
        data: String
    ) {
        mDiaryRepository.userActionLog(action, data, userActionLog)
    }
    fun editDiary(diaryId: Int,content:String){
        mDiaryRepository.editDiary(diaryId,content,userActionLog)
    }
    fun deleteDiary(groupId:Int,childId:Int,diaryId: Int){
        mDiaryRepository.deleteDiary(groupId,childId,diaryId,userDeleteDiary)
    }
}