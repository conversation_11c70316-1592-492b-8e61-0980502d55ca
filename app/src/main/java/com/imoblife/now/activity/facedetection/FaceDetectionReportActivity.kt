package com.imoblife.now.activity.facedetection

import android.animation.ValueAnimator
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.MyApplication
import com.imoblife.now.R
import com.imoblife.now.activity.ShareActivity
import com.imoblife.now.activity.member.SubscribeActivity
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.Course
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.LayoutAcFaceDetectionReportBinding
import com.imoblife.now.ext.animAlphaShow
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.ext.rotationAnimator
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.ScreenshotUtils
import com.jaychang.st.SimpleText

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - 报告 - activity
 */
class FaceDetectionReportActivity : BaseVMActivity<FaceDetectionViewModel>() {

    companion object {

        fun startActivity(context: Context, reportId: String) {
            Intent(context, FaceDetectionReportActivity::class.java).let {
                it.putExtra(ConsIntent.BUNDLE_REPORT_ID, reportId)
                context.startActivity(it)
            }
        }

    }

    private lateinit var mBind: LayoutAcFaceDetectionReportBinding

    private var mReportId = ""

    private val mFaceDetectionRecommendedExercisesAdapter by lazy(LazyThreadSafetyMode.NONE) { FaceDetectionRecommendedExercisesAdapter() }

    private val mAdapterData by lazy(LazyThreadSafetyMode.NONE) { mutableListOf<Course>() }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentBar()
            .statusBarDarkFont(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_face_detection_report

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_REPORT_ID)) {
                mReportId = it.getStringExtra(ConsIntent.BUNDLE_REPORT_ID) ?: ""
            }
        }
    }

    override fun initVM() = ViewModelProvider(this)[FaceDetectionViewModel::class.java]

    override fun initView() {
        mBind = mBinding as LayoutAcFaceDetectionReportBinding
        mBind.apply {
            MyApplication.mFaceDetectionShareLittleredbook = true
            rvRecommendedExercises.addItemDecoration(CommonItemDecoration(6.dp, 6.dp, 20.dp, 20.dp, 20.dp, 0))
            rvRecommendedExercises.adapter = mFaceDetectionRecommendedExercisesAdapter

            refreshLockUI()

            imgLayer.onDebounceClickListener { }
            imgLockLayer.onDebounceClickListener {
                SensorsDataEvent.facialEmotionMembershipClick()
                SubscribeActivity.openSubscribeActivity(this@FaceDetectionReportActivity, pageOrigin = 25)
            }
            imgClose.onDebounceClickListener { onBackPressed() }
            stvShare.onDebounceClickListener {
                Handler(Looper.getMainLooper()).post {
                    container.let {
                        stvShare.visibility = View.INVISIBLE
                        stvPractice.visibility = View.INVISIBLE
                        val bitmap = ScreenshotUtils.shotScrollView(container)
                        bitmap?.apply {
                            ShareActivity.shareBitmap(
                                this@FaceDetectionReportActivity,
                                bitmap,
                                false,
                                1
                            )
                        }
                    }
                }
            }
            stvPractice.onDebounceClickListener {
                if (UserMgr.getInstance().isHasNowVip) {
                    if (mAdapterData.isNotEmpty()) {
                        mFaceDetectionRecommendedExercisesAdapter.goCourse(mAdapterData[0])
                    }
                } else {
                    SubscribeActivity.openSubscribeActivity(this@FaceDetectionReportActivity, pageOrigin = 25)
                }
            }

            Looper.myQueue().addIdleHandler {
                imgEmotionalStateBg.rotationAnimator {
                    imgPressure.animAlphaShow()
                    statePressure.animAlphaShow()

                    imgAttackIndex.animAlphaShow()
                    stateAttackIndex.animAlphaShow()

                    imgInternalFrictionIndex.animAlphaShow()
                    stateInternalFrictionIndex.animAlphaShow()

                    imgHappinessIndex.animAlphaShow()
                    stateHappinessIndex.animAlphaShow()

                    imgFatigueValue.animAlphaShow()
                    stateFatigueValue.animAlphaShow()

                    imgAnxietyLevel.animAlphaShow()
                    stateAnxietyLevel.animAlphaShow()
                }
                false
            }
        }
    }

    /**
     * true为限免非会员；false为非会员
     *
     * @receiver LayoutAcFaceDetectionReportBinding
     */
    private fun LayoutAcFaceDetectionReportBinding.refreshLockUI() {
        if (!UserMgr.getInstance().isHasNowVip && ConfigMgr.getInstance().config.isFace_detection_report_vip_limited_exemption) {
            imgLayer.visibility = View.GONE
            imgLockLayer.visibility = View.GONE
        } else {
            if (UserMgr.getInstance().isHasNowVip) {
                imgLayer.visibility = View.GONE
                imgLockLayer.visibility = View.GONE
            } else {
                imgLayer.visibility = View.VISIBLE
                imgLockLayer.visibility = View.VISIBLE
                SensorsDataEvent.facialEmotionMembershipUnlockShow()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBind.apply {
            stvShare.visibility = View.VISIBLE
            stvPractice.visibility = View.VISIBLE
        }
    }

    override fun initData() {
        mViewModel.getReportDetail(mReportId)
    }

    override fun startObserve() {
        mViewModel.reportDetail.observe(this) { uiStatus ->
            if (uiStatus.isSuccess) {
                uiStatus.successData?.let { entity ->
                    mBind.apply {
                        if (EmptyUtils.isNotEmpty(entity.detail)) {
                            // 情绪状态
                            if (!entity.detail.five_graph_data.isNullOrEmpty()) {
                                MyApplication.mFaceDetectionSynthesisBitmap?.let { bitmap -> civImg.setImageBitmap(bitmap) } ?: run {
                                    ImageLoader.loadImageUrl(
                                        this@FaceDetectionReportActivity,
                                        entity.detail.img,
                                        civImg
                                    )
                                }
                                if (entity.detail.five_graph_data.size == 6) {
                                    val fiveGraphDataEntity = entity.detail.five_graph_data
                                    statePressure.setData(fiveGraphDataEntity[0])
                                    stateAttackIndex.setData(fiveGraphDataEntity[1])
                                    stateInternalFrictionIndex.setData(fiveGraphDataEntity[2])
                                    stateHappinessIndex.setData(fiveGraphDataEntity[3])
                                    stateFatigueValue.setData(fiveGraphDataEntity[4])
                                    stateAnxietyLevel.setData(fiveGraphDataEntity[5])
                                }
                            }

                            // 情绪总分
                            if (EmptyUtils.isNotEmpty(entity.detail.score)) {
                                val scoreEntity = entity.detail.score
                                tvTotalEmotionalScore.text = scoreEntity.score_value.toString()
                                tvTotalEmotionalScoreLevel.text = scoreEntity.score_title
                                tvTotalEmotionalScoreContent.text = scoreEntity.score_detail
                                // 情绪总分 - progress - 0.065 ~ 1
                                val ratio = if (scoreEntity.score_value.toFloat() / 100 < 0.065) 0.065F else scoreEntity.score_value.toFloat() / 100
                                val heightAnimator = ValueAnimator.ofFloat(0F, ratio)
                                heightAnimator.duration = 1000
                                heightAnimator.addUpdateListener { animation ->
                                    val progress = animation.animatedValue as Float
                                    ConstraintSet().let { set ->
                                        set.clone(containerTotalEmotionalScoreProgress)
                                        set.setGuidelinePercent(R.id.glVtTotalEmotionalScore, progress)
                                        set.applyTo(containerTotalEmotionalScoreProgress)
                                    }
                                }
                                heightAnimator.start()
                            }

                            // 你的情绪占比
                            if (!entity.detail.emotional_detail.isNullOrEmpty()) {
                                val emotionalDetailEntity = entity.detail.emotional_detail
                                fdrpoevHate.setData(emotionalDetailEntity[0])
                                fdrpoevRepressed.setData(emotionalDetailEntity[1])
                                fdrpoevSadness.setData(emotionalDetailEntity[2])
                                fdrpoevJoyful.setData(emotionalDetailEntity[3])
                                fdrpoevScare.setData(emotionalDetailEntity[4])
                                fdrpoevAmazed.setData(emotionalDetailEntity[5])
                                fdrpoevIrritability.setData(emotionalDetailEntity[6])
                            }

                            // 你的压力程度
                            if (EmptyUtils.isNotEmpty(entity.detail.stress_detail)) {
                                val stressDetailEntity = entity.detail.stress_detail
                                imgYourLevelOfStress.setImageDrawable(
                                    ContextCompat.getDrawable(
                                        this@FaceDetectionReportActivity, when (stressDetailEntity.score) {
                                            in 0 until 20 -> R.mipmap.img_face_detection_pressure_progress_one
                                            in 20 until 40 -> R.mipmap.img_face_detection_pressure_progress_two
                                            in 40 until 60 -> R.mipmap.img_face_detection_pressure_progress_three
                                            in 60 until 80 -> R.mipmap.img_face_detection_pressure_progress_four
                                            in 80..100 -> R.mipmap.img_face_detection_pressure_progress_five
                                            else -> R.mipmap.img_face_detection_pressure_progress_three
                                        }
                                    )
                                )
                                tvProgressYourLevelOfStress.text = stressDetailEntity.score.toString()
                                tvContentYourLevelOfStress.text = stressDetailEntity.score_detail
                                val rotation = when (stressDetailEntity.score) {
                                    0 -> 270F
                                    in 1 until 50 -> stressDetailEntity.score.toFloat() / 49 * 90 + 270
                                    50 -> 0F
                                    in 51 until 100 -> (stressDetailEntity.score.toFloat() - 51) / 49 * 90
                                    100 -> 90F
                                    else -> 0F
                                }
                                ConstraintSet().let { set ->
                                    set.clone(containerYourLevelOfStress)
                                    set.setRotation(R.id.imgIndicatorYourLevelOfStress, rotation)
                                    set.constrainCircle(R.id.imgIndicatorYourLevelOfStress, R.id.viewPointYourLevelOfStress, resources.getDimension(R.dimen.qb_px_100).toInt(), rotation)
                                    set.applyTo(containerYourLevelOfStress)
                                }
                            }

                            // 你的焦虑指数
                            if (EmptyUtils.isNotEmpty(entity.detail.anxiety_detail)) {
                                val anxietyDetailEntity = entity.detail.anxiety_detail
                                tvScoreYourAnxietyIndex.text = SimpleText
                                    .from("${anxietyDetailEntity.score}分")
                                    .first("分")
                                    .size(20)
                                tvContentYourAnxietyIndex.text = anxietyDetailEntity.score_detail
                                // 焦虑指数 - progress - 0.2; 0.5; 0.82
                                val score = when (anxietyDetailEntity.score) {
                                    in 0..33 -> 0.2F
                                    in 34..66 -> 0.5F
                                    in 67..100 -> 0.82F
                                    else -> 0.5F
                                }
                                ConstraintSet().let { set ->
                                    set.clone(containerYourAnxietyIndex)
                                    set.setGuidelinePercent(R.id.glProgressYourAnxietyIndex, score)
                                    set.applyTo(containerYourAnxietyIndex)
                                }
                            }

                            // 你的疲惫指数
                            if (EmptyUtils.isNotEmpty(entity.detail.friction_detail)) {
                                val frictionDetailEntity = entity.detail.friction_detail
                                arcSeekBarYourFatigueIndex.setProgressColor(Color.parseColor("#9FFFA9"), Color.parseColor("#01BDB9"))
                                arcSeekBarYourFatigueIndex.progress = frictionDetailEntity.score
                                tvScoreYourFatigueIndex.text = frictionDetailEntity.score.toString()
                                tvContentYourFatigueIndex.text = frictionDetailEntity.score_detail
                            }
                        }

                        // 根据你当前情绪状态推荐练习
                        if (!entity.recommend_course.isNullOrEmpty()) {
                            mAdapterData.clear()
                            mAdapterData.addAll(entity.recommend_course)
                            mFaceDetectionRecommendedExercisesAdapter.setNewData(entity.recommend_course)
                        }
                    }
                }
            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        when (event?.eventCode) {
            ConsEventCode.LOGIN_CHANGE_EVENT, ConsEventCode.CHANGE_SUBSCRIBE_EVENT, ConsEventCode.PAY_SUCCESS_EVENT -> mBind.refreshLockUI()
            else -> {}
        }
    }

    override fun onDestroy() {
        MyApplication.mFaceDetectionShareLittleredbook = false
        super.onDestroy()
    }

}