package com.imoblife.now.activity.category

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.coorchice.library.SuperTextView
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.util.ImageLoader
import com.makeramen.roundedimageview.RoundedImageView
import java.text.DecimalFormat

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - 读书
 */
class CategoryBookAdapter :
    BaseQuickAdapter<CategoryNavigationItemEntity, BaseViewHolder>(R.layout.item_book_layout) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationItemEntity?) {
        item?.apply {
            val itemBookImg = holder.getView<RoundedImageView>(R.id.item_book_img)
            val itemBookTitleTxt = holder.getView<TextView>(R.id.item_book_title_txt)
            val itemBookSubTitleTxt = holder.getView<TextView>(R.id.item_book_sub_title_txt)
            val itemBookAuthorTv = holder.getView<TextView>(R.id.item_book_author_tv)
            val itemBookCountTv = holder.getView<TextView>(R.id.item_book_count_tv)
            val itemBookSmallCountTxt =
                holder.getView<SuperTextView>(R.id.item_book_small_count_txt)

            itemBookSmallCountTxt.visibility = View.GONE
            ImageLoader.loadImageUrl(mContext, thumb_img, itemBookImg)
            itemBookTitleTxt.text = title
            itemBookSubTitleTxt.text = subtitle
            itemBookAuthorTv.text = label
            itemBookCountTv.text =
                if (!TextUtils.isEmpty(duration_title)) getListerCount(duration_title.toInt()) else ""
            holder.itemView.setOnClickListener { pageRoute(mContext, holder.layoutPosition) }
        }
    }

    fun getListerCount(splay_count: Int) = if (splay_count >= 10000) {
        val countF: Double = splay_count / 10000.0
        // 构造方法的字符格式这里如果小数不足2位,会以0补足.
        val decimalFormat = DecimalFormat(".0")
        decimalFormat.format(countF) + "万"
    } else {
        splay_count.toString() + ""
    }

}