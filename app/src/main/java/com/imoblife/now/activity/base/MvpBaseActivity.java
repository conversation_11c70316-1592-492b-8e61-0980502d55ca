package com.imoblife.now.activity.base;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.gyf.immersionbar.ImmersionBar;
import com.imoblife.commlibrary.mvp.BaseMvpActivity;
import com.imoblife.commlibrary.mvp.MvpBasePresenter;
import com.imoblife.now.ActivityStackManager;
import com.imoblife.now.R;
import com.imoblife.now.activity.main.MainActivity;
import com.imoblife.now.model.ConfigMgr;
import com.imoblife.now.util.ToastUtils;
import com.kongzue.dialog.v3.WaitDialog;
import java.lang.ref.WeakReference;
public abstract class MvpBaseActivity<P extends MvpBasePresenter> extends BaseMvpActivity {
  @Override
  protected P getPresenter() {
    return (P) super.getPresenter();
  }
  @Override
  protected void onCreate(Bundle savedInstanceState) {
    //竖屏
//    if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
//      setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
//    }
    super.onCreate(savedInstanceState);
    //初始化沉浸式
    initImmersionBar();
  }

  protected void initImmersionBar() {
    ImmersionBar.with(this)
            .statusBarColor(R.color.white)
            .statusBarDarkFont(true)
            .fitsSystemWindows(true)
            .init();
  }
  protected void showWaitLoading(){
    showWaitLoading(null);
  }
  protected void showWaitLoading(String msg){
    hideWaitLoading();
    Activity activity= ActivityStackManager.getInstance().getCurrentActivity();
    if (activity!=null && activity instanceof Activity && !activity.isFinishing()){
      try {
        WeakReference weakReference=new WeakReference(activity);
        WaitDialog.show((AppCompatActivity) weakReference.get(),msg);
      }catch (ClassCastException ignored){}
    }
  }
  protected void showWaitLoading(@NonNull Context context, @Nullable String msg){
    if (context instanceof Activity){
      if (!((Activity) context).isFinishing()){
        WaitDialog.show((AppCompatActivity) context,  msg);
      }
    }
  }
  protected void hideWaitLoading(){
    WaitDialog.dismiss();
  }
  @Override
  protected void onPause() {
    super.onPause();
    hideWaitLoading();
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    ToastUtils.hideToast();
  }
  protected void stopRefresh(SwipeRefreshLayout refreshLayout) {
    if (refreshLayout!=null && refreshLayout.isRefreshing()){
      refreshLayout.setRefreshing(false);
    }
  }
  @Override
  public void onBackPressed() {
    if (ActivityStackManager.getInstance().isOpenActivity(MainActivity.class)){
      super.onBackPressed();
    }else {
      MainActivity.Companion.openMainActivity(this, ConfigMgr.getInstance().getConfig().getApp_default_tab(), "");
    }
  }
  @Override
  public void onConfigurationChanged(Configuration newConfig) {
    if (newConfig.fontScale != 1) { getResources(); }
    super.onConfigurationChanged(newConfig);
  }
  @Override
  public Resources getResources() {
    Resources res = super.getResources();
    Configuration config=new Configuration();
    config.setToDefaults();
    res.updateConfiguration(config,res.getDisplayMetrics() );
    return res;
  }
}
