package com.imoblife.now.activity.memberchallenge

import android.annotation.SuppressLint
import android.view.View
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.MemberChallengeEntity
import com.imoblife.now.databinding.LayoutItemMemberChallengeBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.custom.Rotate3DFlipView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/19
 * 描   述：会员挑战 - 挑战未知的自己 - adapter
 */
class MemberChallengeAdapter :
    BaseQuickAdapter<MemberChallengeEntity.ListEntity, BaseViewHolder>(R.layout.layout_item_member_challenge) {

//    private var mBlockSkuDialog: (() -> Unit)? = null
//
//    fun setClickListener(block: (() -> Unit)) {
//        mBlockSkuDialog = block
//    }

    private var mBlock: ((position: Int, entity: MemberChallengeEntity.ListEntity) -> Unit)? = null

    fun setClickJoinListener(block: ((position: Int, entity: MemberChallengeEntity.ListEntity) -> Unit)) {
        mBlock = block
    }

    @SuppressLint("ResourceType")
    override fun convert(holder: BaseViewHolder, item: MemberChallengeEntity.ListEntity) {
        holder.getBinding(LayoutItemMemberChallengeBinding::bind).let { layout ->
            layout.container.apply {
                if (currentFlipState == Rotate3DFlipView.FlipState.FRONT_SIDE && item.isFlipped) {
                    flipDuration = 0
                    flipTheView()
                } else if (currentFlipState == Rotate3DFlipView.FlipState.BACK_SIDE && !item.isFlipped) {
                    flipDuration = 0
                    flipTheView()
                }
            }
            // 布局 - 正面
            layout.includeFrontContent.apply {
                /*
                    * 0：待解锁
                    * 1：加入
                    * 2：活动中
                    * 3：再次加入
                */
                when (item.status) {
                    0 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_3C3C3C))
                        tvTitle.text = mContext.getString(R.string.string_to_be_unlocked)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_lock
                            )
                        )
                    }
                    1 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_16C1D6))
                        tvTitle.text = mContext.getString(R.string.string_join_txt)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_add
                            )
                        )
                    }
                    2 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_16C1D6))
                        tvTitle.text = mContext.getString(R.string.string_in_activity)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_complete
                            )
                        )
                        tvCompleteCount.visibility = View.GONE
                        jpvProgress.visibility = View.VISIBLE
                        jpvProgress
                            .setProgress(item.rate.toFloat())
                            .startAnimal()
                    }
                    3 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_16C1D6))
                        tvTitle.text = mContext.getString(R.string.string_join_again_txt)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_add
                            )
                        )
                        jpvProgress.visibility = View.GONE
                        tvCompleteCount.visibility = View.VISIBLE
                        tvCompleteCount.text = item.subtitle
                    }
                    else -> {}
                }
                tvLongContent.visibility = View.GONE
                tvContent.text = item.title
                imgSwitch.setOnClickListener {
                    layout.flipTheView(item)
                }
                container.setOnClickListener {
                    item.showSkuDialog()
                }
                llContainer.setOnClickListener {
                    item.joinChallenge(holder.layoutPosition)
                }
            }
            // 布局 - 反面
            layout.includeBackContent.apply {
                when (item.status) {
                    0 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_3C3C3C))
                        tvTitle.text = mContext.getString(R.string.string_to_be_unlocked)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_lock
                            )
                        )
                    }
                    1 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_16C1D6))
                        tvTitle.text = mContext.getString(R.string.string_join_txt)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_add
                            )
                        )
                    }
                    2 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_16C1D6))
                        tvTitle.text = mContext.getString(R.string.string_in_activity)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_complete
                            )
                        )
                    }
                    3 -> {
                        tvTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_16C1D6))
                        tvTitle.text = mContext.getString(R.string.string_join_again_txt)
                        img.setImageDrawable(
                            ContextCompat.getDrawable(
                                mContext,
                                R.mipmap.src_member_challenge_add
                            )
                        )
                    }
                    else -> {}
                }
                tvCompleteCount.visibility = View.GONE
                jpvProgress.visibility = View.GONE
                tvContent.visibility = View.GONE
                tvLongContent.visibility = View.VISIBLE
                tvLongContent.text = item.profile
                imgSwitch.setOnClickListener {
                    layout.flipTheView(item)
                }
                container.setOnClickListener {
                    item.showSkuDialog()
                }
                llContainer.setOnClickListener {
                    item.joinChallenge(holder.layoutPosition)
                }
            }
        }
    }

    /**
     * 翻转View
     */
    private fun LayoutItemMemberChallengeBinding.flipTheView(item: MemberChallengeEntity.ListEntity) {
        item.isFlipped = !item.isFlipped
        container.apply {
            flipDuration = 1000
            flipTheView()
        }
    }

    /**
     * 加入挑战赛
     */
    private fun MemberChallengeEntity.ListEntity.joinChallenge(position: Int) {
        when {
            !UserMgr.getInstance().isLogin -> LoginCenter.getInstance().loginControl(mContext)
//            !UserMgr.getInstance().isHasNowVip -> mBlockSkuDialog?.invoke()
            else -> if (status == 1 || status == 3) mBlock?.invoke(position, this)
        }
    }

    /**
     * 展示Sku弹窗
     */
    private fun MemberChallengeEntity.ListEntity.showSkuDialog() {
        when {
            !UserMgr.getInstance().isLogin -> LoginCenter.getInstance().loginControl(mContext)
//            !UserMgr.getInstance().isHasNowVip -> mBlockSkuDialog?.invoke()
            else -> if (status == 0) ToastUtils.showShortToastCenter(
                mContext.getString(R.string.string_unlock_challenge)
            )
        }
    }

}