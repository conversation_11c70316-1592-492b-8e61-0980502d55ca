package com.imoblife.now.activity.breath

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.BreathShareEntity
import com.imoblife.now.bean.BreathStaticsEntity
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiService
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022-8-5
 * 描   述：BreathRepository
 */
class BreathRepository : BaseRepository() {

    /**
     * 呼吸模式 - 分享数据
     */
    fun getBreathShareData(
        schemaId: Int,
        onlyId: String,
        _data: MutableLiveData<UiStatus<BreathShareEntity>>
    ) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .breathShare(schemaId, onlyId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<BreathShareEntity>>() {
                override fun onSuccess(response: BaseResult<BreathShareEntity>?) {
                    response?.result?.let {
                        _data.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        _data.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    _data.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 呼吸「历史」
     */
    fun getBreathStatics(liveData: MutableLiveData<UiStatus<BreathStaticsEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .breathStatics
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<BreathStaticsEntity>>() {
                override fun onSuccess(response: BaseResult<BreathStaticsEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        liveData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

}