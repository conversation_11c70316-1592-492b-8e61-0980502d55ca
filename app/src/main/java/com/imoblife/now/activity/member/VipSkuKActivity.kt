package com.imoblife.now.activity.member

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Looper
import android.text.TextUtils
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.activity.main.MainActivity
import com.imoblife.now.adapter.SkuStyleVipImgAdapter
import com.imoblife.now.bean.SubSkuInfo
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.ActivityVipSkuKBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.ext.removeAnim
import com.imoblife.now.ext.slideInFromLeft
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.NetworkUtils
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.GridSpaceItemDecoration
import com.imoblife.now.viewmodel.PaymentViewModel
import com.shuyu.gsyvideoplayer.GSYVideoManager

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/7/1
 * 描   述：ob - sku - 二次ob「用户在ob未成单进入首页展示全屏前先弹出抽奖转盘，自动转动，转出后弹出全屏推荐sku，支持1-2个sku」
 */
class VipSkuKActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        fun startActivity(context: Activity) {
            val intent = Intent(context, VipSkuKActivity::class.java)
            context.startActivity(intent)
            context.slideInFromLeft()
        }

    }

    private lateinit var mBind: ActivityVipSkuKBinding

    private var mImgUrl = ""

    private var mSubSkuInfo: SubSkuInfo? = null

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) {
        SkuStyleVipImgAdapter(R.layout.layout_item_hr_sku_vip_img, mDynamicImgSize = false)
    }

    override fun superInit(intent: Intent?) {}

    override fun getLayoutResId() = R.layout.activity_vip_sku_k

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .init()
    }

    override fun initVM() = ViewModelProvider(this)[PaymentViewModel::class.java]

    override fun initView() {
        mBind = mBinding as ActivityVipSkuKBinding
        mBind.apply {
            lifecycle.addObserver(mBind.bottomVipTimerView)
            groupContent.visibility = View.GONE
            groupBottomBtn.visibility = View.GONE
            emptyControlVideo.visibility = View.VISIBLE
            emptyControlVideo.setUp(
                "android.resource://${packageName}/${R.raw.video_second_ob}",
                true,
                ""
            )
            emptyControlVideo.startPlayLogic()
            emptyControlVideo.setOnPlayAutoCompletion {
                emptyControlVideo.visibility = View.GONE
                groupContent.visibility = View.VISIBLE
                groupBottomBtn.visibility = View.VISIBLE
                if (!TextUtils.isEmpty(mImgUrl)) {
                    initWebpImg(img, mImgUrl)
                }
            }
            bottomVipTimerView.setSubmitPayOnClickListener {
                mSubSkuInfo?.let { entity ->
                    if (entity.sku_list.size == 1) {
                        subProtocolPrivacy.isAgreePrivacy(entity.sku_list[0], "二次ob订阅页") {
                            PayCenter.getInstance()
                                .doSubmitPay(this@VipSkuKActivity, entity.sku_list[0])
                        }
                    } else {
                        mAdapter.getSubscribe()?.let {
                            subProtocolPrivacy.isAgreePrivacy(it, "二次ob订阅页") {
                                PayCenter.getInstance().doSubmitPay(this@VipSkuKActivity, it)
                            }
                        } ?: let {
                            ToastUtils.showShortToastCenter(getString(R.string.string_please_select_subscribe))
                        }
                    }
                }
            }
            closeImg.onDebounceClickListener { super.onBackPressed() }
        }
    }

    override fun initData() {
        mViewModel.getSecondObVideoSku()
        Looper.myQueue().addIdleHandler {
            if (!NetworkUtils.isNetworkAvailable()) {
                ToastUtils.showShortToast(getString(R.string.string_ob_questionnaire_no_net_txt))
            }
            false
        }
    }

    override fun startObserve() {
        mViewModel.secondObVideoSku.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    mBind.apply {
                        mSubSkuInfo = entity
                        mImgUrl = entity.banner
                        if (!TextUtils.isEmpty(entity.bg_color)) {
                            container.setBackgroundColor(Color.parseColor(entity.bg_color))
                            bottomVipTimerView.setBgColor(entity.bg_color)
                            subProtocolPrivacy.setBgColor(entity.bg_color)
                        }
                        initWebpImg(img, mImgUrl)
                        ImageLoader.loadImageUrl(
                            this@VipSkuKActivity,
                            entity.close_img,
                            mBind.closeImg,
                            R.mipmap.icon_vip_sku_close
                        )
                        if (!entity.sku_list.isNullOrEmpty()) {
                            if (entity.sku_list.size == 1) {
                                recyclerView.visibility = View.GONE
                                imgSku.visibility = View.VISIBLE
                                ImageLoader.loadImageUrl(
                                    this@VipSkuKActivity,
                                    entity.sku_list[0].background_checked,
                                    imgSku
                                )
                                mBind.subProtocolPrivacy.setData(
                                    entity.sku_list[0],
                                    ConfigMgr.getInstance().config.isAuto_vip_privacy_ob
                                )
                                mBind.bottomVipTimerView.setSubscribeFlagImg(entity.sku_list[0].sku_offer_tag_url)
                            } else {
                                recyclerView.visibility = View.VISIBLE
                                imgSku.visibility = View.INVISIBLE
                                recyclerView.removeAnim()
                                recyclerView.addItemDecoration(
                                    GridSpaceItemDecoration(
                                        2,
                                        10.dp,
                                        10.dp
                                    )
                                )
                                recyclerView.layoutManager =
                                    GridLayoutManager(this@VipSkuKActivity, 2)
                                recyclerView.adapter = mAdapter
                                mAdapter.setActionBlock { _ ->
                                    subProtocolPrivacy.setAgreementCheckboxFalse()
                                    mAdapter.getSubscribe()?.let { sub ->
                                        mBind.subProtocolPrivacy.setData(
                                            sub,
                                            ConfigMgr.getInstance().config.isAuto_vip_privacy_ob
                                        )
                                        mBind.bottomVipTimerView.setSubscribeFlagImg(sub.sku_offer_tag_url)
                                    }
                                }
                                mAdapter.setNewData(entity.sku_list)
                            }
                        }
                        bottomVipTimerView.setPayButtonData(entity.pay_button)
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBind.emptyControlVideo.onVideoResume()
    }

    override fun onPause() {
        super.onPause()
        mBind.emptyControlVideo.onVideoPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        mBind.emptyControlVideo.setVideoAllCallBack(null)
        mBind.emptyControlVideo.release()
        GSYVideoManager.releaseAllVideos()
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        event?.let {
            when (it.eventCode) {
                ConsEventCode.PAY_CANCEL_EVENT, ConsEventCode.PAY_FAIL_EVENT -> {}
                ConsEventCode.PAY_SUCCESS_EVENT -> {
                    MainActivity.openMainActivity(
                        this,
                        ConfigMgr.getInstance().config.app_default_tab,
                        isForceLogin = "Login"
                    )
                }

                ConsEventCode.PAY_IN_PROGRESS_EVENT -> DialogUtil.showWaitLoading(
                    this,
                    getString(R.string.string_order_validation_txt),
                    false
                )

                else -> {}
            }
        }
    }

    /**
     * 加载gif动图 - 1次
     */
    private fun initWebpImg(img: AppCompatImageView, url: String) {
        Glide
            .with(this)
            .asGif()
            .load(url)
            .placeholder(R.mipmap.img_second_gif_test)
            .listener(object : RequestListener<GifDrawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<GifDrawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    return false
                }

                override fun onResourceReady(
                    resource: GifDrawable?,
                    model: Any?,
                    target: Target<GifDrawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    // 只播放一次
                    resource?.setLoopCount(1)
                    return false
                }

            })
            .into(img)
    }

    override fun onBackPressed() {}

}