package com.imoblife.now.activity.main

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.home.ThemeChallengeRvItemAdapter
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.databinding.LayoutAcVipPlanListBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.viewmodel.HomeViewModel

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/10/25
 * 描   述：主题挑战赛 - 列表
 */
class ThemeChallengeListActivity : BaseVMActivity<HomeViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(context: Context) {
            context.startActivity(Intent(context, ThemeChallengeListActivity::class.java))
        }

    }

    private lateinit var mBind: LayoutAcVipPlanListBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) { ThemeChallengeRvItemAdapter() }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(HomeViewModel::class.java)

    override fun getLayoutResId(): Int = R.layout.layout_ac_vip_plan_list

    override fun initView() {
        mLoadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_challenge_txt),
            NavIconType.BACK,
            true
        )
        mBind = mBinding as LayoutAcVipPlanListBinding
        mBind.recyclerView.apply {
            addItemDecoration(CommonItemDecoration(12.dp, 12.dp, 12.dp, 16.dp, 12.dp, 16.dp))
            adapter = mAdapter
        }
    }

    override fun initData() {
        mLoadingHelper.apply {
            showLoadingView()
            setOnReloadListener { mViewModel.getVipPlanList() }
        }
        mViewModel.getThemeChallengeList()
    }

    override fun startObserve() {
        mViewModel.apply {
            themeChallengeList.observe(this@ThemeChallengeListActivity) {
                if (it.isSuccess) {
                    it.successData?.let { list ->
                        if (list.isEmpty()) {
                            mLoadingHelper.showEmptyView()
                        } else {
                            mLoadingHelper.showContentView()
                            mAdapter.setNewData(list)
                        }
                    }
                } else {
                    mLoadingHelper.showErrorView()
                }
            }
        }
    }

}