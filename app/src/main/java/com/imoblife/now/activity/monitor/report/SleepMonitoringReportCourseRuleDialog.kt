package com.imoblife.now.activity.monitor.report

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutViewSleepMonitoringReportCourseRuleBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/7/27
 * 描   述：睡眠历程 - 规则 - dialog
 */
class SleepMonitoringReportCourseRuleDialog(
    mContext: Context,
    private val mTitle: String,
    private val mSubTitle: String
) :
    Dialog(mContext, R.style.dialog) {

    private lateinit var mBind: LayoutViewSleepMonitoringReportCourseRuleBinding

    companion object {

        fun showDialog(context: Context, title: String, subTitle: String) {
            SleepMonitoringReportCourseRuleDialog(context, title, subTitle).showDialog()
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBind = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.layout_view_sleep_monitoring_report_course_rule,
            null,
            false
        )
        setContentView(mBind.root)
        mBind.apply {
            clickProxy = ClickProxy()
            tvTitle.text = mTitle
            tvContent.text = mSubTitle
            executePendingBindings()
        }
        setCancelable(false)
    }

    fun showDialog() {
        try {
            if (isShowing) {
                dismiss()
            }
            super.show()
            val attributes = window?.attributes
            attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window?.attributes = attributes
        } catch (i: Throwable) {
            i.printStackTrace()
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 知道了
                R.id.tvBtn -> dismiss()
                else -> {}
            }
        }

    }

}