package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.MeditationClassListEntity
import com.imoblife.now.bean.MeditationClassShareEntity
import com.imoblife.now.bean.MeditationClassShareParamsEntity
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiServiceAgreedUponUnTheMeditation
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers
import com.imoblife.now.util.EmptyUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-11
 * 描   述：约定冥想_Repository
 */
class AgreedUponInTheMeditationRepository : BaseRepository() {

    private var mPage = 1

    /**
     * 获取冥想班列表
     */
    fun getMeditationClassList(
        initPage: Boolean = false,
        _meditationClassListEntity: MutableLiveData<UiStatus<MeditationClassListEntity>>,
    ) {
        if (initPage) mPage = 1
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .getMeditationClassList(mPage)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationClassListEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationClassListEntity>?) {
                    response?.result?.let {
                        if (EmptyUtils.isNotEmpty(it)) {
                            if (mPage == 1) {
                                _meditationClassListEntity.value =
                                    UiStatus(true, it, null, Status.REFRESHSUCCESS)
                            } else {
                                _meditationClassListEntity.value =
                                    UiStatus(true, it, null, Status.MORESUCCESS)
                            }
                            mPage++
                        } else {
                            checkStatus(_meditationClassListEntity)
                        }
                    } ?: checkStatus(_meditationClassListEntity)
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    if (mPage == 1) {
                        _meditationClassListEntity.value =
                            UiStatus(false, null, null, Status.FAILED)
                    } else {
                        _meditationClassListEntity.value =
                            UiStatus(false, null, null, Status.MOREFAIL)
                    }
                }
            })
    }

    private fun checkStatus(_meditationClassListEntity: MutableLiveData<UiStatus<MeditationClassListEntity>>) {
        if (mPage == 1) {
            _meditationClassListEntity.value = UiStatus(true, null, null, Status.EMPTYDATA)
        } else {
            _meditationClassListEntity.value = UiStatus(true, null, null, Status.NOMOREDATA)
        }
    }

    /**
     * 冥想班分享信息
     */
    fun getMeditationClassShareInfo(
        teamId: Int,
        mutableLiveData: MutableLiveData<UiStatus<MeditationClassShareEntity>>,
    ) {
        ApiClient.getInstance().createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .getShareMeditationClassInfo(teamId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationClassShareEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationClassShareEntity>?) {
                    response?.let {
                        mutableLiveData.value = UiStatus(true, it.result)
                    } ?: let {
                        mutableLiveData.value = UiStatus(false, null)
                    }
                }

                override fun onFailure(msg: String?) {
                    mutableLiveData.value = UiStatus(false, null)
                }
            })
    }

    /**
     * 分享_创建冥想班成功
     */
    fun getMeditationClassShareParams(
        teamId: Int,
        _meditationClassShareParamsEntity: MutableLiveData<UiStatus<MeditationClassShareParamsEntity>>,
    ) {
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .getMeditationClassShareParams(teamId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationClassShareParamsEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationClassShareParamsEntity>?) {
                    response?.result?.let {
                        _meditationClassShareParamsEntity.value =
                            UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    _meditationClassShareParamsEntity.value = UiStatus(isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED)
                }
            })
    }

}