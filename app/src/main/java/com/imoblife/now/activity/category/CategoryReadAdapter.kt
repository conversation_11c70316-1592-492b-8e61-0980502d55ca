package com.imoblife.now.activity.category

import android.text.TextUtils
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.activity.member.SubscribeActivity
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.ImageLoader
import com.jaychang.st.SimpleText
import com.makeramen.roundedimageview.RoundedImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - 阅读
 */
class CategoryReadAdapter :
    BaseQuickAdapter<CategoryNavigationItemEntity, BaseViewHolder>(R.layout.layout_item_category_read) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationItemEntity?) {
        item?.apply {
            val rivImg = holder.getView<RoundedImageView>(R.id.rivImg)
            val tvTitle = holder.getView<AppCompatTextView>(R.id.tvTitle)
            val tvContent = holder.getView<AppCompatTextView>(R.id.tvContent)
            val tvReadNumber = holder.getView<AppCompatTextView>(R.id.tvReadNumber)
            val tvArticleNew = holder.getView<AppCompatImageView>(R.id.im_article_new)
            ImageLoader.loadImageUrl(mContext, thumb_img, rivImg)
            tvTitle.text = title
            tvContent.text = subtitle
            if (item.isShow_new_flag) {
                tvArticleNew.visibility = View.VISIBLE
                tvArticleNew.setImageResource(R.mipmap.icon_course_status_new)
            } else if (ConsCommon.COURSE_TYPE_FREE == item.type_new) {
                if (!UserMgr.getInstance().isHasNowVip) {
                    tvArticleNew.visibility = View.VISIBLE
                    tvArticleNew.setImageResource(R.mipmap.icon_course_status_free)
                } else {
                    tvArticleNew.visibility = View.GONE
                }
            } else {
                tvArticleNew.visibility = View.GONE
            }


            val count = if (!TextUtils.isEmpty(duration_title)) duration_title.toInt() else 0
            tvReadNumber.text =
                SimpleText.from(mContext.getString(R.string.string_read_number, count))
                    .first(count.toString()).textColor(R.color.main_color)
            holder.itemView.setOnClickListener {
                if (ConsCommon.COURSE_TYPE_FREE == item.type_new || UserMgr.getInstance().isHasNowVip) {
                    pageRoute(mContext, holder.layoutPosition)
                } else {
                    SubscribeActivity.openSubscribeActivity(mContext)
                }
            }
        }
    }

}