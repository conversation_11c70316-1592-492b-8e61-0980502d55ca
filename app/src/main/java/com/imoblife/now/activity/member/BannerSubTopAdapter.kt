package com.imoblife.now.activity.member

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.SubTopEntity
import com.imoblife.now.databinding.LayoutItemBannerSubBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.ImageLoader
import com.youth.banner.adapter.BannerAdapter

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/10/23
 * 描   述：订阅中心 - top - banner - Adapter
 */
class BannerSubTopAdapter(data: List<SubTopEntity.ShowCardEntity>) :
    BannerAdapter<SubTopEntity.ShowCardEntity, BannerSubTopAdapter.BannerAdHolder>(data) {

    override fun onCreateHolder(parent: ViewGroup?, viewType: Int) = BannerAdHolder(
        LayoutInflater.from(parent!!.context)
            .inflate(R.layout.layout_item_banner_sub, parent, false)
    )

    @SuppressLint("SetTextI18n")
    override fun onBindView(
        holder: BannerAdHolder,
        data: SubTopEntity.ShowCardEntity,
        position: Int,
        size: Int
    ) {
        holder.mBind?.apply {
            val context = holder.itemView.context
            if (data.member_level != 3) {
                ImageLoader.loadImageUrl(context, data.background_image, rivBg)
                ImageLoader.loadImageUrl(context, data.top_left_icon, img)
                ImageLoader.loadImageUrl(context, data.top_left_icon, civImg)
                ImageLoader.loadImageUrl(context, data.top_right_icon, rivLabelEnd)
                tvContent.text = data.top_title
                tvContent.setTextColor(Color.parseColor(data.top_title_color))
                tvVipType.text = data.top_title
                tvVipType.setTextColor(Color.parseColor(data.top_title_color))
                tvVipDate.text = data.bottom_title
                tvVipDate.setTextColor(Color.parseColor(data.bottom_title_color))
            }

            // 0 - 未登录；1 - 非会员；2 - 会员「各种类型会员」; 3 - 默认
            when (data.member_level) {
                0, 1 -> {
                    groupNoVip.visibility = View.VISIBLE
                    groupVip.visibility = View.GONE
                }

                2, 3 -> {
                    groupNoVip.visibility = View.GONE
                    groupVip.visibility = View.VISIBLE
                }

                else -> {}
            }

            if (data.member_level == 0 && !UserMgr.getInstance().isLogin) {
                root.onDebounceClickListener { LoginCenter.getInstance().loginControl(context) }
            }
        }
    }

    class BannerAdHolder(view: View) : RecyclerView.ViewHolder(view) {

        val mBind = DataBindingUtil.bind<LayoutItemBannerSubBinding>(view)

    }

}
