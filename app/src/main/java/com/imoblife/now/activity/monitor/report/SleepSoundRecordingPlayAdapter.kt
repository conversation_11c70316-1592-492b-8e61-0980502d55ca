package com.imoblife.now.activity.monitor.report

import android.annotation.SuppressLint
import android.widget.SeekBar
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.bean.MonitoringReportSoundItemEntity
import com.imoblife.now.databinding.LayoutViewSleepMonitoringReportSoundRecordingPlayBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.player.BGPlayerUtils
import com.imoblife.now.player.PlayCenter
import com.imoblife.now.util.XLog

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/26
 * 描   述：睡眠监测报告 - 睡眠声音记录 - play - Adapter
 */
class SleepSoundRecordingPlayAdapter(private val lifecycleOwner: LifecycleOwner) :
    BaseQuickAdapter<MonitoringReportSoundItemEntity, BaseViewHolder>(R.layout.layout_view_sleep_monitoring_report_sound_recording_play) {

    private var mBlockAction: ((url: String, bool: Boolean) -> Unit)? = null

    fun setBlockAction(block: ((url: String, bool: Boolean) -> Unit)) {
        mBlockAction = block
    }

    @SuppressLint("NotifyDataSetChanged", "SetTextI18n")
    override fun convert(holder: BaseViewHolder, item: MonitoringReportSoundItemEntity) {
        holder.getBinding(LayoutViewSleepMonitoringReportSoundRecordingPlayBinding::bind).apply {
            if (item.isPlayState) {
                setEnableDragSeekBar(true)
                voicePlayView.setImageDrawable(
                    ContextCompat.getDrawable(
                        mContext,
                        R.mipmap.icon_sleep_voice_pause
                    )
                )
                voiceBgView.solid = ContextCompat.getColor(mContext, R.color.main_color)
                lifecycleOwner.lifecycle.addObserver(BGPlayerUtils.instance)
                BGPlayerUtils.instance.getProgressLiveData().observeForever {
                    if (item.isPlayState) {
                        voicePlayProgress.max = it.second.toInt()
                        voicePlayProgress.progress = it.third.toInt()
                        XLog.e("tag", "======LiveData=max==" + it.second.toInt())
                        XLog.e("tag", "======LiveData=progress==" + it.third.toInt())
//                        BGPlayerUtils.instance.getDuration().let { voicePlayProgress.max = it.toInt() }
//                        BGPlayerUtils.instance.getCurrent().let { voicePlayProgress.progress = it.toInt() }
                    } else {
                        voicePlayProgress.progress = 0
                    }
                }
                voicePlayProgress.setOnSeekBarChangeListener(object :
                    SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(
                        seekBar: SeekBar,
                        progress: Int,
                        fromUser: Boolean
                    ) {
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {}
                    override fun onStopTrackingTouch(seekBar: SeekBar) {
                        XLog.e("tag", "======seekBar=max==" + seekBar.max)
                        XLog.e("tag", "======seekBar=progress==" + seekBar.progress)
                        BGPlayerUtils.instance.seekTo(seekBar.progress.toLong())
                    }
                })
            } else {
                setEnableDragSeekBar(false)
                voicePlayView.setImageDrawable(
                    ContextCompat.getDrawable(
                        mContext,
                        R.mipmap.icon_sleep_voice_play
                    )
                )
                voiceBgView.solid = ContextCompat.getColor(mContext, R.color.color_2A2A31)
            }
            progressText.text = "${item.create_time} / ${item.duration} 秒"
            voicePlayView.setOnClickListener {
                PlayCenter.getInstance().playerControl.pauseMusic()
                mData.forEach { entity ->
                    if (item.url == entity.url) {
                        entity.isPlayState = !entity.isPlayState
                        if (entity.isPlayState) {
                            BGPlayerUtils.instance.startPlayBackground(entity.url, isLoop = false)
                        } else {
                            BGPlayerUtils.instance.pause()
                        }
                        mBlockAction?.invoke(item.url, entity.isPlayState)
                        return@forEach
                    }
                }
            }
            // 播放完成 - 回调
            BGPlayerUtils.instance.setPlayEndCallBack {
                mBlockAction?.invoke(item.url, false)
            }
        }
    }

    /**
     * 设置是否启动 SeekBar 拖拽
     *
     * @receiver LayoutViewSleepMonitoringReportSoundRecordingPlayBinding
     * @param bool 是否开启拖拽
     */
    private fun LayoutViewSleepMonitoringReportSoundRecordingPlayBinding.setEnableDragSeekBar(bool: Boolean) {
        voicePlayProgress.isEnabled = bool
        voicePlayProgress.isClickable = bool
        voicePlayProgress.isSelected = bool
        voicePlayProgress.isFocusable = bool
    }

}