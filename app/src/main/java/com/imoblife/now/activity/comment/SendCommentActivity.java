package com.imoblife.now.activity.comment;

import android.app.Activity;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;

import com.gyf.immersionbar.ImmersionBar;
import com.imoblife.commlibrary.mvp.CreatePresenter;
import com.imoblife.now.MyApplication;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.activity.user.LoginCenter;
import com.imoblife.now.bean.CommentCourse;
import com.imoblife.now.bean.Track;
import com.imoblife.now.constant.ConsCommon;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.constant.ConsRequestCode;
import com.imoblife.now.databinding.ActivitySendCommentBinding;
import com.imoblife.now.model.UserMgr;
import com.imoblife.now.mvp_contract.CommentContract;
import com.imoblife.now.mvp_presenter.CommentPresenter;
import com.imoblife.now.util.ToastUtils;
import com.jakewharton.rxbinding2.view.RxView;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

@CreatePresenter(presenter = CommentPresenter.class)
public class SendCommentActivity extends MvpBaseActivity<CommentPresenter> implements CommentContract.ICommentView {

    private ActivitySendCommentBinding mBind;

    private int type;
    private int courseId;
    private int sectionId;
    private int commentId;
    private int rId;
    private int fId;
    private String replyNickName;
    private String comment;
    private boolean isFullScreen = false;
    private CommentCourse.Comment.CommentBean commentBean;

    /**
     * 发表对课程评论的评论
     *
     * @param comment
     */
    public static void sendCommentComment(Activity mContext, CommentCourse.Comment comment) {
        if (comment == null) {
            return;
        }
        Intent intent = new Intent(mContext, SendCommentActivity.class);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_TYPE, ConsCommon.COMMENT_TYPE_COMMENT);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID, comment.getCourse_id());
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_ID, comment.getId());
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_FID, comment.getUser_id());
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_RID, 0);
        intent.putExtra(ConsIntent.BUNDLE_KEY_REPLY_NICKNAME, comment.getNickname());
        mContext.startActivityForResult(intent, ConsRequestCode.SEND_REQUEST_COMMENT_COMMENT_CODE);
    }

    /**
     * 发表对小课程的评论
     */
    public static void sendTrackComment(Activity activity, Track track) {
        if (track == null) {
            return;
        }
        Intent intent = new Intent(activity, SendCommentActivity.class);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_TYPE, ConsCommon.COMMENT_TYPE_COURSE);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID, track.getCourse_id());
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_SECTION_ID, track.getId());
        activity.startActivityForResult(intent, ConsRequestCode.SEND_REQUEST_COURSE_COMMENT_CODE);
    }

    /**
     * 发表对大课程的评论
     */
    public static void sendCourseComment(Activity activity, int courseId) {
        if (activity == null) {
            return;
        }
        Intent intent = new Intent(activity, SendCommentActivity.class);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_TYPE, ConsCommon.COMMENT_TYPE_COURSE);
        intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID, courseId);
        activity.startActivityForResult(intent, ConsRequestCode.SEND_REQUEST_COURSE_COMMENT_CODE);
    }

    @Override
    protected int setContentViewId() {
        return 0;
    }

    @Override
    protected void initImmersionBar() {
        ImmersionBar.with(this)
                .statusBarColor(R.color.transparent)
                .statusBarDarkFont(false)
                .fitsSystemWindows(true)
                .init();
    }

    @Override
    protected void initDataBinding() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_send_comment);
        mBind.setLifecycleOwner(this);
    }

    @Override
    protected void initView() {
        mBind.title.titleBackImg.setVisibility(View.GONE);
        mBind.title.titleBackTxt.setText(getString(R.string.string_cancel_text));
        mBind.title.titleMoreTxt.setText(R.string.send_comment_txt);
        if (ConsCommon.COMMENT_TYPE_COMMENT == type) {
            mBind.title.titleContentText.setText(R.string.replay_send_comment_txt);
        } else {
            mBind.title.titleContentText.setText(R.string.replay_send_comment_course_txt);
        }

        mBind.title.titleLly.setVisibility(View.GONE);
        mBind.commentEdit.setFocusable(true);
        mBind.commentEdit.setFocusableInTouchMode(true);
        mBind.commentEdit.requestFocus();
        mBind.commentEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String comment = "";
                if (s.length() > 0) {
                    comment = s.toString().trim();
                }
                if (comment.length() > 0) {
                    lengthComment(comment);
                } else {
                    mBind.lengthFullTxt.setVisibility(View.GONE);
                    mBind.lengthTxt.setVisibility(View.GONE);
                    mBind.sendCommentTxt.setTextColor(getResources().getColor(R.color.black));
                    mBind.title.titleMoreTxt.setTextColor(getResources().getColor(R.color.black));
                    mBind.lengthTxt.setVisibility(View.GONE);
                    mBind.lengthFullTxt.setVisibility(View.GONE);
                }
                MyApplication.getInstance().defaultEditStr = comment;
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });
        if (!TextUtils.isEmpty(replyNickName)) {
            mBind.commentEdit.setHint(String.format(getString(R.string.replay_txt), replyNickName));
        }
        String defaultEditStr = MyApplication.getInstance().defaultEditStr;
        if (!TextUtils.isEmpty(defaultEditStr)) {
            mBind.commentEdit.setText(defaultEditStr);
            mBind.commentEdit.setSelection(defaultEditStr.length());
        }

        RxView.clicks(mBind.title.titleMoreTxt)
                .throttleFirst(1, TimeUnit.SECONDS)
                .subscribe(o -> doSendComment());
        RxView.clicks(mBind.sendCommentTxt)
                .throttleFirst(1, TimeUnit.SECONDS)
                .subscribe(o -> doSendComment());

        mBind.title.titleBackTxt.setOnClickListener(v -> finish());
        mBind.editFullImg.setOnClickListener(v -> {
            mBind.title.titleLly.setVisibility(View.VISIBLE);
            mBind.editFullImg.setVisibility(View.GONE);
            mBind.sendCommentTxt.setVisibility(View.GONE);
            ViewGroup.LayoutParams lp = mBind.commentEdit.getLayoutParams();
            lp.height = ViewGroup.LayoutParams.MATCH_PARENT;
            mBind.commentEdit.setLayoutParams(lp);
            mBind.sendCommentTxt.setBackgroundResource(R.color.white);
            isFullScreen = true;
            comment = mBind.commentEdit.getText().toString().trim();
            lengthComment(comment);
        });
    }

    @Override
    protected void initData() {
    }

    @Override
    protected void superInit(Intent intent) {

        if (hasExtra(ConsIntent.BUNDLE_KEY_COMMENT_TYPE)) {
            type = getIntent().getIntExtra(ConsIntent.BUNDLE_KEY_COMMENT_TYPE, 1);
        }
        if (hasExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID)) {
            courseId = getIntent().getIntExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_ID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_KEY_COMMENT_SECTION_ID)) {
            sectionId = getIntent().getIntExtra(ConsIntent.BUNDLE_KEY_COMMENT_SECTION_ID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_KEY_COMMENT_ID)) {
            commentId = getIntent().getIntExtra(ConsIntent.BUNDLE_KEY_COMMENT_ID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_KEY_COMMENT_RID)) {
            rId = getIntent().getIntExtra(ConsIntent.BUNDLE_KEY_COMMENT_RID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_KEY_COMMENT_FID)) {
            fId = getIntent().getIntExtra(ConsIntent.BUNDLE_KEY_COMMENT_FID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_KEY_REPLY_NICKNAME)) {
            replyNickName = getIntent().getStringExtra(ConsIntent.BUNDLE_KEY_REPLY_NICKNAME);
        }
    }


    private void lengthComment(CharSequence s) {
        if (isFullScreen) {
            mBind.lengthFullTxt.setVisibility(View.VISIBLE);
            mBind.lengthTxt.setVisibility(View.GONE);
        } else {
            mBind.lengthTxt.setVisibility(View.VISIBLE);
            mBind.lengthFullTxt.setVisibility(View.GONE);
        }
        int MAX_COMMENT_LENGTH = 1000;
        if (s.length() > MAX_COMMENT_LENGTH) {
            mBind.lengthTxt.setText(MAX_COMMENT_LENGTH - s.length() + "");
            mBind.lengthFullTxt.setText(MAX_COMMENT_LENGTH - s.length() + "");
            mBind.lengthTxt.setTextColor(getResources().getColor(R.color.main_color));
            mBind.lengthFullTxt.setTextColor(getResources().getColor(R.color.main_color));
            mBind.sendCommentTxt.setTextColor(getResources().getColor(R.color.black_6));
            mBind.title.titleMoreTxt.setTextColor(getResources().getColor(R.color.black_6));
        } else if (s.length() > 990 && s.length() < 1001) {
            mBind.lengthTxt.setText(MAX_COMMENT_LENGTH - s.length() + "");
            mBind.lengthFullTxt.setText(MAX_COMMENT_LENGTH - s.length() + "");
            mBind.lengthTxt.setTextColor(getResources().getColor(R.color.black));
            mBind.lengthFullTxt.setTextColor(getResources().getColor(R.color.black));
            mBind.sendCommentTxt.setTextColor(getResources().getColor(R.color.main_color));
            mBind.title.titleMoreTxt.setTextColor(getResources().getColor(R.color.main_color));
        } else {
            if (s.length() > 0) {
                mBind.sendCommentTxt.setTextColor(getResources().getColor(R.color.main_color));
                mBind.title.titleMoreTxt.setTextColor(getResources().getColor(R.color.main_color));
            } else {
                mBind.sendCommentTxt.setTextColor(getResources().getColor(R.color.black_6));
                mBind.title.titleMoreTxt.setTextColor(getResources().getColor(R.color.black_6));
            }
            mBind.lengthFullTxt.setVisibility(View.GONE);
            mBind.lengthTxt.setVisibility(View.GONE);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        finish();
        return true;
    }

    private void doSendComment() {
        comment = mBind.commentEdit.getText().toString().trim();
        if (UserMgr.getInstance().isLogin()) {
            if (!TextUtils.isEmpty(comment)) {
                if (comment.length() > 1000) {
                    mBind.title.titleContentText.setClickable(true);
                    return;
                }
                int status = 0;
                sendComment(type, status, courseId, sectionId, comment, commentId, rId, fId);
            } else {
                ToastUtils.showShortToastCenter(getString(R.string.comment_short_txt));
                mBind.title.titleContentText.setClickable(true);
            }
        } else {
            mBind.title.titleContentText.setClickable(true);
            LoginCenter.getInstance().loginControl(this);
        }
    }

    /**
     * @param type      1 -- 对课程评论  2 -- 对评论评论
     * @param status    0 -- 评论列表可以见 3-- 评论列表不可见
     * @param courseId  课程Id
     * @param sectionId 小课Id
     * @param comment   评论内容
     * @param commentId 评论Id
     * @param rId       评论的评论的Id
     * @param fId       评论的用户Id
     * @return
     */
    private void sendComment(int type, int status, int courseId, final int sectionId, final String comment, int commentId, int rId, int fId) {
        mBind.title.titleContentText.setClickable(false);
        showWaitLoading();
        createComment(comment);
        getPresenter().sendComment(type, status, courseId, sectionId, comment, commentId, rId, fId);
        if (type == 1) {
            // 学习计划 - Task
            UserMgr.getInstance().queryStudyPlanTime();
        }
    }

    /**
     * 创建一个课程评论的评论
     *
     * @param comment
     */
    private void createComment(String comment) {
        commentBean = new CommentCourse.Comment.CommentBean();
        commentBean.setIs_manage(UserMgr.getInstance().getUserIsManger());
        commentBean.setContent(comment);
        commentBean.setNickname(UserMgr.getInstance().getUserNickname());
    }

    @Override
    public void sendCommentView(int status, String msg, int commentId) {
        mBind.sendCommentTxt.setClickable(true);
        hideWaitLoading();
        if (status == 101 || status == 103) {
            hideInput(mBind.commentEdit);
            ToastUtils.showToastShort(getString(R.string.comment_success_txt));
            MyApplication.getInstance().defaultEditStr = "";
            mBind.commentEdit.getText().clear();
            if (status == 101) {
                Intent intent = new Intent();
                intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_CONTENT, comment);
                intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_ID, commentId);
                setResult(RESULT_OK, intent);
            } else {
                Intent intent = new Intent();
                intent.putExtra(ConsIntent.BUNDLE_KEY_COMMENT_COURSE_COMMENT, (Serializable) commentBean);
                setResult(RESULT_OK, intent);
            }
        } else {
            ToastUtils.showToastShort(msg);
        }
        finish();
    }
}
