package com.imoblife.now.activity.member.signing

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutAcVipEquityVideoBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import com.shuyu.gsyvideoplayer.GSYVideoManager

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/7/7
 * 描   述：支付 - 会员权益 - Video
 */
class VipEquityVideoActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(context: Context) {
            val intent = Intent(context, VipEquityVideoActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcVipEquityVideoBinding

    override fun getLayoutResId() = R.layout.layout_ac_vip_equity_video

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(NoViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcVipEquityVideoBinding
        mBind.apply {
            emptyControlVideo.setUp(
                "android.resource://${packageName}/${R.raw.video_vip_equity}",
                true,
                ""
            )
            emptyControlVideo.startPlayLogic()
            emptyControlVideo.setOnPlayAutoCompletion {
                img.visibility = View.VISIBLE
                img.onDebounceClickListener { finish() }
            }
            imgClose.onDebounceClickListener { finish() }
        }
    }

    override fun onResume() {
        super.onResume()
        mBind.emptyControlVideo.onVideoResume()
    }

    override fun onPause() {
        super.onPause()
        mBind.emptyControlVideo.onVideoPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        mBind.emptyControlVideo.setVideoAllCallBack(null)
        mBind.emptyControlVideo.release()
        GSYVideoManager.releaseAllVideos()
    }

    override fun initData() {}

    override fun startObserve() {}

    override fun onBackPressed() {}

}