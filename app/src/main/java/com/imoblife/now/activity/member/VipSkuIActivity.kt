package com.imoblife.now.activity.member

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Looper
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.MyApplication.Companion.mCancelCheckedSkuId
import com.imoblife.now.R
import com.imoblife.now.activity.WebViewActivity
import com.imoblife.now.activity.main.MainActivity
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.BannerPurchasingPersonnelAdapter
import com.imoblife.now.adapter.ImageAdapter
import com.imoblife.now.adapter.SkuStyleVipImgAdapter
import com.imoblife.now.bean.SubSkuInfo
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.ActivityVipSkuIBinding
import com.imoblife.now.databinding.LayoutSkuVipImgItemBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.ext.preLoadImg
import com.imoblife.now.ext.removeAnim
import com.imoblife.now.ext.setImgSize
import com.imoblife.now.ext.slideInFromLeft
import com.imoblife.now.ext.slideOutToRight
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.NetworkUtils
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.dialog.PopupHalfAlterImgDialog
import com.imoblife.now.viewmodel.PaymentViewModel
import com.youth.banner.Banner
import com.youth.banner.indicator.RectangleIndicator
import io.paperdb.Paper
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/7/3
 * 描   述：ob - sku - 已为你开启最大优惠
 *
 * 界面UI结构 =>
 * 头部 - 静态视频
 * 中部 - 静图
 * 标签 - 已为你开启最大优惠
 * sku - RecyclerView
 * banner - 购买人数
 * banner - 用户评论
 * 自定义View - 悬浮按钮｜倒计时
 */
class VipSkuIActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        fun startActivity(context: Activity, templateId: Int?, is_second_ob: Int) {
            Intent(context, VipSkuIActivity::class.java).apply {
                putExtra(ConsCommon.SKU_TEMPLATE_ID, templateId)
                putExtra(ConsCommon.SKU_IS_OB_SUB, is_second_ob)
                context.startActivity(this)
                context.slideInFromLeft()
            }
        }

    }

    private lateinit var mBind: ActivityVipSkuIBinding
    private var skuStyleHorizontalAdapter: SkuStyleVipImgAdapter? = null
    private val popupHalfAlterImageDialog by lazy(LazyThreadSafetyMode.NONE) {
        PopupHalfAlterImgDialog(
            this
        )
    }

    private var isClickCloseVipPage = false
    private var isShowCancelDialogPage = false
    private var mTemplateId: Int = 0
    private var mSkuInfo: SubSkuInfo? = null

    // 是否购买成功
    private var isPurchase = false

    // banner - 购买人员
    private var mBannerPurchasingPersonnel: Banner<String, BannerPurchasingPersonnelAdapter>? = null

    // 是否第二次ob订阅
    private var mIsSecondOb = 0

    // 是否高亮标签 - 已为你开启最大优惠
    private var mIsHighlightTagsFromPosition = 0

    // 是否需要刷新当前页面 - update Sku
    private var mNeedRefreshOb = false

    // 神策数据 - 商业线订阅页面挽留弹框曝光 - 触发原因
    private var mTriggerReason = ""

    // 神策数据 - 商业线订阅页面挽留弹框曝光 - 是否当次ob的首次曝光
    private var mIsFirstShow = 0

    // 缓存的entity
    private val mEntity: SubSkuInfo? by lazy(LazyThreadSafetyMode.NONE) {
        Paper.book().read(ConsCommon.OB_SKU_CACHE_ENTITY)
    }

    // 是否展示 - 停留挽留弹窗
    private var mIsShowStayAndRetainDialog = false
    private var mIsShowStayAndRetainDialogJob: Job? = null
    private var mIsShowStayAndRetainDialogState = true

    // 点击支付触发场景逻辑
    private var mPaySceneType = ""

    // banner - top
    private var mBannerTop: Banner<String, BannerVipSkuIAdapter>? = null

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsCommon.SKU_TEMPLATE_ID)) {
                mTemplateId = it.getIntExtra(ConsCommon.SKU_TEMPLATE_ID, 0)
            }
            if (hasExtra(ConsCommon.SKU_IS_OB_SUB)) {
                mIsSecondOb = it.getIntExtra(ConsCommon.SKU_IS_OB_SUB, 0)
            }
        }
    }

    override fun getLayoutResId() = R.layout.activity_vip_sku_i

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .init()
    }

    override fun initVM() = ViewModelProvider(this).get(PaymentViewModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityVipSkuIBinding
        mBind.apply {
            mBannerTop = findViewById(R.id.bannerTop)
            mBannerPurchasingPersonnel = findViewById(R.id.bannerPurchasingPersonnel)
            lifecycle.addObserver(bottomVipTimerView)
//            subProtocolPrivacy.subProtocolPrivacy()
            if (mIsSecondOb == 1) {
                // OB订阅页面顶部视频测试 - 0为旧；1为新
                if (ConfigMgr.getInstance().config.ab_sub_page_video_ob2 == 0) {
                    topHeaderBg.setUp(
                        "android.resource://${packageName}/${R.raw.sku_top_video}",
                        true,
                        ""
                    )
                } else {
                    topHeaderBg.setUp(
                        "android.resource://${packageName}/${R.raw.sku_top_video_second}",
                        true,
                        ""
                    )
                }
            } else {
                // OB订阅页面顶部视频测试 - 0为旧；1为新
                if (ConfigMgr.getInstance().config.ab_sub_page_video == 0) {
                    topHeaderBg.setUp(
                        "android.resource://${packageName}/${R.raw.sku_top_video}",
                        true,
                        ""
                    )
                } else {
                    topHeaderBg.setUp(
                        "android.resource://${packageName}/${R.raw.sku_top_video_481}",
                        true,
                        ""
                    )
                }
            }

            topHeaderBg.isLooping = true
            recyclerSku.removeAnim()
            bottomVipTimerView.setSubmitPayOnClickListener {
                mPaySceneType = "ob"
                mIsShowStayAndRetainDialog = true
                // 埋点 - 商业线订阅页面点击事件 - 下一步
                SensorsDataEvent.businessLineVipPageClick(
                    mTemplateId,
                    getString(R.string.string_next_step)
                )
                skuStyleHorizontalAdapter?.getSubscribe()?.let {
                    mBind.subProtocolPrivacy.isAgreePrivacy(it, "ob订阅页") {
                        PayCenter.getInstance().doSubmitPay(this@VipSkuIActivity, it)
                    }
                } ?: let {
                    ToastUtils.showShortToastCenter(getString(R.string.string_please_select_subscribe))
                }
            }
            closeImg.setOnClickListener {
                mIsShowStayAndRetainDialog = true
                mTriggerReason = "点击订阅页关闭按钮"
                mIsFirstShow += 1
                // 埋点 - 商业线订阅页面点击事件 - 关闭
                SensorsDataEvent.businessLineVipPageClick(
                    mTemplateId,
                    getString(R.string.string_close_txt)
                )
                if (isPurchase) {
                    super.onBackPressed()
                    return@setOnClickListener
                }
                mSkuInfo?.let { skuInfo ->
                    isClickCloseVipPage = true
                    adSkuPageClose(
                        this@VipSkuIActivity,
                        mViewModel,
                        skuInfo,
                        isShowCancelDialogPage,
                        mIsSecondOb,
                        skuId = skuStyleHorizontalAdapter?.getSubscribe()?.id ?: -1
                    )
                } ?: super.onBackPressed()
            }
        }
    }

    override fun initData() {
        if (ConfigMgr.getInstance().config.isStart_login) {
            if (!UserMgr.getInstance().isLogin) {
                LoginCenter.getInstance().loginControl(this, LoginCenter.LoginStyleDialog)
            }
        }
        mEntity?.let { updateUI(it) } ?: run {
            mViewModel.getSubSkuTest(mTemplateId, mIsSecondOb)
        }
        Looper.myQueue().addIdleHandler {
            if (!NetworkUtils.isNetworkAvailable()) {
                ToastUtils.showShortToast(getString(R.string.string_ob_questionnaire_no_net_txt))
            }
            false
        }
    }

    override fun startObserve() {
        mViewModel.testSubSkuInfo.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    updateUI(entity)
                } ?: showLocalData(it.successData)
            } else {
                showLocalData(it.successData)
            }
        }
        mViewModel.adPageCancelSkuList.observe(this) {
            val flag = mIsFirstShow == 1
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    SensorsDataEvent.businessDialogPageShow(
                        entity.template_id_429,
                        flag,
                        mTriggerReason
                    )
                }
            }
            showHalfDialog(it, popupHalfAlterImageDialog, mTriggerReason, flag)
            popupHalfAlterImageDialog.setClickCloseEvent {
                if (mNeedRefreshOb && flag) {
                    mViewModel.getSubSkuTest(mTemplateId, mIsSecondOb, mNeedRefreshOb)
                }
            }
            popupHalfAlterImageDialog.setClickPayEvent { content ->
                mPaySceneType = content
            }
            if (isClickCloseVipPage) {
                isShowCancelDialogPage = true
            }
        }
    }

    /**
     * 更新UI
     *
     * @param entity SubSkuInfo
     */
    private fun updateUI(entity: SubSkuInfo) {
        mNeedRefreshOb = entity.isNeed_refresh_ob
        entity.preLoadImg(this)
        setSukInfo(entity)
        // banner - 购买人员
        if (!entity.scroll_info_list.isNullOrEmpty()) {
            mBannerPurchasingPersonnel?.visibility = View.VISIBLE
            initBannerPurchasingPersonnel(entity.scroll_info_list)
        } else {
            mBannerPurchasingPersonnel?.visibility = View.GONE
        }
    }

    /**
     * 展示本地数据
     *
     * @param entity SubSkuInfo?
     */
    private fun showLocalData(entity: SubSkuInfo?) {
        entity?.let {
            setSukInfo(entity)
            // banner - 购买人员
            if (!entity.scroll_info_list.isNullOrEmpty()) {
                mBannerPurchasingPersonnel?.visibility = View.VISIBLE
                initBannerPurchasingPersonnel(entity.scroll_info_list)
            } else {
                mBannerPurchasingPersonnel?.visibility = View.GONE
            }
        }
    }

    /**
     * 初始化banner - 购买人员
     *
     * @param nowOrderCommentList data
     */
    private fun initBannerPurchasingPersonnel(nowOrderCommentList: MutableList<String>) {
        mBannerPurchasingPersonnel
            ?.addBannerLifecycleObserver(this)
            ?.setAdapter(BannerPurchasingPersonnelAdapter(nowOrderCommentList))
            ?.setOrientation(Banner.VERTICAL)
            ?.setUserInputEnabled(false)
    }

    private fun setSukInfo(subSkuInfo: SubSkuInfo?) {
        subSkuInfo?.let {
            mSkuInfo = it
            if (!TextUtils.isEmpty(it.bg_img)) {
                Glide
                    .with(this)
                    .load(it.bg_img)
                    .set(
                        WebpFrameLoader.FRAME_CACHE_STRATEGY,
                        WebpFrameCacheStrategy.AUTO
                    )
                    .into(object : SimpleTarget<Drawable>() {
                        override fun onResourceReady(
                            resource: Drawable,
                            transition: Transition<in Drawable>?
                        ) {
                            mBind.container.background = resource
                        }
                    })
            }
            initStayAndRetain()
            run out@{
                subSkuInfo.sku_list.forEachIndexed { index, subscribe ->
                    if (subscribe.isChecked) {
                        mIsHighlightTagsFromPosition = index
                        return@out
                    }
                }
            }
            it.topCountdown?.let { top ->
                if (top.countdown > 0) {
                    mBind.topTimerView.startTimer(top.countdown)
                } else {
                    mBind.topTimerView.visibility = View.GONE
                }
            } ?: let {
                mBind.topTimerView.visibility = View.GONE
            }
            it.refundTip?.let { refundTip ->
                if (TextUtils.isEmpty(refundTip.show_img)) {
                    mBind.refundTipView.visibility = View.GONE
                } else {
                    mBind.refundTipView.visibility = View.VISIBLE
                    ImageLoader.loadImageUrl(
                        this@VipSkuIActivity,
                        refundTip.show_img,
                        mBind.refundTipImg
                    )
                    mBind.refundTipClick.setOnClickListener {
                        WebViewActivity.openWebViewActivity(
                            this@VipSkuIActivity,
                            refundTip.link_url,
                            ""
                        )
                    }
                }
            } ?: let {
                mBind.refundTipView.visibility = View.GONE
            }

            if (subSkuInfo.style == 1) {
//                skuStyleHorizontalAdapter = when (subSkuInfo.sku_list.size) {
//                    1 -> { //只展示一个SKU
//                        SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_style_c)
//                    }
//                    2 -> {
//                        SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_style_b)
//                    }
//                    else -> { //展示多个SKU
//                        SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_style_d)
//                    }
//                }
                // 动态适配 - skuSize
                skuStyleHorizontalAdapter =
                    SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_dynamic_img_size)
                mBind.recyclerSku.layoutManager =
                    LinearLayoutManager(
                        this@VipSkuIActivity,
                        LinearLayoutManager.VERTICAL,
                        false
                    )
            } else {
                skuStyleHorizontalAdapter =
                    SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_style_a, it.isLocalFlag)
                mBind.recyclerSku.layoutManager =
                    LinearLayoutManager(
                        this@VipSkuIActivity,
                        LinearLayoutManager.HORIZONTAL,
                        false
                    )
            }
            if (it.isLocalFlag) {
                ImageLoader.loadImageLocal(this, R.mipmap.img_64ad1155a8f6b, mBind.topHeaderImg)
                ImageLoader.loadImageLocal(
                    this,
                    R.mipmap.icon_vip_sku_close,
                    mBind.closeImg
                )
                // 选中 - 已为你开启最大优惠
                it.checked_tip?.let { _ ->
                    mBind.imgMaximumDiscount.visibility = View.VISIBLE
                    Glide.with(this)
                        .load(R.mipmap.img_maximum_discount_enable_place)
                        .set<WebpFrameCacheStrategy>(
                            WebpFrameLoader.FRAME_CACHE_STRATEGY,
                            WebpFrameCacheStrategy.AUTO
                        )
                        .placeholder(R.mipmap.img_maximum_discount_enable_place)
                        .into(mBind.imgMaximumDiscount)

                }
                skuStyleHorizontalAdapter?.setActionBlock { position ->
                    it.checked_tip?.let { _ ->
                        if (position == mIsHighlightTagsFromPosition) {
                            // 选中 - 已为你开启最大优惠
                            Glide.with(this)
                                .load(R.mipmap.img_maximum_discount_enable_place)
                                .set<WebpFrameCacheStrategy>(
                                    WebpFrameLoader.FRAME_CACHE_STRATEGY,
                                    WebpFrameCacheStrategy.AUTO
                                )
                                .placeholder(R.mipmap.img_maximum_discount_enable_place)
                                .into(mBind.imgMaximumDiscount)
                        } else {
                            // 未选中 - 已为你开启最大优惠
                            Glide.with(this)
                                .load(R.mipmap.img_maximum_discount_enable_place_no_select)
                                .set<WebpFrameCacheStrategy>(
                                    WebpFrameLoader.FRAME_CACHE_STRATEGY,
                                    WebpFrameCacheStrategy.AUTO
                                )
                                .placeholder(R.mipmap.img_maximum_discount_enable_place_no_select)
                                .into(mBind.imgMaximumDiscount)
                        }
                    }
                    setSubPrivacy()
                }
            } else {
                // 0视频  1 多图轮播
                if (it.ob_style_type == 0) {
                    mBannerTop?.visibility = View.GONE
                    mBind.topHeaderBg.startPlayLogic()
                } else {
                    val layoutParams = mBind.clContainer.layoutParams
                    layoutParams.height = resources.getDimensionPixelSize(R.dimen.qb_px_349)
                    mBind.clContainer.layoutParams = layoutParams
                    if (!it.banner_list.isNullOrEmpty()) {
                        mBannerTop?.visibility = View.VISIBLE
                        mBannerTop
                            ?.addBannerLifecycleObserver(this)
                            ?.setIndicator(RectangleIndicator(this))
                            ?.setAdapter(BannerVipSkuIAdapter(it.banner_list))
                    }
                }
                ImageLoader.loadImageUrl(this, it.banner, mBind.topHeaderImg)
                ImageLoader.loadImageUrl(
                    this,
                    it.close_img,
                    mBind.closeImg,
                    R.mipmap.icon_vip_sku_close
                )
                // 选中 - 已为你开启最大优惠
                it.checked_tip?.let { entity ->
                    mBind.imgMaximumDiscount.visibility = View.VISIBLE
                    Glide
                        .with(this)
                        .asBitmap()
                        .load(entity.checked_img)
                        .dontAnimate()
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .placeholder(R.mipmap.img_maximum_discount_enable_place)
                        .into(object : SimpleTarget<Bitmap?>() {
                            override fun onResourceReady(
                                resource: Bitmap, transition: Transition<in Bitmap?>?
                            ) {
                                mBind.imgMaximumDiscount.setImgSize(this@VipSkuIActivity, resource)
                            }
                        })
                }
                skuStyleHorizontalAdapter?.setClickActionBlock {
                    mSkuInfo?.let {
                        if (it.sku_pull_payment == 1) {
                            skuStyleHorizontalAdapter?.getSubscribe()?.let { sub ->
                                mBind.subProtocolPrivacy.isAgreePrivacy(sub, "ob订阅页") {
                                    PayCenter.getInstance().doSubmitPay(this@VipSkuIActivity, sub)
                                }
                            }
                        }
                    }
                }
                skuStyleHorizontalAdapter?.setActionBlock { position ->
                    it.checked_tip?.let { entity ->
                        if (position == mIsHighlightTagsFromPosition) {
                            // 选中 - 已为你开启最大优惠
                            Glide
                                .with(this)
                                .asBitmap()
                                .load(entity.checked_img)
                                .dontAnimate()
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .placeholder(R.mipmap.img_maximum_discount_enable_place)
                                .into(object : SimpleTarget<Bitmap?>() {
                                    override fun onResourceReady(
                                        resource: Bitmap, transition: Transition<in Bitmap?>?
                                    ) {
                                        mBind.imgMaximumDiscount.setImgSize(this@VipSkuIActivity, resource)
                                    }
                                })
                        } else {
                            // 未选中 - 已为你开启最大优惠
                            Glide
                                .with(this)
                                .asBitmap()
                                .load(entity.default_img)
                                .dontAnimate()
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .placeholder(R.mipmap.img_maximum_discount_enable_place_no_select)
                                .into(object : SimpleTarget<Bitmap?>() {
                                    override fun onResourceReady(
                                        resource: Bitmap, transition: Transition<in Bitmap?>?
                                    ) {
                                        mBind.imgMaximumDiscount.setImgSize(this@VipSkuIActivity, resource)
                                    }
                                })
                        }
                    }
                    setSubPrivacy()
                }
            }
            mBind.recyclerSku.adapter = skuStyleHorizontalAdapter
            skuStyleHorizontalAdapter?.setNewData(subSkuInfo.sku_list)
            mBind.bottomVipTimerView.setPayButtonData(subSkuInfo.pay_button)
            if (EmptyUtils.isNotEmpty(it.comment_card)) {
                mBind.commentBanner.visibility = View.VISIBLE
                mBind.commentBanner.apply {
                    addBannerLifecycleObserver(this@VipSkuIActivity)
                    setAdapter(ImageAdapter(it.comment_card, it.isLocalFlag))
                    setBannerGalleryEffect(20, 10)
                }
            } else {
                mBind.commentBanner.visibility = View.GONE
            }
        }
    }

    /**
     * 停留挽留弹窗
     */
    private fun initStayAndRetain() {
        if (mIsShowStayAndRetainDialogState) {
            mIsShowStayAndRetainDialogState = false
            mSkuInfo?.let { entity ->
                if (entity.is_residence_time == 1) {
                    mIsShowStayAndRetainDialogJob = lifecycleScope.launch {
                        delay(entity.residence_seconds * 1000L)
                        if (mIsShowStayAndRetainDialog) {
                            mIsShowStayAndRetainDialogJob?.cancel()
                        } else {
                            mSkuInfo?.let { entity ->
                                showStayAndRetainDialog(entity)
                            }
                        }
                    }
                }
            }
        }
    }
    private fun setSubPrivacy() {
        mBind.subProtocolPrivacy.setAgreementCheckboxFalse()
        skuStyleHorizontalAdapter?.getSubscribe()?.let { sub ->
            mBind.subProtocolPrivacy.setData(
                sub,
                ConfigMgr.getInstance().config.isAuto_vip_privacy_ob
            )
            mBind.bottomVipTimerView.setSubscribeFlagImg(sub.sku_offer_tag_url)
        }
    }
    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        event?.let {
            when (it.eventCode) {
                ConsEventCode.PAY_CANCEL_EVENT, ConsEventCode.PAY_FAIL_EVENT -> {
                    mSkuInfo?.let { entity ->
                        when (mPaySceneType) {
                            "点击订阅页关闭按钮" -> {
                                adSkuPageClose(
                                    this@VipSkuIActivity,
                                    mViewModel,
                                    entity,
                                    false,
                                    mIsSecondOb,
                                    skuId = skuStyleHorizontalAdapter?.getSubscribe()?.id ?: -1
                                )
                            }

                            "停留挽留" -> showStayAndRetainDialog(entity)

                            else -> {
                                mTriggerReason = "取消支付"
                                mIsFirstShow += 1
                                adSkuPageCancelPay(
                                    mViewModel,
                                    entity,
                                    skuId = skuStyleHorizontalAdapter?.getSubscribe()?.id ?: -1
                                )
                            }
                        }
                    }
                    isPurchase = false
                }

                ConsEventCode.PAY_SUCCESS_EVENT -> {
                    isPurchase = true
                    MainActivity.openMainActivity(
                        this,
                        ConfigMgr.getInstance().config.app_default_tab,
                        isForceLogin = "Login"
                    )
                }

                ConsEventCode.PAY_IN_PROGRESS_EVENT -> DialogUtil.showWaitLoading(
                    this,
                    getString(R.string.string_order_validation_txt),
                    false
                )

                else -> {}
            }
        }
    }

    /**
     * 展示停留挽留弹窗
     *
     * @param entity SubSkuInfo
     */
    private fun showStayAndRetainDialog(entity: SubSkuInfo) {
        mTriggerReason = "停留挽留"
        mIsFirstShow += 1
        adSkuPageStayAndRetain(
            mViewModel,
            entity,
            skuId = skuStyleHorizontalAdapter?.getSubscribe()?.id ?: -1
        )
    }

    inner class SkuCardImageAdapter :
        BaseQuickAdapter<String, BaseViewHolder>(R.layout.layout_sku_vip_img_item) {
        override fun convert(holder: BaseViewHolder, item: String) {
            holder.getBinding(LayoutSkuVipImgItemBinding::bind).skuImg.let {
                ImageLoader.loadImageUrl(mContext, item, it)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBind.topHeaderBg.onVideoResume()
    }

    override fun onDestroy() {
        super.onDestroy()
        mBind.topHeaderBg.setVideoAllCallBack(null)
        mBind.topHeaderBg.release()
        mCancelCheckedSkuId = 0
        Paper.book().delete(ConsCommon.OB_SKU_CACHE_ENTITY)
    }

    override fun onBackPressed() {}

    override fun finish() {
        if (!isPurchase && mSkuInfo?.need_second_ob == 2) {
            VipSkuKActivity.startActivity(this)
        }
        super.finish()
        slideOutToRight()
    }

}