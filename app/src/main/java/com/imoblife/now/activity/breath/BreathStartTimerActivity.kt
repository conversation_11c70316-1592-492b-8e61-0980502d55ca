package com.imoblife.now.activity.breath

import android.content.Intent
import android.view.View
import androidx.databinding.DataBindingUtil
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.mvp.CreatePresenter
import com.imoblife.now.R
import com.imoblife.now.activity.base.MvpBaseActivity
import com.imoblife.now.bean.BreathBgEntity
import com.imoblife.now.bean.BreathModeEntity
import com.imoblife.now.bean.BreathUploadBean
import com.imoblife.now.databinding.ActivityBreathStartTimerBinding
import com.imoblife.now.mvp_contract.BreathUploadContract
import com.imoblife.now.mvp_presenter.BreathUploadPresenter
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.RxTimer
import com.imoblife.now.util.SoundPoolUtil
import com.imoblife.now.util.TimeUtils
import com.imoblife.now.util.breath.BREATH_TIMER
import com.imoblife.now.viewmodel.getBreathViewModel
import java.util.concurrent.TimeUnit

@CreatePresenter(presenter = [BreathUploadPresenter::class])
class BreathStartTimerActivity : MvpBaseActivity<BreathUploadPresenter>(),
    BreathUploadContract.IBreathUploadView, View.OnClickListener {

    private var countDownTime = 0

    private var startTime = 0L
    private var endTime = 0L

    private val rxTimer by lazy {
        RxTimer()
    }

    private lateinit var mBind: ActivityBreathStartTimerBinding

    override fun initDataBinding() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_breath_start_timer)
        mBind.lifecycleOwner = this
    }

    override fun initView() {
        mBind.apply {
            toolbar.title = ""
            toolbarCenterTitleTv.text = getString(R.string.breath_timer)
            ivClose.setOnClickListener(this@BreathStartTimerActivity)
            tvStart.setOnClickListener(this@BreathStartTimerActivity)
            ImageLoader.loadImageLocal(
                this@BreathStartTimerActivity,
                R.mipmap.breath_count_down_bg,
                R.mipmap.breath_count_down_bg,
                ivBg
            )
        }
    }

    override fun initData() {
        val intent = intent
        val bundle = intent.extras
        countDownTime = bundle?.getInt("COUNT_DOWN_TIME") ?: 0
        countDownTime *= 1000
        mBind.apply {
            seekBar.progress = 1
            seekBar.max = countDownTime
            tvSurplusTime.text = TimeUtils.formatTime(countDownTime.toLong())
        }
        startTime = System.currentTimeMillis()
        startTiming(countDownTime.toLong())
    }

    //开始定时
    private fun startTiming(millisInFuture: Long) {
        SoundPoolUtil.getInstance(this).playSoundWithRedId(R.raw.breath_start_end_hint)
        rxTimer.timerDown(millisInFuture, TimeUnit.MILLISECONDS, object : RxTimer.RxAction {
            override fun onComplete() {
                timingEnd()
            }
            override fun action(number: Long) {
                mBind.apply {
                    seekBar.progress = countDownTime - number.toInt()
                    tvSurplusTime.text = TimeUtils.formatTime(number)
                }
            }
        })
    }

    //定时结束
    private fun timingEnd() {
        SoundPoolUtil.getInstance(this).playSoundWithRedId(R.raw.breath_start_end_hint)
        cancelTiming()
        uploadBreath("yes")
    }

    private fun uploadBreath(isCompleted: String) {
        endTime = System.currentTimeMillis()
        val duration = (endTime - startTime) / 1000
        val ratio =
            if (isCompleted == "yes") 100 else ((duration.toFloat() / countDownTime.toFloat()) * 100).toInt()
        presenter.uploadBreathPractice(
            tagType = BREATH_TIMER,
            duration = duration / 1000, completed = isCompleted, ratio = ratio
        )
    }

    //取消定时
    private fun cancelTiming() {
        rxTimer.cancel()
    }

    override fun finish() {
        getBreathViewModel(application, this).breathViewModel.postValue(true)
        super.finish()
    }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .titleBar(R.id.toolbar)
            .init()
    }

    override fun onDestroy() {
        super.onDestroy()
        cancelTiming()
    }

    override fun setContentViewId(): Int = 0

    override fun onBackPressed() {
        finishAfterTransition()
        uploadBreath("no")
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_close,
            R.id.tv_start -> {
                finishAfterTransition()
                uploadBreath("no")

            }
        }
    }

    override fun uploadSuccess(data: BreathUploadBean?) {
        if (data != null && data.all_number > 0) {
            Intent(this, BreathTimingShareActivity::class.java).apply {
                this.putExtra("all_number", data.all_number)
                startActivity(this)
            }
            finish()
            overridePendingTransition(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            )
        }
    }

    override fun getBreathBgSuccess(list: List<BreathBgEntity>?) {
    }

    override fun getBreathModeSuccess(list: List<BreathModeEntity>?) {
    }

}