package com.imoblife.now.activity.category

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.util.ImageLoader
import com.makeramen.roundedimageview.RoundedImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - 拓展进阶
 */
class CategoryExcellentAdapter :
    BaseQuickAdapter<CategoryNavigationItemEntity, BaseViewHolder>(R.layout.layout_course_jp_item_view) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationItemEntity?) {
        item?.apply {
            val courseJpBgImg = holder.getView<RoundedImageView>(R.id.course_jp_bg_img)
            val courseJpJoinNum = holder.getView<TextView>(R.id.course_jp_join_num)

            ImageLoader.loadImageUrl(mContext, banner, courseJpBgImg)
            courseJpJoinNum.text = label
            holder.itemView.setOnClickListener { pageRoute(mContext, holder.layoutPosition) }
        }
    }

}