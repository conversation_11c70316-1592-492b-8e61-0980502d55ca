package com.imoblife.now.activity.facedetection

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutAcFaceDetectionStartBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.ToastUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-22
 * 描   述：人脸检测 - 开始 - activity
 */
class FaceDetectionStartActivity : BaseVMActivity<FaceDetectionViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(context: Context) {
            Intent(context, FaceDetectionStartActivity::class.java).let {
                context.startActivity(it)
            }
        }

    }

    private lateinit var mBind: LayoutAcFaceDetectionStartBinding

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentBar()
            .statusBarDarkFont(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_face_detection_start

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[FaceDetectionViewModel::class.java]

    override fun initView() {
        mBind = mBinding as LayoutAcFaceDetectionStartBinding
        mBind.apply {
            imgBtn.onDebounceClickListener {
                XXPermissions
                    .with(this@FaceDetectionStartActivity)
                    .permission(Permission.CAMERA)
                    .request(object : OnPermissionCallback {
                        override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                            if (!allGranted) {
                                ToastUtils.showShortToast("获取相机权限失败")
                                return
                            }
                            // 限制10分钟使用该功能 - 「注释」
//                            mViewModel.checkDFRight()

                            FaceDetectionActivity.startActivity(this@FaceDetectionStartActivity)
                            finish()
                        }

                        override fun onDenied(permissions: MutableList<String>, doNotAskAgain: Boolean) {
                            if (doNotAskAgain) {
                                ToastUtils.showShortToast("被永久拒绝授权，请手动授予相机权限")
                                XXPermissions.startPermissionActivity(this@FaceDetectionStartActivity, permissions)
                            } else {
                                ToastUtils.showShortToast("获取相机权限失败")
                            }
                        }
                    })
            }
            imgComeBackLater.onDebounceClickListener { finish() }
        }
    }

    override fun initData() {}

    override fun startObserve() {
        mViewModel.checkDFRight.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    if (entity.isAllow_check) {
                        FaceDetectionActivity.startActivity(this@FaceDetectionStartActivity)
                        finish()
                    } else {
                        mBind.containerFlLayer.visibility = View.VISIBLE
                    }
                }
            }
        }
    }

}