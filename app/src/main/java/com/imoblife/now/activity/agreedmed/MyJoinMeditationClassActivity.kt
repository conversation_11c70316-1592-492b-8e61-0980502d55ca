package com.imoblife.now.activity.agreedmed

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.drakeet.multitype.MultiTypeAdapter
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.Status
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.delegate.DailyMeditationClassJoinDelegate
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.databinding.LayoutAcMyJoinMeditationClassBinding
import com.imoblife.now.util.DisplayUtil
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-14
 * 描   述：我的参与_冥想班
 */
class MyJoinMeditationClassActivity :
    BaseVMActivity<MyJoinMeditationClassViewModel>() {

    companion object {
        fun startActivity(context: Context) {
            Intent(context, MyJoinMeditationClassActivity::class.java).let {
                context.startActivity(it)
            }
        }
    }

    private lateinit var mBind: LayoutAcMyJoinMeditationClassBinding

    private val mAdapter = MultiTypeAdapter()

    private var mItems = ArrayList<Any>()

    private lateinit var loadingHelper: LoadingHelper

    override fun getLayoutResId() = R.layout.layout_ac_my_join_meditation_class

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(
        MyJoinMeditationClassViewModel::class.java
    )

    override fun initView() {
        loadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_i_participate_in),
            NavIconType.BACK,
            true
        )
        mBind = mBinding as LayoutAcMyJoinMeditationClassBinding
        mAdapter.apply {
            register(DailyMeditationClassJoinDelegate())
            items = mItems
        }
        mBind.apply {
            recyclerView.apply {
                adapter = mAdapter
                addItemDecoration(
                    CommonItemDecoration(
                        0,
                        DisplayUtil.dip2px(10f),
                        DisplayUtil.dip2px(20f),
                        0,
                        DisplayUtil.dip2px(20f),
                        DisplayUtil.dip2px(10f)
                    )
                )
            }
            smartRefreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    mViewModel.getJoinMeditationClass()
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    mViewModel.getMoreJoinMeditationClass()
                }
            })
        }
    }

    override fun initData() {
        loadingHelper.apply {
            showLoadingView()
            setOnReloadListener {
                mViewModel.getJoinMeditationClass()
            }
        }
        mViewModel.getJoinMeditationClass()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.meditationClassJoinList.observe(this) {
            if (it.isSuccess) {
                when (it.status) {
                    Status.REFRESHSUCCESS -> {
                        it.successData?.apply {
                            if (this.isEmpty()) {
                                loadingHelper.showEmptyView()
                            } else {
                                loadingHelper.showContentView()
                                mItems.clear()
                                mItems.addAll(this)
                                mAdapter.notifyDataSetChanged()
                            }
                        }
                        mBind.smartRefreshLayout.finishRefresh()
                    }
                    Status.MORESUCCESS -> {
                        if (!it.successData.isNullOrEmpty()) {
                            it.successData?.let { list ->
                                val oldSize = mItems.size
                                mItems.addAll(oldSize, list)
                                mAdapter.notifyItemRangeInserted(oldSize, list.size)
                            }
                        }
                        mBind.smartRefreshLayout.finishLoadMore()
                    }
                    Status.NOMOREDATA -> {
                        mBind.smartRefreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    Status.EMPTYDATA -> {
                        loadingHelper.showEmptyView()
                        mBind.smartRefreshLayout.finishRefreshWithNoMoreData()
                    }
                    else -> {
                    }
                }
            } else {
                when (it.status) {
                    Status.FAILED -> {
                        loadingHelper.showErrorView()
                        mBind.smartRefreshLayout.finishRefresh(false)
                    }
                    Status.MOREFAIL -> {
                        mBind.smartRefreshLayout.finishLoadMore(false)
                    }
                    else -> {
                    }
                }
            }
        }
    }

}