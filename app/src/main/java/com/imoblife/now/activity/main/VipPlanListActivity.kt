package com.imoblife.now.activity.main

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.VipPlanListAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.databinding.LayoutAcVipPlanListBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.view.dialog.HomePracticeVipPlanListAlterDialog
import com.imoblife.now.viewmodel.HomeViewModel

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/1/15
 * 描   述：会员计划 - 列表
 */
class VipPlanListActivity : BaseVMActivity<HomeViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            context.startActivity(Intent(context, VipPlanListActivity::class.java))
        }

    }

    private lateinit var mBind: LayoutAcVipPlanListBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) {
        VipPlanListAdapter { plan_id, _ ->
            HomePracticeVipPlanListAlterDialog(this) { bool ->
                if (bool) mViewModel.uploadVipPlanClickLog(plan_id)
            }.showDialog()
        }
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(HomeViewModel::class.java)

    override fun getLayoutResId(): Int = R.layout.layout_ac_vip_plan_list

    override fun initView() {
        mLoadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_membership_plan_txt),
            NavIconType.BACK,
            true
        )
        mBind = mBinding as LayoutAcVipPlanListBinding
        mBind.recyclerView.apply {
            addItemDecoration(CommonItemDecoration(16.dp, 16.dp, 12.dp, 16.dp, 12.dp, 16.dp))
            adapter = mAdapter
        }
    }

    override fun initData() {
        mLoadingHelper.apply {
            showLoadingView()
            setOnReloadListener { mViewModel.getVipPlanList() }
        }
        mViewModel.getVipPlanList()
    }

    override fun startObserve() {
        mViewModel.apply {
            vipPlanList.observe(this@VipPlanListActivity) {
                if (it.isSuccess) {
                    it.successData?.let { list ->
                        if (list.isEmpty()) {
                            mLoadingHelper.showEmptyView()
                        } else {
                            mLoadingHelper.showContentView()
                            mAdapter.setNewData(list)
                        }
                    }
                } else {
                    mLoadingHelper.showErrorView()
                }
            }
            // 首页 - 练习 - 上报会员计划点击记录
            uploadVipPlanClickLog.observe(this@VipPlanListActivity) { finish() }
        }
    }

}