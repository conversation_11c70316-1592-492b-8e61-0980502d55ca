package com.imoblife.now.activity.monitor.history

import android.content.Context
import android.content.Intent
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.commlibrary.utils.ViewType
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.SleepMonitorViewModel
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarAdapter
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.AllSleepHistoryEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcSleepRecordingBinding
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ToastUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import org.greenrobot.eventbus.EventBus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/28
 * 描   述：睡眠记录
 */
class SleepRecordingActivity : BaseVMActivity<SleepMonitorViewModel>() {

    companion object {

        fun startActivity(context: Context, entity: AllSleepHistoryEntity.ListEntity) {
            Intent(context, SleepRecordingActivity::class.java).apply {
                if (EmptyUtils.isNotEmpty(entity)) putExtra(
                    ConsCommon.SLEEP_RECORDING_CONTENT_ENTITY,
                    entity
                )
                context.startActivity(this)
            }
        }

    }

    private lateinit var mBind: LayoutAcSleepRecordingBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private var mAllSleepHistoryItemEntity: AllSleepHistoryEntity.ListEntity? = null

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .statusBarColor(R.color.color_0C0E1A)
            .statusBarDarkFont(false)
            .fitsSystemWindows(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_sleep_recording

    override fun superInit(intent: Intent?) {
        if (hasExtra(ConsCommon.SLEEP_RECORDING_CONTENT_ENTITY)) {
            mAllSleepHistoryItemEntity =
                intent?.getParcelableExtra(ConsCommon.SLEEP_RECORDING_CONTENT_ENTITY)
        }
    }

    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mLoadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_sleep_recording),
            NavIconType.BACK_SLEEP_MONITORING_REPORT_COURSE,
            "",
            null,
            true,
        )
        mLoadingHelper.getAdapter<ToolbarAdapter>(ViewType.TITLE).setRightTitleColor(
            ContextCompat.getColor(this, R.color.white),
            ContextCompat.getColor(this, R.color.main_color)
        )
        mBind = mBinding as LayoutAcSleepRecordingBinding

        mBind.tvDeleteTxt.setOnClickListener {
            mAllSleepHistoryItemEntity?.let {
                mViewModel.deleteSleepRecord(it.id)
            }
        }
    }

    override fun initData() {
        mBind.apply {
            entity = mAllSleepHistoryItemEntity
            executePendingBindings()
        }
    }

    override fun startObserve() {
        mViewModel.deleteRecord.observe(this) { uiStatus ->
            if (uiStatus.isSuccess && uiStatus.successData==true){
                ToastUtils.showLongToastCenter("记录删除成功")
                mAllSleepHistoryItemEntity?.let {
                    EventBus.getDefault().post(BaseEvent(ConsEventCode.SLEEP_RECORD_DELETE,it.id))
                }
                finish()
            }
        }
    }

}