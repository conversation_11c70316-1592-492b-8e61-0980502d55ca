package com.imoblife.now.activity.collect

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager.widget.ViewPager
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.R
import com.imoblife.now.activity.poster.PosterCollectFragment
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.BasePageAdapter
import com.imoblife.now.databinding.ActivityCollectBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.statistics.SensorsDataEvent

class CollectActivity : BaseVMActivity<CollectViewModel>() {

    private lateinit var pagerAdapter: BasePageAdapter<String>
    private val title = listOf("系列", "单节","今日寄语")

    private lateinit var mBind: ActivityCollectBinding

    override fun getLayoutResId() = R.layout.activity_collect

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(CollectViewModel::class.java)
    companion object{
        @JvmStatic
        fun startCollect(context: Context){
            if (UserMgr.getInstance().isLogin) {
                context.startActivity(Intent(context, CollectActivity::class.java))
            } else {
                LoginCenter.getInstance().loginControl(context)
            }
            SensorsDataEvent.minePageClick("收藏")
        }
    }
    override fun initView() {
        mBind = mBinding as ActivityCollectBinding
        mBind.toolbar.apply {
            titleBackImg.setOnClickListener { finish() }
            titleContentText.text = getString(R.string.string_my_favorite)
            titleLine.visibility = View.GONE
        }
    }

    override fun initData() {
        mBind.apply {
            tabLayout.setSnapOnTabClick(true)
            viewpager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {}
                override fun onPageSelected(position: Int) {
                    for (i in 0 until pagerAdapter.count) {
                        val isSelect = i == position
                        tabLayout.getTitleView(i).textSize = if (isSelect) 15f else 14f
                    }
                }

                override fun onPageScrollStateChanged(state: Int) {}
            })
            pagerAdapter =
                object : BasePageAdapter<String>(<EMAIL>) {
                    override fun getFragment(position: Int, data: String?): Fragment {
                        return if (position==2){
                            PosterCollectFragment.geInstance()
                        }else{
                            CollectFragment.geInstance(position)
                        }
                    }
                    override fun getTitle(data: String?): String? {
                        return data
                    }
                }
            pagerAdapter.setNewData(title)
            viewpager.adapter = pagerAdapter
            tabLayout.setViewPager(viewpager)
            tabLayout.getTitleView(0)?.textSize = 15f
        }
    }

    override fun startObserve() {

    }

}