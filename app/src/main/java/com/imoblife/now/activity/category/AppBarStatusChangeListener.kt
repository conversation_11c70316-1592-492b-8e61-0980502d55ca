package com.imoblife.now.activity.category

import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener
import kotlin.math.abs

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：AppBarLayout - 滑动状态监听
 */
abstract class AppBarStatusChangeListener : OnOffsetChangedListener {

    private var mCurrentState: AppBarStatus = AppBarStatusIdle

    override fun onOffsetChanged(appBarLayout: AppBarLayout, verticalOffset: Int) {
        mCurrentState = when {
            verticalOffset == 0 -> {
                if (mCurrentState != AppBarStatusExpanded) {
                    onStateChanged(appBarLayout, AppBarStatusExpanded, verticalOffset)
                }
                AppBarStatusExpanded
            }
            abs(verticalOffset) >= appBarLayout.totalScrollRange -> {
                if (mCurrentState != AppBarStatusCollapsed) {
                    onStateChanged(appBarLayout, AppBarStatusCollapsed, verticalOffset)
                }
                AppBarStatusCollapsed
            }
            else -> {
                onStateChanged(appBarLayout, AppBarStatusIdle, verticalOffset)
                AppBarStatusIdle
            }
        }
    }

    abstract fun onStateChanged(
        appBarLayout: AppBarLayout?,
        state: AppBarStatus,
        verticalOffset: Int
    )

}