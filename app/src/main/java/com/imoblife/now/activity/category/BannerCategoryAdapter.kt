package com.imoblife.now.activity.category

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.databinding.LayoutItemCategoryNavigationBannerBinding
import com.imoblife.now.util.ImageLoader
import com.youth.banner.adapter.BannerAdapter

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/8
 * 描   述：首页 - 金刚区 - 分类导航 - banner - adapter
 */
class BannerCategoryAdapter(data: MutableList<CategoryNavigationItemEntity>) :
    BannerAdapter<CategoryNavigationItemEntity, BannerCategoryAdapter.BannerCategoryHolder>(data) {

    override fun onCreateHolder(parent: ViewGroup?, viewType: Int) = BannerCategoryHolder(
        LayoutInflater.from(parent!!.context)
            .inflate(R.layout.layout_item_category_navigation_banner, parent, false)
    )

    override fun onBindView(
        holder: BannerCategoryHolder,
        data: CategoryNavigationItemEntity,
        position: Int,
        size: Int
    ) {

        holder.mBind?.apply {
            entity = data
            ImageLoader.loadImageUrl(root.context, data.banner, rivImgBg)
            executePendingBindings()
        }
    }

    class BannerCategoryHolder(view: View) : RecyclerView.ViewHolder(view) {

        val mBind = DataBindingUtil.bind<LayoutItemCategoryNavigationBannerBinding>(view)

    }

}