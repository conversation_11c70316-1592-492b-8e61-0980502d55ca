package com.imoblife.now.activity.main

import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.bean.VipPlanPopupEntity
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.LayoutAcFirstTrainingBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.SpUtil
import com.imoblife.now.viewmodel.HomeViewModel

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/3/17
 * 描   述：首训 - 会员计划 - Activity
 */
class FirstTrainingActivity : BaseVMActivity<HomeViewModel>() {

    companion object {

        fun startActivity(activity: Activity) {
            val intent = Intent(activity, FirstTrainingActivity::class.java)
            activity.startActivity(intent)
            activity.overridePendingTransition(R.anim.enter_anim_slide, R.anim.exit_anim_slide)
        }

    }

    private lateinit var mBind: LayoutAcFirstTrainingBinding

    private var mVipPlanPopupEntity: VipPlanPopupEntity? = null

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .statusBarDarkFont(true)
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[HomeViewModel::class.java]

    override fun getLayoutResId(): Int = R.layout.layout_ac_first_training

    override fun initView() {
        mBind = mBinding as LayoutAcFirstTrainingBinding
        SensorsDataEvent.practiceQuidanceExposure(2)
    }

    override fun initData() {
        val entityValue = SpUtil.getInstance().getStringValue(ConsSp.SP_KEY_HOME_PRACTICE_FIRST_TRAINING, "")
        if (!TextUtils.isEmpty(entityValue)) {
            try {
                mVipPlanPopupEntity = Gson().fromJson(entityValue, VipPlanPopupEntity::class.java)
                mVipPlanPopupEntity?.let {
                    mBind.apply {
                        ImageLoader.loadImageUrl(this@FirstTrainingActivity, it.img_bg, imgBg)
                        ImageLoader.loadImageUrl(this@FirstTrainingActivity, it.img, img)

                        stvBtn.onDebounceClickListener {
                            FirstTrainingStartActivity.startActivity(this@FirstTrainingActivity)
                            finish()
                        }
                    }
                }
            } catch (e: Exception) {
            }
        }
    }

    override fun startObserve() {}

    override fun onBackPressed() {}

}