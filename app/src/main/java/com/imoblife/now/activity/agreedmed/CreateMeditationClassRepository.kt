package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.MeditationClassCreateEntity
import com.imoblife.now.bean.MeditationTeamRulesEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.net.*
import io.reactivex.Observable

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-12
 * 描   述：创建约定冥想_Repository
 */
class CreateMeditationClassRepository : BaseRepository() {

    /**
     * 获取冥想组队规则
     */
    fun getMeditationTeamRules(_meditationTeamRulesEntity: MutableLiveData<UiStatus<MeditationTeamRulesEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .meditationTeamRules
            .onErrorResumeNext { _: Throwable? -> Observable.never() }
            .map(SaveCatchMap.getMapFunction(ConsCommon.MEDITATION_RULES_CACHE_DATA))
            .publish(PublishFunction.getFunction(CatchObserver.getCacheObservable<BaseResult<MeditationTeamRulesEntity>>(
                ConsCommon.MEDITATION_RULES_CACHE_DATA,
                true)))
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationTeamRulesEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationTeamRulesEntity>?) {
                    response?.result?.let {
                        _meditationTeamRulesEntity.value =
                            UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    _meditationTeamRulesEntity.value = UiStatus(isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED)
                }
            })
    }

    /**
     * 创建冥想班
     *
     * @param parameters 参数map
     */
    fun createMeditationClass(
        parameters: MutableMap<String, String?>,
        _meditationClassCreateEntity: MutableLiveData<UiStatus<MeditationClassCreateEntity>>,
    ) {
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .createMeditationClass(parameters)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MeditationClassCreateEntity>>() {
                override fun onSuccess(response: BaseResult<MeditationClassCreateEntity>?) {
                    response?.result?.let {
                        _meditationClassCreateEntity.value =
                            UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    _meditationClassCreateEntity.value = UiStatus(isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED)
                }
            })
    }

}