package com.imoblife.now.activity.member;

import android.annotation.SuppressLint;

import androidx.appcompat.widget.AppCompatImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.imoblife.now.R;
import com.imoblife.now.bean.Subscribe;
import com.imoblife.now.util.ImageLoader;

import java.util.ArrayList;
import java.util.List;

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/6/25
 * 描   述：订阅｜会员中心 - 竖SkuList - Now|Plus - Adapter
 */
public class SubscribeVerticalAdapter extends BaseQuickAdapter<Subscribe, BaseViewHolder> {

    private OnItemClickLister onItemClickLister;

    public void setOnItemClickLister(OnItemClickLister onItemClickLister) {
        this.onItemClickLister = onItemClickLister;
    }

    private int selectPosition;

    private ArrayList<Integer> select = new ArrayList<>();

    public SubscribeVerticalAdapter() {
        super(R.layout.layout_sku_vip_item_style_b);
    }

    public void setData(List<Subscribe> subscribes) {
        if (subscribes == null) {
            return;
        }
        if (select != null) {
            select.clear();
        }
        for (int i = 0; i < subscribes.size(); i++) {
            if ("true".equals(subscribes.get(i).getChecked())) {
                selectPosition = i;
                select.add(i);
            }
        }
        setNewData(subscribes);
    }

    public Subscribe getSelectSubscribe() {
        if (getData() == null || getData().size() == 0) {
            return null;
        }
        return getData().get(selectPosition);
    }

    public int getSelectPosition() {
        return selectPosition;
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    protected void convert(BaseViewHolder helper, Subscribe item) {
        AppCompatImageView img = helper.getView(R.id.vip_item_view);
        int position = helper.getLayoutPosition();
        img.setOnClickListener(v -> {
            if (selectPosition != position) {
                if (onItemClickLister != null) {
                    onItemClickLister.itemData(item, position);
                }
                if (!select.contains(position)) {
                    select.clear();
                    select.add(position);
                }
                selectPosition = position;
                notifyDataSetChanged();
            }
        });
        if (select.contains(position)) {
            ImageLoader.loadImageUrl(mContext, item.getBackground_checked(), img);
        } else {
            ImageLoader.loadImageUrl(mContext, item.getBackground_default(), img);
        }
    }

    public interface OnItemClickLister<T> {
        void itemData(T data, int position);
    }

}

