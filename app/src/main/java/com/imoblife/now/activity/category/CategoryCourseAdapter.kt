package com.imoblife.now.activity.category

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.adapter.CourseAdapterCommentUtils
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.util.ImageLoader
import com.makeramen.roundedimageview.RoundedImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - 课程
 */
class CategoryCourseAdapter :
    BaseQuickAdapter<CategoryNavigationItemEntity, BaseViewHolder>(R.layout.layout_square_course_inner_item_view) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationItemEntity?) {
        item?.apply {
            val courseThumbImg = holder.getView<RoundedImageView>(R.id.course_thumb_img)
            val courseTagVipImg = holder.getView<RoundedImageView>(R.id.course_tag_vip_img)
            val courseNameTxt = holder.getView<TextView>(R.id.course_name_txt)
            val courseCountTxt = holder.getView<TextView>(R.id.course_count_txt)

            ImageLoader.loadImageUrl(mContext, thumb_img, courseThumbImg)
            CourseAdapterCommentUtils.setCourseUITag(courseTagVipImg, label_style)
            courseNameTxt.text = title
            courseCountTxt.text = label
            holder.itemView.setOnClickListener { pageRoute(mContext, holder.layoutPosition) }
        }
    }

}