package com.imoblife.now.activity.course

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.R
import com.imoblife.now.activity.search.SearchActivity
import com.imoblife.now.adapter.course.CourseCategoryCourseListAdapter
import com.imoblife.now.adapter.course.CourseCategoryHotListAdapter
import com.imoblife.now.adapter.course.CourseCategoryTagListAdapter
import com.imoblife.now.adapter.home.HomeAdAdapter
import com.imoblife.now.bean.Course
import com.imoblife.now.bean.CourseTagGroup
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityCourseCategoryBinding
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.NetworkUtils
import com.imoblife.now.viewmodel.AdViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 课程分类Activity
 */
class CourseCategoryActivity : BaseVMActivity<CourseModel>(), View.OnClickListener {

    // 神策数据 - 当前页面id
    private var mPageId = 0

    // 神策数据 - 当前页面 - 分类id
    private var mCategoryId = 0

    companion object {

        @JvmStatic
        fun openCourseCategory(
            mContext: Context,
            showHotCourse: Boolean = true,
            title: String = "全部内容",
            pageId: Int = 0,
            categoryId: Int = 0
        ) {
            Intent(mContext, CourseCategoryActivity::class.java).let {
                it.putExtra(ConsIntent.BUNDLE_IS_SHOE_HOT, showHotCourse)
                it.putExtra(ConsIntent.BUNDLE_TITLE_KEY, title)
                it.putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId)
                it.putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId)
                mContext.startActivity(it)
            }
        }

    }

    private var mPage = 1
    private var mTag = ""
    private var size = 10
    private var total = 0
    private var isShowHot = false
    private val mGson by lazy { Gson() }
    private val mUploadIds by lazy { mutableListOf<List<Int>>() }
    private val mHotData by lazy { mutableListOf<Course>() }
    private val mHotListAdapter by lazy { CourseCategoryHotListAdapter(mHotData) }
    private val mTagData by lazy { mutableListOf<CourseTagGroup>() }
    private val mTagListAdapter by lazy { CourseCategoryTagListAdapter(mViewModel, mTagData) }
    private val mCourseData by lazy { mutableListOf<Course>() }
    private val mCourseListAdapter by lazy { CourseCategoryCourseListAdapter(mCourseData) }

    private val mHomeAdAdapter by lazy { HomeAdAdapter(mutableListOf(), true) }

    private val mAdViewModel by viewModels<AdViewModel>()

    private val mConcatAdapter by lazy {
        ConcatAdapter(mHotListAdapter, mHomeAdAdapter, mTagListAdapter, mCourseListAdapter)
    }

    override fun getLayoutResId() = R.layout.activity_course_category

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
                it.let {
                    mPageId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0)
                }
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
                it.let {
                    mCategoryId = it.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0)
                }
            }
        }
    }

    override fun initVM() = ViewModelProvider(this).get(CourseModel::class.java)

    private lateinit var mBind: ActivityCourseCategoryBinding

    override fun initView() {
        mBind = mBinding as ActivityCourseCategoryBinding
        isShowHot = intent.getBooleanExtra(ConsIntent.BUNDLE_IS_SHOE_HOT, isShowHot)
        mBind.apply {
            toolbar.apply {
                titleContentText.text = intent.getStringExtra(ConsIntent.BUNDLE_TITLE_KEY)
                titleMoreImg.setImageResource(R.mipmap.icon_category_search)
                titleBackImg.setOnClickListener(this@CourseCategoryActivity)
                titleMoreImg.setOnClickListener(this@CourseCategoryActivity)
            }
            noNet.tvRefresh.setOnClickListener(this@CourseCategoryActivity)
            recyclerView.apply {
                layoutManager = LinearLayoutManager(this@CourseCategoryActivity)
                adapter = mConcatAdapter
                itemAnimator?.addDuration = 0
                itemAnimator?.changeDuration = 0
                itemAnimator?.moveDuration = 0
                itemAnimator?.removeDuration = 0
                (itemAnimator as DefaultItemAnimator).supportsChangeAnimations = false
            }
            smartRefresh.setEnableRefresh(false)
            smartRefresh.setOnLoadMoreListener {
                mPage++
                mViewModel.getCourseByTag(mTag, mPage, size, mPageId, mCategoryId)
            }
            backTopView.setOnClickListener(this@CourseCategoryActivity)
            recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    total += dy
                    if (total > DisplayUtil.getScreenHeight(this@CourseCategoryActivity)) {
                        backTopView.visibility = View.VISIBLE
                    } else if (total == 0) {
                        backTopView.visibility = View.INVISIBLE
                    }
                }
            })
        }
    }

    override fun initData() {
        if (isShowHot) {
            mViewModel.getHotCourseList(mPageId, mCategoryId)
        }
        mViewModel.getCourseTag()
        mCourseData.clear()
        mPage = 1
        mTag = ""
        mViewModel.getCourseByTag(mTag, mPage, size, mPageId, mCategoryId)
        mTagListAdapter.clearSelectTag()

        mAdViewModel.getCampaignTriggerAds(ConsCommon.POSITION_IN_BREAK_LOOKING_FOR_A_COURSE)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.hotCourseLiveData.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { hotData ->
                    mHotData.clear()
                    mHotData.addAll(hotData)
                    mHotListAdapter.notifyDataSetChanged()
                }
            }
        }
        mViewModel.selectTagIdsLiveData.observe(this) {
            val sortMap = it.toSortedMap()
            mUploadIds.clear()
            sortMap.keys.forEach { ids ->
                sortMap[ids]?.let { listId ->
                    if (listId.isNotEmpty()) {
                        mUploadIds.add(listId)
                    }
                }
            }
            mTag = if (mUploadIds.isNotEmpty()) mGson.toJson(mUploadIds) else ""
            mPage = 1
            mCourseData.clear()
            mCourseListAdapter.showLoading()
            lifecycleScope.launch {
                delay(260)
                mViewModel.getCourseByTag(mTag, mPage, size, mPageId, mCategoryId)
            }
        }
        mViewModel.courseByTagLiveData.observe(this) {
            mCourseListAdapter.hideLoading()
            mBind.apply {
                if (it.isSuccess) {
                    it.successData?.let { successData ->
                        if (successData.size >= size) {
                            smartRefresh.finishLoadMore()
                            smartRefresh.setEnableLoadMore(true)
                            smartRefresh.resetNoMoreData()
                        } else {
                            smartRefresh.finishLoadMoreWithNoMoreData()
                            smartRefresh.setEnableLoadMore(false)
                        }
                        mCourseData.addAll(successData)
                        mCourseListAdapter.setCategoryName(mTag)
                        mCourseListAdapter.notifyDataSetChanged()
                    }
                } else {
                    smartRefresh.finishLoadMore()
                    smartRefresh.finishRefresh()
                }
                if (mCourseData.size <= 0) {
                    if (!NetworkUtils.isNetworkAvailable()) {
                        noNet.root.visibility = View.VISIBLE
                        recyclerView.visibility = View.INVISIBLE
                    }
                } else {
                    noNet.root.visibility = View.INVISIBLE
                    recyclerView.visibility = View.VISIBLE
                }
            }
        }
        mViewModel.courseTagLiveData.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { tagData ->
                    mTagData.clear()
                    mTagData.addAll(tagData)
                    mTagListAdapter.notifyDataSetChanged()
                }
            }
        }

        mAdViewModel.campaignTriggerAds.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { ad ->
                    if (isShowHot) mHomeAdAdapter.setNewData(
                        mutableListOf(ad),
                        true,
                        16F,
                        2F,
                        16F,
                        10F
                    ) else mHomeAdAdapter.setNewData(
                        mutableListOf(ad),
                        true,
                        16F,
                        16F,
                        16F,
                        6F
                    )
                }
            }
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.title_back_img -> {
                finish()
            }
            R.id.back_top_view -> {
                mBind.apply {
                    recyclerView.scrollToPosition(0)
                    total = 0
                    backTopView.visibility = View.INVISIBLE
                }
            }
            R.id.title_more_img -> {
                SearchActivity.openSearchActivity(
                    this,
                    null,
                    "全部课程",
                    ConsCommon.PAGE_ID_SEARCH_FIND_COURSE
                )
            }
            R.id.tv_refresh -> {
                initData()
            }
        }
    }

}