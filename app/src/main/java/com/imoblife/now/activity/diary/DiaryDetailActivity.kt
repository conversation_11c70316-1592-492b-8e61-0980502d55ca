package com.imoblife.now.activity.diary

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.R
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityDiaryDetailBinding

class DiaryDetailActivity : BaseVMActivity<DiaryViewModel>(), View.OnClickListener {

    companion object {
        fun startDiaryDetailActivity(
            context: Context,
            diaryContent: String,
            createDate: String,
            createTime: String
        ) {
            Intent(context, DiaryDetailActivity::class.java).let {
                it.putExtra(ConsIntent.BUNDLE_DIARY_CONTENT, diaryContent)
                it.putExtra(ConsIntent.BUNDLE_DIARY_DATE, createDate)
                it.putExtra(ConsIntent.BUNDLE_DIARY_TIME, createTime)
                context.startActivity(it)
            }
        }
    }

    private var mDiaryContent: String = ""
    private var mDiaryCreateDate: String = ""
    private var mDiaryCreateTime: String = ""

    private lateinit var mBind: ActivityDiaryDetailBinding

    override fun getLayoutResId(): Int = R.layout.activity_diary_detail

    override fun superInit(intent: Intent?) {
        mDiaryContent = intent?.getStringExtra(ConsIntent.BUNDLE_DIARY_CONTENT) ?: mDiaryContent
        mDiaryCreateDate = intent?.getStringExtra(ConsIntent.BUNDLE_DIARY_DATE) ?: mDiaryCreateDate
        mDiaryCreateTime = intent?.getStringExtra(ConsIntent.BUNDLE_DIARY_TIME) ?: mDiaryCreateTime
    }

    override fun initVM(): DiaryViewModel = ViewModelProvider(
        this,
        ViewModelProvider.NewInstanceFactory()
    ).get(DiaryViewModel::class.java)


    override fun initView() {
        mBind = mBinding as ActivityDiaryDetailBinding
        mBind.apply {
            commonTitle.apply {
                titleContentText.text = getString(R.string.diary_title)
                titleBackImg.setOnClickListener(this@DiaryDetailActivity)
            }
            tvDiaryDate.text = mDiaryCreateDate
            tvDiaryTime.text = mDiaryCreateTime
            tvContent.text = mDiaryContent
        }
    }

    override fun initData() {
    }

    override fun startObserve() {
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.title_back_img -> {
                finish()
            }
            else -> {}
        }
    }

}