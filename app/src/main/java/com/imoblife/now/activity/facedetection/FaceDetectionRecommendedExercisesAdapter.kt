package com.imoblife.now.activity.facedetection

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.activity.product.ProductDetailActivity
import com.imoblife.now.bean.BuriedPointCourseEntity
import com.imoblife.now.bean.Course
import com.imoblife.now.databinding.LayoutItemFaceDetectionReportRecommendCourseBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.ImageLoader

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/11/21
 * 描   述：人脸检测 - 报告 - rv - 推荐课程
 */
class FaceDetectionRecommendedExercisesAdapter : BaseQuickAdapter<Course, BaseViewHolder>(R.layout.layout_item_face_detection_report_recommend_course) {

    override fun convert(holder: BaseViewHolder, item: Course?) {
        item?.let {
            holder.getBinding(LayoutItemFaceDetectionReportRecommendCourseBinding::bind).apply {
                ImageLoader.loadImageUrl(
                    mContext,
                    it.body_img_new,
                    rivImg,
                    R.mipmap.img_common_place_holder,
                )
                tvTitle.text = it.title
                tvContent.text = String.format(
                    mContext.resources.getString(R.string.count_course_count_listen_txt),
                    it.small_num,
                    it.practiceCount
                )

                root.onDebounceClickListener {
                    goCourse(it, holder)
                }
            }
        }
    }

    fun goCourse(it: Course, holder: BaseViewHolder? = null) {
        SensorsDataEvent.courseClick(
            BuriedPointCourseEntity(
                course_id = it.id,
                course_name = it.title,
                course_type = it.type,
                teacher_name = it.teacher_name,
                teacher_id = it.teacher,
                course_place_module = "面部情绪识别",
                course_category = "面部情绪识别",
                course_category_index = holder?.layoutPosition ?: 0,
                is_single_course = it.isSingleCourse,
                course_is_free = it.isFreeCourse,
                referrer_entrance_type = "面部情绪识别",
                page_id = it.page_id,
                category_id = it.category_id,
                page_title = it.page_title,
                category_title = it.category_title
            )
        )
        ProductDetailActivity.openActivity(
            mContext,
            it.id,
            it.isSingleCourse,
            it.page_id,
            it.category_id
        )
    }

}