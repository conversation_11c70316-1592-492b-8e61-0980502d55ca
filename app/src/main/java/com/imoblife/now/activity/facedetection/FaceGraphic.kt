package com.imoblife.now.activity.facedetection

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceContour
import com.google.mlkit.vision.face.FaceLandmark
import com.google.mlkit.vision.face.FaceLandmark.LandmarkType
import java.util.Locale
import kotlin.math.abs
import kotlin.math.max

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - FaceGraphic
 */
class FaceGraphic constructor(overlay: GraphicOverlay?, private val face: Face) : GraphicOverlay.Graphic(overlay) {
    private val facePositionPaint: Paint
    private val numColors = COLORS.size
    private val idPaints = Array(numColors) { Paint() }
    private val boxPaints = Array(numColors) { Paint() }
    private val labelPaints = Array(numColors) { Paint() }

    init {
        val selectedColor = Color.WHITE
        facePositionPaint = Paint()
        facePositionPaint.color = selectedColor
        for (i in 0 until numColors) {
            idPaints[i] = Paint()
            idPaints[i].color = COLORS[i][0]
            idPaints[i].textSize = ID_TEXT_SIZE
            boxPaints[i] = Paint()
            boxPaints[i].color = COLORS[i][1]
            boxPaints[i].style = Paint.Style.STROKE
            boxPaints[i].strokeWidth = BOX_STROKE_WIDTH
            labelPaints[i] = Paint()
            labelPaints[i].color = COLORS[i][1]
            labelPaints[i].style = Paint.Style.FILL
        }
    }

    override fun draw(canvas: Canvas) {
        val x = translateX(face.boundingBox.centerX().toFloat())
        val y = translateY(face.boundingBox.centerY().toFloat())
        canvas.drawCircle(x, y, FACE_POSITION_RADIUS, facePositionPaint)

        val left = x - scale(face.boundingBox.width() / 2.0f)
        val top = y - scale(face.boundingBox.height() / 2.0f)
        val right = x + scale(face.boundingBox.width() / 2.0f)
        val bottom = y + scale(face.boundingBox.height() / 2.0f)
        val lineHeight = ID_TEXT_SIZE + BOX_STROKE_WIDTH
        var yLabelOffset: Float = if (face.trackingId == null) 0f else -lineHeight

        val colorID = if (face.trackingId == null) 0 else abs(face.trackingId!! % NUM_COLORS)

        var textWidth = idPaints[colorID].measureText("ID: " + face.trackingId)
        if (face.smilingProbability != null) {
            yLabelOffset -= lineHeight
            textWidth =
                max(
                    textWidth,
                    idPaints[colorID].measureText(
                        String.format(Locale.US, "Happiness: %.2f", face.smilingProbability)
                    )
                )
        }
        if (face.leftEyeOpenProbability != null) {
            yLabelOffset -= lineHeight
            textWidth =
                max(
                    textWidth,
                    idPaints[colorID].measureText(
                        String.format(Locale.US, "Left eye open: %.2f", face.leftEyeOpenProbability)
                    )
                )
        }
        if (face.rightEyeOpenProbability != null) {
            yLabelOffset -= lineHeight
            textWidth =
                max(
                    textWidth,
                    idPaints[colorID].measureText(
                        String.format(
                            Locale.US,
                            "Right eye open: %.2f",
                            face.rightEyeOpenProbability
                        )
                    )
                )
        }

        yLabelOffset = yLabelOffset - 3 * lineHeight
        textWidth =
            Math.max(
                textWidth,
                idPaints[colorID].measureText(
                    String.format(Locale.US, "EulerX: %.2f", face.headEulerAngleX)
                )
            )
        textWidth =
            Math.max(
                textWidth,
                idPaints[colorID].measureText(
                    String.format(Locale.US, "EulerY: %.2f", face.headEulerAngleY)
                )
            )
        textWidth =
            Math.max(
                textWidth,
                idPaints[colorID].measureText(
                    String.format(Locale.US, "EulerZ: %.2f", face.headEulerAngleZ)
                )
            )

        yLabelOffset += ID_TEXT_SIZE
        if (face.trackingId != null) {
            canvas.drawText("ID: " + face.trackingId, left, top + yLabelOffset, idPaints[colorID])
            yLabelOffset += lineHeight
        }

        val facePoints = mutableListOf<PointF>()
        val leftEyebrowTopPoints = mutableListOf<PointF>()
        val rightEyebrowTopPoints = mutableListOf<PointF>()
        val leftEyePoints = mutableListOf<PointF>()
        val rightEyePoints = mutableListOf<PointF>()
        val upperLipTopPoints = mutableListOf<PointF>()
        val upperLipBottomPoints = mutableListOf<PointF>()
        val lowerLipBottomPoints = mutableListOf<PointF>()
        val noseBridgePoints = mutableListOf<PointF>()
        val noseBottomPoints = mutableListOf<PointF>()
        val leftCheekPoints = mutableListOf<PointF>()
        val rightCheekPoints = mutableListOf<PointF>()

        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.color = Color.WHITE
//        paint.alpha = 90
        paint.style = Paint.Style.STROKE

        val allContours = face.allContours.filter { entity ->
            entity.faceContourType == FaceContour.FACE
                    || entity.faceContourType == FaceContour.LEFT_EYEBROW_TOP
//              || entity.faceContourType == FaceContour.LEFT_EYEBROW_BOTTOM
                    || entity.faceContourType == FaceContour.RIGHT_EYEBROW_TOP
//              || entity.faceContourType == FaceContour.RIGHT_EYEBROW_BOTTOM
                    || entity.faceContourType == FaceContour.LEFT_EYE
                    || entity.faceContourType == FaceContour.RIGHT_EYE
                    || entity.faceContourType == FaceContour.UPPER_LIP_TOP
                    || entity.faceContourType == FaceContour.UPPER_LIP_BOTTOM
//              || entity.faceContourType == FaceContour.LOWER_LIP_TOP
                    || entity.faceContourType == FaceContour.LOWER_LIP_BOTTOM
                    || entity.faceContourType == FaceContour.NOSE_BRIDGE
                    || entity.faceContourType == FaceContour.NOSE_BOTTOM
                    || entity.faceContourType == FaceContour.LEFT_CHEEK
                    || entity.faceContourType == FaceContour.RIGHT_CHEEK
        }
        for (contour in allContours) {
            val list = contour.points
                .filterIndexed { index, _ ->
                    when (contour.faceContourType) {
                        FaceContour.UPPER_LIP_TOP -> index == 5
                        FaceContour.UPPER_LIP_BOTTOM -> index == 0 || index == 8
                        FaceContour.LOWER_LIP_BOTTOM -> index == 4
                        FaceContour.NOSE_BRIDGE -> index == 0
                        FaceContour.NOSE_BOTTOM -> true
                        else -> index % 4 == 0
                    }
                }
            when (contour.faceContourType) {
                FaceContour.FACE -> facePoints.addAll(list)
                FaceContour.LEFT_EYEBROW_TOP -> leftEyebrowTopPoints.addAll(list)
                FaceContour.RIGHT_EYEBROW_TOP -> rightEyebrowTopPoints.addAll(list)
                FaceContour.LEFT_EYE -> leftEyePoints.addAll(list)
                FaceContour.RIGHT_EYE -> rightEyePoints.addAll(list)
                FaceContour.UPPER_LIP_TOP -> upperLipTopPoints.addAll(list)
                FaceContour.UPPER_LIP_BOTTOM -> upperLipBottomPoints.addAll(list)
                FaceContour.LOWER_LIP_BOTTOM -> lowerLipBottomPoints.addAll(list)
                FaceContour.NOSE_BRIDGE -> noseBridgePoints.addAll(list)
                FaceContour.NOSE_BOTTOM -> noseBottomPoints.addAll(list)
                FaceContour.LEFT_CHEEK -> leftCheekPoints.addAll(list)
                FaceContour.RIGHT_CHEEK -> rightCheekPoints.addAll(list)
            }
            for (point in list) {
                canvas.drawCircle(
                    translateX(point.x),
                    translateY(point.y),
                    FACE_POSITION_RADIUS,
                    facePositionPaint
                )
            }

            val path = Path()
            val startPoint = list[0]
            path.moveTo(translateX(startPoint.x), translateY(startPoint.y))
            if (contour.faceContourType != FaceContour.UPPER_LIP_BOTTOM) {
                list.forEach { point ->
                    if (point.x != startPoint.x && point.y != startPoint.y) {
                        path.lineTo(translateX(point.x), translateY(point.y))
                    }
                }
            }
            if (contour.faceContourType != FaceContour.NOSE_BOTTOM) {
                path.close()
            }
            canvas.drawPath(path, paint)
        }


        val globalPath = Path()
        globalPath.moveTo(translateX(facePoints[0].x), translateY(facePoints[0].y))
        globalPath.lineTo(translateX(rightEyebrowTopPoints[1].x), translateY(rightEyebrowTopPoints[1].y))
        globalPath.lineTo(translateX(noseBridgePoints[0].x), translateY(noseBridgePoints[0].y))

        globalPath.moveTo(translateX(facePoints[0].x), translateY(facePoints[0].y))
        globalPath.lineTo(translateX(leftEyebrowTopPoints[1].x), translateY(leftEyebrowTopPoints[1].y))
        globalPath.lineTo(translateX(noseBridgePoints[0].x), translateY(noseBridgePoints[0].y))

        globalPath.moveTo(translateX(rightEyebrowTopPoints[0].x), translateY(rightEyebrowTopPoints[0].y))
        globalPath.lineTo(translateX(facePoints[2].x), translateY(facePoints[2].y))

        globalPath.moveTo(translateX(rightEyebrowTopPoints[0].x), translateY(rightEyebrowTopPoints[0].y))
        globalPath.lineTo(translateX(facePoints[1].x), translateY(facePoints[1].y))

        globalPath.moveTo(translateX(rightEyebrowTopPoints[0].x), translateY(rightEyebrowTopPoints[0].y))
        globalPath.lineTo(translateX(facePoints[2].x), translateY(facePoints[2].y))

        globalPath.moveTo(translateX(rightEyebrowTopPoints[0].x), translateY(rightEyebrowTopPoints[0].y))
        globalPath.lineTo(translateX(rightEyePoints[2].x), translateY(rightEyePoints[2].y))
        globalPath.lineTo(translateX(rightCheekPoints[0].x), translateY(rightCheekPoints[0].y))
        globalPath.lineTo(translateX(noseBottomPoints[2].x), translateY(noseBottomPoints[2].y))
        globalPath.lineTo(translateX(upperLipBottomPoints[1].x), translateY(upperLipBottomPoints[1].y))
        globalPath.lineTo(translateX(upperLipTopPoints[0].x), translateY(upperLipTopPoints[0].y))
        globalPath.lineTo(translateX(upperLipBottomPoints[0].x), translateY(upperLipBottomPoints[0].y))
        globalPath.lineTo(translateX(lowerLipBottomPoints[0].x), translateY(lowerLipBottomPoints[0].y))
        globalPath.lineTo(translateX(upperLipBottomPoints[1].x), translateY(upperLipBottomPoints[1].y))
        globalPath.lineTo(translateX(rightCheekPoints[0].x), translateY(rightCheekPoints[0].y))
        globalPath.lineTo(translateX(facePoints[2].x), translateY(facePoints[2].y))

        globalPath.moveTo(translateX(rightCheekPoints[0].x), translateY(rightCheekPoints[0].y))
        globalPath.lineTo(translateX(facePoints[3].x), translateY(facePoints[3].y))
        globalPath.lineTo(translateX(upperLipBottomPoints[1].x), translateY(upperLipBottomPoints[1].y))
        globalPath.lineTo(translateX(facePoints[4].x), translateY(facePoints[4].y))
        globalPath.lineTo(translateX(lowerLipBottomPoints[0].x), translateY(lowerLipBottomPoints[0].y))
        globalPath.lineTo(translateX(facePoints[5].x), translateY(facePoints[5].y))
        globalPath.lineTo(translateX(upperLipBottomPoints[0].x), translateY(upperLipBottomPoints[0].y))
        globalPath.lineTo(translateX(noseBottomPoints[1].x), translateY(noseBottomPoints[1].y))
        globalPath.lineTo(translateX(upperLipBottomPoints[1].x), translateY(upperLipBottomPoints[1].y))

        globalPath.moveTo(translateX(upperLipBottomPoints[0].x), translateY(upperLipBottomPoints[0].y))
        globalPath.lineTo(translateX(noseBottomPoints[0].x), translateY(noseBottomPoints[0].y))
        globalPath.lineTo(translateX(leftCheekPoints[0].x), translateY(leftCheekPoints[0].y))
        globalPath.lineTo(translateX(facePoints[6].x), translateY(facePoints[6].y))

        globalPath.moveTo(translateX(leftCheekPoints[0].x), translateY(leftCheekPoints[0].y))
        globalPath.lineTo(translateX(facePoints[7].x), translateY(facePoints[7].y))

        globalPath.moveTo(translateX(leftCheekPoints[0].x), translateY(leftCheekPoints[0].y))
        globalPath.lineTo(translateX(leftEyePoints[0].x), translateY(leftEyePoints[0].y))
        globalPath.lineTo(translateX(leftEyebrowTopPoints[0].x), translateY(leftEyebrowTopPoints[0].y))
        globalPath.lineTo(translateX(facePoints[8].x), translateY(facePoints[8].y))

        globalPath.moveTo(translateX(noseBottomPoints[1].x), translateY(noseBottomPoints[1].y))
        globalPath.lineTo(translateX(rightEyePoints[0].x), translateY(rightEyePoints[0].y))
        globalPath.lineTo(translateX(noseBridgePoints[0].x), translateY(noseBridgePoints[0].y))
        globalPath.lineTo(translateX(leftEyePoints[2].x), translateY(leftEyePoints[2].y))
        globalPath.lineTo(translateX(noseBottomPoints[1].x), translateY(noseBottomPoints[1].y))

        canvas.drawPath(globalPath, paint)


        drawFaceLandmark(canvas, FaceLandmark.LEFT_EYE)
        drawFaceLandmark(canvas, FaceLandmark.RIGHT_EYE)
        drawFaceLandmark(canvas, FaceLandmark.LEFT_CHEEK)
        drawFaceLandmark(canvas, FaceLandmark.RIGHT_CHEEK)
    }

    private fun drawFaceLandmark(canvas: Canvas, @LandmarkType landmarkType: Int) {
        val faceLandmark = face.getLandmark(landmarkType)
        if (faceLandmark != null) {
            canvas.drawCircle(
                translateX(faceLandmark.position.x),
                translateY(faceLandmark.position.y),
                FACE_POSITION_RADIUS,
                facePositionPaint
            )
        }
    }

    companion object {
        private const val FACE_POSITION_RADIUS = 8.0f
        private const val ID_TEXT_SIZE = 30.0f
        private const val ID_Y_OFFSET = 40.0f
        private const val BOX_STROKE_WIDTH = 5.0f
        private const val NUM_COLORS = 10
        private val COLORS =
            arrayOf(
                intArrayOf(Color.BLACK, Color.WHITE),
                intArrayOf(Color.WHITE, Color.MAGENTA),
                intArrayOf(Color.BLACK, Color.LTGRAY),
                intArrayOf(Color.WHITE, Color.RED),
                intArrayOf(Color.WHITE, Color.BLUE),
                intArrayOf(Color.WHITE, Color.DKGRAY),
                intArrayOf(Color.BLACK, Color.CYAN),
                intArrayOf(Color.BLACK, Color.YELLOW),
                intArrayOf(Color.WHITE, Color.BLACK),
                intArrayOf(Color.BLACK, Color.GREEN)
            )
    }

}
