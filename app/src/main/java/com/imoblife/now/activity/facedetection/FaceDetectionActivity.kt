package com.imoblife.now.activity.facedetection

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.mlkit.common.MlKitException
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.MyApplication
import com.imoblife.now.R
import com.imoblife.now.constant.ConsFilePath
import com.imoblife.now.databinding.LayoutAcFaceDetectionBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.repository.OssRepository
import com.imoblife.now.util.BitmapUtil
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.util.interval.Interval
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.libpag.PAGFile
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - activity
 */
class FaceDetectionActivity : BaseVMActivity<FaceDetectionViewModel>() {

    companion object {

        val PATH_ORIGIN_BITMAP = "${ConsFilePath.getExternalFilePath()}face_origin.jpg"
        val PATH_SYNTHESIS_BITMAP = "${ConsFilePath.getExternalFilePath()}face_synthesis.jpg"

        fun startActivity(context: Context) {
            Intent(context, FaceDetectionActivity::class.java).let {
                context.startActivity(it)
            }
        }

    }

    private lateinit var mBind: LayoutAcFaceDetectionBinding

    private var mPreviewView: PreviewView? = null
    private var mGraphicOverlay: GraphicOverlay? = null
    private var mCameraProvider: ProcessCameraProvider? = null
    private var mCamera: Camera? = null
    private var mPreviewUseCase: Preview? = null
    private var mAnalysisUseCase: ImageAnalysis? = null
    private var mImageProcessor: VisionImageProcessor? = null
    private var mNeedUpdateGraphicOverlayImageSourceInfo = false
    private var mLensFacing = CameraSelector.LENS_FACING_FRONT
    private var mCameraSelector: CameraSelector? = null

    private var mFaceInterval: Interval? = null
    private var mProgressInterval: Interval? = null
    private var mStartInterval: Boolean = false
    private var mHasFace: Boolean = false
    private var mStartSaveBitmap: Boolean = false
    private var mOriginBitmap: Bitmap? = null
    private var mSynthesisBitmap: Bitmap? = null
    private var mProcessedBitmap: Bitmap? = null

    private val mOssRepository by lazy(LazyThreadSafetyMode.NONE) { OssRepository() }

    private var mOriginBitmapUrl = ""
    private var mSynthesisBitmapUrl = ""

    private var mReportId = ""
    private var mGetBitmapState = false

    private val mCameraXViewModel: CameraXViewModel by viewModels { CameraXViewModelFactory(application) }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentBar()
            .statusBarDarkFont(true)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_face_detection

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[FaceDetectionViewModel::class.java]

    override fun initView() {
        mCameraSelector = CameraSelector.Builder().requireLensFacing(mLensFacing).build()
        mBind = mBinding as LayoutAcFaceDetectionBinding
        mBind.apply {
            mPreviewView = findViewById(R.id.previewView)
            if (mPreviewView == null) {
                Log.d("yunyang", "previewView is null")
            }
            mGraphicOverlay = findViewById(R.id.graphicOverlay)
            if (mGraphicOverlay == null) {
                Log.d("yunyang", "graphicOverlay is null")
            }
            pagImg.apply {
                composition = PAGFile.Load(assets, "pag_face_detection.pag")
                setRepeatCount(-1)
                play()
            }

            imgBack.onDebounceClickListener { onBackPressed() }
            imgReset.onDebounceClickListener { reset() }
        }
    }

    override fun initData() {
        progressAnim()
    }

    /**
     * 进度动画
     */
    @SuppressLint("SetTextI18n")
    private fun progressAnim() {
        val seconds = 4 * 1000
        mProgressInterval = Interval(seconds.toLong(), 1, TimeUnit.MILLISECONDS, 1).life(this)
        mProgressInterval?.subscribe { value ->
            mBind.apply {
                jpvProgress
                    .setProgress(value.toFloat())
                    .setMaxProgress(seconds)
                    .resetValue()
                tvProgress.text = "${((value.toFloat() / seconds) * 100).toInt()}%"
                tvTip.text = when (value) {
                    in 0L until 800L -> "正在为你检测你的心情"
                    in 800L until 1600L -> "正在为你检测你的压力值"
                    in 1600L until 2400L -> "正在为你检测你的焦虑值"
                    in 2400L until 3200L -> "正在为你检测你的疲惫指数"
                    in 3200L until 4000L -> "正在为你检测你的幸福指数"
                    else -> {
                        imgComplete.visibility = View.VISIBLE
                        "正在为你检测你的幸福指数"
                    }
                }
            }
        }?.finish {
            if (!TextUtils.isEmpty(mReportId)) {
                lifecycleScope.launch {
                    mBind.tvTip.text = "正在为你生成情绪报告"
                    delay(500)
                    FaceDetectionReportActivity.startActivity(this@FaceDetectionActivity, mReportId)
                    finish()
                }
            } else {
                mBind.groupImgLayer.visibility = View.VISIBLE
            }
        }
    }

    override fun startObserve() {
        mCameraXViewModel.processCameraProvider.observe(this) { provider: ProcessCameraProvider? ->
            mCameraProvider = provider
            bindAllCameraUseCases()
        }
        mViewModel.reportDeepFace.observe(this) { uiStatus ->
            mGetBitmapState = true
            if (uiStatus.isSuccess) {
                uiStatus.successData?.record_id?.let { reportId ->
                    if (!TextUtils.isEmpty(reportId)) {
                        mBind.imgComplete.visibility = View.VISIBLE
                        mReportId = reportId
                    } else {
                        mReportId = ""
                    }
                } ?: run { mReportId = "" }
            } else {
                mReportId = ""
            }
        }
    }

    private fun reset() {
        mBind.groupImgLayer.visibility = View.GONE
        mGetBitmapState = false
        mProgressInterval?.cancel()
        mBind.apply {
            imgComplete.visibility = View.GONE
            tvTip.text = ""
            tvProgress.text = "0%"
            jpvProgress.setProgress(0F).resetValue()
        }
        mStartInterval = false
        mStartSaveBitmap = false
        mOriginBitmap = null
        mSynthesisBitmap = null
    }

    public override fun onResume() {
        super.onResume()
        bindAllCameraUseCases()
    }

    override fun onPause() {
        super.onPause()
        mImageProcessor?.run { this.stop() }
    }

    public override fun onDestroy() {
        super.onDestroy()
        mImageProcessor?.run { this.stop() }
    }

    private fun bindAllCameraUseCases() {
        if (mCameraProvider != null) {
            // 根据CameraX API的要求，在尝试重新绑定所有用例之前，请解除它们的绑定。
            mCameraProvider!!.unbindAll()
            bindPreviewUseCase()
            bindAnalysisUseCase()
            mFaceInterval = Interval(1, 1, TimeUnit.SECONDS, 3).life(this)
            mFaceInterval?.subscribe {
                if (!mHasFace) {
                    mFaceInterval?.cancel()
                    mStartInterval = false
                }
            }?.finish { mStartSaveBitmap = true }
        }
    }

    private fun bindPreviewUseCase() {
        if (mCameraProvider == null) {
            return
        }
        if (mPreviewUseCase != null) {
            mCameraProvider!!.unbind(mPreviewUseCase)
        }

        val builder = Preview.Builder()
        mPreviewUseCase = builder.build()
        mPreviewUseCase!!.setSurfaceProvider(mPreviewView!!.getSurfaceProvider())
        mCamera = mCameraProvider!!.bindToLifecycle(this, mCameraSelector!!, mPreviewUseCase)
    }

    private fun bindAnalysisUseCase() {
        if (mCameraProvider == null) {
            return
        }
        if (mAnalysisUseCase != null) {
            mCameraProvider!!.unbind(mAnalysisUseCase)
        }
        if (mImageProcessor != null) {
            mImageProcessor!!.stop()
        }
        mImageProcessor =
            try {
                val faceDetectorOptions = FaceDetectorOptions
                    .Builder()
                    .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
                    .setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
                    .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE)
                    .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
                    .setMinFaceSize(0.1F)
                    .build()
                FaceDetectorProcessor(
                    this,
                    faceDetectorOptions
                ) { bool, originBitmap, facePoints, noseBridgePoints, noseBottomPoints ->
                    if (!mGetBitmapState) {
                        mHasFace = bool
                        if (bool && noseBridgePoints.isNotEmpty() && noseBottomPoints.isNotEmpty()) {
                            mGraphicOverlay?.apply {
                                post {
                                    val widthDpValue = DisplayUtil.px2dip(this@FaceDetectionActivity, width.toFloat())
                                    val heightDpValue = DisplayUtil.px2dip(this@FaceDetectionActivity, height.toFloat())
                                    val xMinRange = widthDpValue * 2 / 5
                                    val xMaxRange = widthDpValue * 4 / 5
                                    val yMinRange = heightDpValue * 1 / 5
                                    val yMaxRange = heightDpValue * 3 / 5
                                    val xList = mutableListOf<Int>()
                                    val yList = mutableListOf<Int>()
                                    noseBridgePoints.forEach { entity ->
                                        xList.add(entity.x.toInt())
                                        yList.add(entity.y.toInt())
                                    }
                                    noseBottomPoints.forEach { entity ->
                                        xList.add(entity.x.toInt())
                                        yList.add(entity.y.toInt())
                                    }
                                    val xBool = xList.all { it in xMinRange..xMaxRange }
                                    val yBool = yList.all { it in yMinRange..yMaxRange }
                                    if (xBool && yBool) {
                                        if (!mStartInterval) {
                                            mStartInterval = true
                                            mFaceInterval?.start()
                                            mProgressInterval?.start()
                                        }
                                        mBind.tvContent.text = "保持正脸不要晃动"
                                        if (originBitmap != null && mStartSaveBitmap) {
                                            mOriginBitmap = originBitmap
                                            BitmapUtil.saveCroppedImage(mOriginBitmap, PATH_ORIGIN_BITMAP)
                                            lifecycleScope.launch {
                                                delay(500L)
                                                mOssRepository.getOssInfo(File(PATH_ORIGIN_BITMAP), ossUploadFileListener = object :
                                                    OssRepository.OSSUploadFileListener {
                                                    override fun uploadFile(isSuccess: Boolean, fileName: String) {
                                                        mOriginBitmapUrl = fileName
                                                        if (!TextUtils.isEmpty(mOriginBitmapUrl) && !TextUtils.isEmpty(mSynthesisBitmapUrl)) {
                                                            mViewModel.reportDeepFace(mOriginBitmapUrl, mSynthesisBitmapUrl)
                                                        }
                                                    }
                                                })
                                            }
                                        }
                                        if (mGraphicOverlay != null && mStartSaveBitmap) {
                                            val bitmap = BitmapUtil.loadBitmapFromView(mGraphicOverlay!!)
                                            mSynthesisBitmap = bitmap
                                            MyApplication.mFaceDetectionSynthesisBitmap = bitmap
                                            BitmapUtil.saveCroppedImage(mSynthesisBitmap, PATH_SYNTHESIS_BITMAP)
                                            lifecycleScope.launch {
                                                delay(500L)
                                                mOssRepository.getOssInfo(File(PATH_SYNTHESIS_BITMAP), ossUploadFileListener = object :
                                                    OssRepository.OSSUploadFileListener {
                                                    override fun uploadFile(isSuccess: Boolean, fileName: String) {
                                                        mSynthesisBitmapUrl = fileName
                                                        if (!TextUtils.isEmpty(mOriginBitmapUrl) && !TextUtils.isEmpty(mSynthesisBitmapUrl)) {
                                                            mViewModel.reportDeepFace(mOriginBitmapUrl, mSynthesisBitmapUrl)
                                                        }
                                                    }
                                                })
                                            }
                                        }
                                        if (mOriginBitmap != null && mSynthesisBitmap != null && mStartSaveBitmap) {
                                            mStartSaveBitmap = false
                                        }
                                    } else {
                                        reset()
                                        mBind.tvContent.text = "请你正对摄像头，以便精准获取你的情绪状态"
                                    }
                                }
                            }
                        } else {
                            reset()
                            mBind.tvContent.text = "未检测到人脸"
                        }
                    }
                }
            } catch (e: Exception) {
                Toast.makeText(
                    applicationContext,
                    "无法创建图像处理器 - " + e.localizedMessage,
                    Toast.LENGTH_LONG,
                )
                    .show()
                return
            }

        val builder = ImageAnalysis.Builder()
        builder.setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
        mAnalysisUseCase = builder.build()

        mNeedUpdateGraphicOverlayImageSourceInfo = true

        mAnalysisUseCase?.setAnalyzer(
            // imageProcessor.processImageProxy将使用另一个线程来运行下面的检测，因此我们可以在主线程上运行分析器本身。
            ContextCompat.getMainExecutor(this),
            ImageAnalysis.Analyzer { imageProxy: ImageProxy ->
                if (mNeedUpdateGraphicOverlayImageSourceInfo) {
                    val isImageFlipped = mLensFacing == CameraSelector.LENS_FACING_FRONT
                    val rotationDegrees = imageProxy.imageInfo.rotationDegrees
                    if (rotationDegrees == 0 || rotationDegrees == 180) {
                        mGraphicOverlay!!.setImageSourceInfo(
                            imageProxy.width,
                            imageProxy.height,
                            isImageFlipped
                        )
                    } else {
                        mGraphicOverlay!!.setImageSourceInfo(
                            imageProxy.height,
                            imageProxy.width,
                            isImageFlipped
                        )
                    }
                    mNeedUpdateGraphicOverlayImageSourceInfo = false
                }
                try {
                    mImageProcessor!!.processImageProxy(imageProxy, mGraphicOverlay)
                } catch (e: MlKitException) {
                    ToastUtils.showShortToast(e.localizedMessage)
                }
            },
        )
        mCameraProvider!!.bindToLifecycle(this, mCameraSelector!!, mAnalysisUseCase)
    }

}