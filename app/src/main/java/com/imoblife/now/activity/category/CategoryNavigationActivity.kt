package com.imoblife.now.activity.category

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.material.appbar.AppBarLayout
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationEntity
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.LayoutAcCategoryNavigationBinding
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.EmptyUtils
import com.youth.banner.Banner
import com.youth.banner.listener.OnPageChangeListener
import jp.wasabeef.glide.transformations.BlurTransformation
import kotlin.math.abs

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/6
 * 描   述：首页 - 金刚区 - 分类导航
 */
class CategoryNavigationActivity : BaseVMActivity<CategoryNavigationViewModel>() {

    companion object {

        fun startActivity(
            context: Context,
            TypeId: Int,
            title: String,
            pageId: Int,
            categoryId: Int
        ) {
            val intent = Intent(context, CategoryNavigationActivity::class.java)
            intent.apply {
                putExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_ID, TypeId)
                putExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_TITLE, title)
                putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId)
                putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId)
                context.startActivity(this)
            }
        }

    }

    private lateinit var mBind: LayoutAcCategoryNavigationBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private lateinit var mTopBanner: Banner<CategoryNavigationItemEntity, BannerCategoryAdapter>

    private var mTypeId = 0

    private var mTitle: String? = ""

    // 神策数据 - 当前页面id
    private var mPageId = 0

    // 神策数据 - 当前页面 - 分类id
    private var mCategoryId = 0

    private val mCategoryNavigationAdapter by lazy(LazyThreadSafetyMode.NONE) { CategoryNavigationAdapter() }

    private val mBannerList by lazy(LazyThreadSafetyMode.NONE) { mutableListOf<String>() }

    override fun getLayoutResId() = R.layout.layout_ac_category_navigation

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_ID)) {
                mTypeId = intent.getIntExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_ID, 0)
            }
            if (hasExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_TITLE)) {
                mTitle = intent.getStringExtra(ConsIntent.BUNDLE_CATEGORY_NAVIGATION_TYPE_TITLE)
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
                mPageId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0)
            }
            if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
                mCategoryId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0)
            }
        }
    }

    override fun initImmersionBar() {}

    override fun initVM() = ViewModelProvider(this).get(CategoryNavigationViewModel::class.java)

    override fun initView() {
        mLoadingHelper = LoadingHelper(this)
        mBind = mBinding as LayoutAcCategoryNavigationBinding
        mBind.apply {
            mTopBanner = findViewById(R.id.banner)
            ImmersionBar
                .with(this@CategoryNavigationActivity)
                .titleBar(toolbar)
                .statusBarDarkFont(true)
                .init()
            toolbar.title = null
            toolbarTitle.text = mTitle
            toolbar.setNavigationOnClickListener { onBackPressed() }
            recyclerView.adapter = mCategoryNavigationAdapter
            appBarLayout.addOnOffsetChangedListener(object : AppBarStatusChangeListener() {
                override fun onStateChanged(
                    appBarLayout: AppBarLayout?,
                    state: AppBarStatus,
                    verticalOffset: Int
                ) {
                    when (state) {
                        AppBarStatusCollapsed -> {
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@CategoryNavigationActivity,
                                R.color.white
                            )
                            toolbar.alpha = 1F
                            toolbarTitle.alpha = 1F
                            toolbar.setNavigationIcon(R.mipmap.icon_back)
                            toolbarTitle.setTextColor(
                                ContextCompat.getColor(
                                    this@CategoryNavigationActivity,
                                    R.color.color_black
                                )
                            )
                        }

                        AppBarStatusExpanded -> {
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@CategoryNavigationActivity,
                                R.color.color_transparent
                            )
                            toolbar.alpha = 1F
                            toolbarTitle.alpha = 1F
                            toolbar.setNavigationIcon(R.mipmap.icon_back_white)
                            toolbarTitle.setTextColor(
                                ContextCompat.getColor(
                                    this@CategoryNavigationActivity,
                                    R.color.color_white
                                )
                            )
                        }

                        AppBarStatusIdle -> {
                            collapsingToolbarLayout.contentScrim = ContextCompat.getDrawable(
                                this@CategoryNavigationActivity,
                                R.color.color_transparent
                            )
                            val offset = abs(verticalOffset)
                            val max = appBarLayout?.totalScrollRange
                            max?.let {
                                val limit = (max / 2).toFloat()
                                if (offset <= limit) {
                                    toolbar.setNavigationIcon(R.mipmap.icon_back_white)
                                    toolbarTitle.setTextColor(
                                        ContextCompat.getColor(
                                            this@CategoryNavigationActivity,
                                            R.color.color_white
                                        )
                                    )
                                    toolbar.alpha = 1F - offset / limit
                                    toolbarTitle.alpha = 1F - offset / limit
                                } else {
                                    val offsetUp = offset - limit
                                    toolbar.setNavigationIcon(R.mipmap.icon_back)
                                    toolbarTitle.setTextColor(
                                        ContextCompat.getColor(
                                            this@CategoryNavigationActivity,
                                            R.color.color_black
                                        )
                                    )
                                    toolbar.alpha = offsetUp / limit
                                    toolbarTitle.alpha = offsetUp / limit
                                }
                            }
                        }
                    }
                }
            })
            smartRefreshLayout.setOnRefreshListener {
                mViewModel.getCategoryData(mTypeId, mPageId, mCategoryId, isReadCache = false)
            }
            // 改善睡眠 => 显示睡眠监测底部入口
            if (mTypeId == 16) {
                bottomSleepMonitoringView.visibility = View.VISIBLE
//                GuideUtils.startSleepMonitorGuide(this@CategoryNavigationActivity,bottomSleepMonitoringView)
            } else {
                bottomSleepMonitoringView.visibility = View.GONE
            }
        }
        mTopBanner.apply {
            isAutoLoop(true)
            addBannerLifecycleObserver(this@CategoryNavigationActivity)
            setIndicator(mBind.bannerRectangleIndicator, false)
            setAdapter(BannerCategoryAdapter(mutableListOf()), true)
            addOnPageChangeListener(object : OnPageChangeListener {
                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                }

                override fun onPageSelected(position: Int) {
                    if (EmptyUtils.isNotEmpty(mBannerList)) {
                        Glide
                            .with(this@CategoryNavigationActivity)
                            .asBitmap()
                            .load(mBannerList[position])
                            .transform(BlurTransformation(20))
                            .into(object : SimpleTarget<Bitmap>() {
                                override fun onResourceReady(
                                    resource: Bitmap,
                                    transition: Transition<in Bitmap>?
                                ) {
                                    mBind.imgBg.setImageBitmap(resource)
                                }
                            })
                    }
                }

                override fun onPageScrollStateChanged(state: Int) {
                }
            })
            setOnBannerListener { entity, position ->
                entity.pageRoute(
                    this@CategoryNavigationActivity,
                    position
                )
            }
        }

    }

    override fun initData() {
        mLoadingHelper.apply {
            mLoadingHelper.showLoadingView()
            setOnReloadListener {
                mLoadingHelper.showLoadingView()
                mViewModel.getCategoryData(mTypeId, mPageId, mCategoryId)
            }
        }
        mViewModel.getCategoryData(mTypeId, mPageId, mCategoryId)
    }

    override fun startObserve() {
        mViewModel.apply {
            listData.distinctUntilChanged().observe(this@CategoryNavigationActivity) {
                if (it.isSuccess) {
                    it.successData?.let { entity ->
                        mLoadingHelper.showContentView()
                        if (EmptyUtils.isNotEmpty(entity.recommend_list)) {
                            if (EmptyUtils.isNotEmpty(entity.recommend_list.listItemsEntity)) {
                                val listItemsEntity = entity.recommend_list.listItemsEntity
                                mBannerList.clear()
                                listItemsEntity.forEach { item ->
                                    mBannerList.add(item.banner)
                                }
                                mTopBanner.setDatas(listItemsEntity)
                                mBannerList.firstOrNull()?.let { imgUrl ->
                                    Glide
                                        .with(this@CategoryNavigationActivity)
                                        .asBitmap()
                                        .load(imgUrl)
                                        .transform(BlurTransformation(20))
                                        .into(object : SimpleTarget<Bitmap>() {
                                            override fun onResourceReady(
                                                resource: Bitmap,
                                                transition: Transition<in Bitmap>?
                                            ) {
                                                mBind.imgBg.setImageBitmap(resource)
                                            }
                                        })
                                }
                            }
                        }
                        if (EmptyUtils.isNotEmpty(entity.category_list)) {
                            if (EmptyUtils.isNotEmpty(entity.ad_list)) {
                                val list =
                                    mutableListOf<CategoryNavigationEntity.CategoryListEntity>()
                                val item = CategoryNavigationEntity.CategoryListEntity()
                                item.tag = ConsCommon.TYPE_RECOMMEND_AD
                                item.ad = entity.ad_list
                                item.title = ""
                                item.more = null
                                list.add(item)
                                list.addAll(entity.category_list)
                                mCategoryNavigationAdapter.setNewData(list)
                            } else {
                                mCategoryNavigationAdapter.setNewData(entity.category_list)
                            }
                        }
                    } ?: mLoadingHelper.showEmptyView()
                } else mLoadingHelper.showErrorView()
            }
            refreshState.observe(this@CategoryNavigationActivity) {
                mBind.smartRefreshLayout.finishRefresh()
            }
        }
    }

}