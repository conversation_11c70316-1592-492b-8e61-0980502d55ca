package com.imoblife.now.activity.breath

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.os.PowerManager
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.animation.Animation
import android.view.animation.ScaleAnimation
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.databinding.DataBindingUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.commlibrary.mvp.CreatePresenter
import com.imoblife.now.R
import com.imoblife.now.activity.base.MvpBaseActivity
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.ArrayWheelAdapter
import com.imoblife.now.bean.BreathBgEntity
import com.imoblife.now.bean.BreathModeEntity
import com.imoblife.now.bean.BreathUploadBean
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.ActivityBreathBinding
import com.imoblife.now.enums.PlayType
import com.imoblife.now.enums.UserAction
import com.imoblife.now.model.UserActionLogMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvp_contract.BreathUploadContract
import com.imoblife.now.mvp_presenter.BreathUploadPresenter
import com.imoblife.now.player.BGPlayerUtils
import com.imoblife.now.util.DeviceUtil
import com.imoblife.now.util.SpUtil
import com.imoblife.now.util.breath.breathPracticeTime
import com.imoblife.now.util.breath.startVibrate
import com.imoblife.now.util.countdowntimer.CountDownTimerSupport
import com.imoblife.now.util.countdowntimer.OnCountDownTimerListener
import com.now.audioplayer.SongInfo
import jp.wasabeef.glide.transformations.BlurTransformation

/**
 * 呼吸Activity
 *
 * add - 2022/08/15 - yunyang - 增加呼吸背景 & 呼吸模式
 */
@CreatePresenter(presenter = [BreathUploadPresenter::class])
class BreathActivity : MvpBaseActivity<BreathUploadPresenter>(),
    BreathUploadContract.IBreathUploadView, View.OnClickListener {

    companion object {

        @JvmStatic
        fun openBreathActivity(mContext: Context) {
            Intent(mContext, BreathActivity::class.java).apply { mContext.startActivity(this) }
        }

    }

    //呼吸是否已经开始
    private var isBreathStart = false

    // 准备动画
    private var readyScale: ScaleAnimation? = null

    // 吸气动画
    private var breathInScale: ScaleAnimation? = null

    // 屏气动画
    private var breathlessScale: ScaleAnimation? = null

    // 第二次屏气动画
    private var breathlessSecondScale: ScaleAnimation? = null

    // 呼吸动画
    private var breathOutScale: ScaleAnimation? = null

    private var startTime = 0L
    private var endTime = 0L
    private var mCountDownMilliSeconds = 0L

    private lateinit var mBind: ActivityBreathBinding

    // 呼吸背景
    private val mBreathBgData = mutableListOf<BreathBgEntity>()

    // 呼吸模式
    private val mBreathModeData = mutableListOf<BreathModeEntity>()

    // 呼吸类型 => 吸呼｜吸屏呼屏｜吸屏呼  1｜2｜3
    private var mBreathType = 1

    // 音频播放
    private var mBreathMusicUrl = ""

    // 呼吸背景选中的url
    private var mSelectImgUrl = ""

    // 数据上报 => songInfo
    private var mSongInfo: SongInfo? = null

    // 总的时间
    private var mTotalPlayTime = 0

    // 开始倒计时 - 3秒
    private val mStartCountDownTimerSupport by lazy(LazyThreadSafetyMode.NONE) { CountDownTimerSupport() }

    // 呼吸练习倒计时
    private val mCountDownTimerSupport by lazy(LazyThreadSafetyMode.NONE) { CountDownTimerSupport() }

    override fun initDataBinding() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_breath)
        mBind.lifecycleOwner = this@BreathActivity
    }

    override fun initView() {
        mBind.apply {
            ivSelectTimeBg.onPlay()
            toolbar.title = ""
            toolbarCenterTitleTv.text = getString(R.string.breath_title)
            wheelView.apply {
                setTextSize(30F)
                setLineSpacingMultiplier(1.5F)
                setDividerColor(Color.TRANSPARENT)
                setTextColorOut(Color.WHITE)
                setTextColorCenter(Color.WHITE)
                currentItem = breathPracticeTime.indexOf("1")
            }
            mSelectImgUrl = SpUtil.getInstance().getStringValue(
                "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_BG_SELECT_IMG}",
                ""
            )
            updateBgImg(true)
            ivClose.setOnClickListener(this@BreathActivity)
            flStartBreath.setOnClickListener(this@BreathActivity)
            tvEndBtn.setOnClickListener(this@BreathActivity)
            tvBreathMode.setOnClickListener(this@BreathActivity)
            tvBreathBgSelect.setOnClickListener(this@BreathActivity)
        }
    }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .titleBar(R.id.toolbar)
            .init()
    }

    override fun initData() {
        if (UserMgr.getInstance().isLogin) {
            presenter.apply {
                breathBg()
                breathMode()
            }
        }
        mBind.apply {
            wheelView.adapter = ArrayWheelAdapter(breathPracticeTime)
            wheelView.currentItem = 0
        }
        lifecycle.apply {
            addObserver(mStartCountDownTimerSupport)
            addObserver(mCountDownTimerSupport)
        }
        // 开始倒计时 - 3秒
        mStartCountDownTimerSupport.setOnCountDownTimerListener(object : OnCountDownTimerListener {
            override fun onTick(number: Long) {
                mBind.tvCountDown.apply {
                    visibility = View.VISIBLE
                    text = (number / 1000).toString()
                }
            }

            override fun onFinish() {
                mBind.apply {
                    tvCountDown.visibility = View.GONE
                    tvBreathHint.visibility = View.VISIBLE
                }
                startReadyAnim()
                startBreathCountDown()
            }

            override fun onCancel() {}
        })
        // 呼吸练习倒计时
        mCountDownTimerSupport.setOnCountDownTimerListener(object : OnCountDownTimerListener {
            override fun onTick(number: Long) {
                // 30秒本地保存一次播放进度状态 => 为上报日志使用
                val time = number / 1000L
                mTotalPlayTime = (mCountDownMilliSeconds - time).toInt()
                if (time.toInt() % 30 == 0) {
                    UserActionLogMgr
                        .getInstance()
                        .saveCourseLog(
                            UserAction.Practice.value,
                            mSongInfo,
                            mTotalPlayTime
                        )
                }
            }

            override fun onFinish() {
                endBreath("yes")
                // 学习计划 - Task
                UserMgr.getInstance().queryStudyPlanTime()
            }

            override fun onCancel() {}
        })
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_close -> {
                closeActivity()
            }
            // 开始呼吸
            R.id.flStartBreath -> {
                // 登陆状态
                if (UserMgr.getInstance().isLogin) {
                    startBreathPractice()
                } else {
                    LoginCenter.getInstance().loginControl(this, LoginCenter.LoginStyleDialog)
                }
            }
            // 呼吸模式
            R.id.tvBreathMode -> {
                if (UserMgr.getInstance().isLogin) {
                    BottomBreathPatternDialog(this, mBreathModeData).show()
                } else {
                    LoginCenter.getInstance().loginControl(this, LoginCenter.LoginStyleDialog)
                }
            }
            // 背景选择
            R.id.tvBreathBgSelect -> {
                if (UserMgr.getInstance().isLogin) {
                    BottomBreathBgSelectDialog(this, mBreathBgData) {
                        mSelectImgUrl = it
                        SpUtil.getInstance().saveStringToSp(
                            "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_BG_SELECT_IMG}",
                            mSelectImgUrl
                        )
                        updateBgImg(true)
                    }.show()
                } else {
                    LoginCenter.getInstance().loginControl(this, LoginCenter.LoginStyleDialog)
                }
            }
            // 结束呼吸
            R.id.tvEndBtn -> startBreathPractice()
            else -> {}
        }
    }

    /**
     * 开始｜结束呼吸练习
     */
    private fun startBreathPractice() {
        if (!isBreathStart) {
            mBind.ivSelectTimeBg.setBezierCirclePaintShader(true)
            mBind.toolbar.visibility = View.GONE
            updateBgImg(false)
            startReadyCountDown()
            mBreathModeData.forEach {
                if (it.isSelect) {
                    initAnim(
                        it.inhale_seconds * 1000L,
                        it.hold_breath_seconds * 1000L,
                        it.breathe_seconds * 1000L,
                        it.repeat_hold_breath_seconds * 1000L
                    )
                    mBreathMusicUrl = it.audio_url
                    mBreathType = it.breath_type
                    return
                }
            }
        } else {
            endBreath("no")
            showToast()
        }
    }

    /**
     * 更新BgImg
     *
     * @param bool 开始呼吸 => 取消bg蒙层 | 结束呼吸 => 带上bg蒙层
     */
    private fun updateBgImg(bool: Boolean) {
        Glide
            .with(this)
            .asBitmap()
            .load(mSelectImgUrl.ifEmpty { R.mipmap.breath_bg })
            .error(R.mipmap.breath_bg)
            .set<WebpFrameCacheStrategy>(
                WebpFrameLoader.FRAME_CACHE_STRATEGY,
                WebpFrameCacheStrategy.AUTO
            )
            .into(mBind.ivBg)
        if (bool) {
            Glide
                .with(this)
                .asBitmap()
                .load(mSelectImgUrl.ifEmpty { R.mipmap.breath_bg })
                .error(R.mipmap.breath_bg)
                .transform(BlurTransformation(20))
                .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap>?
                    ) {
                        mBind.imgBg.setImageBitmap(resource)
                    }
                })
        } else {
            Glide
                .with(this)
                .asBitmap()
                .load(mSelectImgUrl)
                .error(R.mipmap.breath_bg)
                .set<WebpFrameCacheStrategy>(
                    WebpFrameLoader.FRAME_CACHE_STRATEGY,
                    WebpFrameCacheStrategy.AUTO
                )
                .into(mBind.imgBg)
        }
    }

    override fun onBackPressed() {
        closeActivity()
    }

    override fun uploadSuccess(data: BreathUploadBean?) {
        if (data != null && data.all_number > 0) {
            Intent(
                this@BreathActivity,
                ShareBreathActivity::class.java
            ).apply {
                this.putExtra("all_number", data.all_number)
                startActivity(this)
                overridePendingTransition(
                    android.R.anim.fade_in,
                    android.R.anim.fade_out
                )
            }
        }
    }

    /**
     * 呼吸背景 - 成功
     */
    override fun getBreathBgSuccess(list: List<BreathBgEntity>?) {
        if (!list.isNullOrEmpty()) {
            mBreathBgData.clear()
            val id = SpUtil.getInstance().getIntValue(
                "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_BG_SELECT_STATUS}",
                1
            )
            list.forEach {
                it.isSelect = it.id == id
            }
            mBreathBgData.addAll(list)
        }
    }

    /**
     * 呼吸模式 - 成功
     */
    override fun getBreathModeSuccess(list: List<BreathModeEntity>?) {
        if (!list.isNullOrEmpty()) {
            mBreathModeData.clear()
            val id = SpUtil.getInstance().getIntValue(
                "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_MODE_SELECT_STATUS}",
                1
            )
            list.forEach {
                it.isSelect = it.id == id
            }
            mBreathModeData.addAll(list)
        }
    }

    override fun setContentViewId() = 0

    /**
     * 初始化呼吸动画
     * @param breathInDuration 吸气动画时长
     * @param breathlessDuration 屏气动画时长
     * @param breathOutDuration 呼吸动画时长
     * @param breathlessSecondsDuration 第二次屏气动画时长
     * @param readyDuration 准备动画时长
     */
    private fun initAnim(
        breathInDuration: Long,
        breathlessDuration: Long,
        breathOutDuration: Long,
        breathlessSecondsDuration: Long,
        readyDuration: Long = 3000L
    ) {
        // 准备动画
        readyScale = ScaleAnimation(
            1.0F, 0.5F, 1.0f, 0.5F,
            Animation.RELATIVE_TO_SELF,
            0.5F, Animation.RELATIVE_TO_SELF, 0.5F
        )
        readyScale?.duration = readyDuration
        // 停留在最后一帧
        readyScale?.fillAfter = true
        readyScale?.fillBefore = true
        readyScale?.setAnimationListener(readyAnimatorListener)
        // 吸气动画
        breathInScale = ScaleAnimation(
            0.5F, 1F, 0.5F, 1F,
            Animation.RELATIVE_TO_SELF,
            0.5F, Animation.RELATIVE_TO_SELF, 0.5F
        )
        breathInScale?.duration = breathInDuration
        breathInScale?.fillAfter = true
        breathInScale?.fillBefore = true
        breathInScale?.setAnimationListener(breathInAnimatorListener)
        // 屏气动画
        breathlessScale = ScaleAnimation(
            1F, 1F, 1F, 1F,
            Animation.RELATIVE_TO_SELF,
            0.5F, Animation.RELATIVE_TO_SELF, 0.5F
        )
        breathlessScale?.duration = breathlessDuration
        breathlessScale?.fillAfter = true
        breathlessScale?.fillBefore = true
        breathlessScale?.setAnimationListener(breathlessAnimatorListener)
        // 呼气动画
        breathOutScale = ScaleAnimation(
            1F, 0.5F, 1F, 0.5F,
            Animation.RELATIVE_TO_SELF,
            0.5F, Animation.RELATIVE_TO_SELF, 0.5F
        )
        breathOutScale?.duration = breathOutDuration
        breathOutScale?.fillAfter = true
        breathOutScale?.fillBefore = true
        breathOutScale?.setAnimationListener(breathOutAnimatorListener)
        // 第二次屏气动画
        breathlessSecondScale = ScaleAnimation(
            0.5F, 0.5F, 0.5F, 0.5F,
            Animation.RELATIVE_TO_SELF,
            0.5F, Animation.RELATIVE_TO_SELF, 0.5F
        ).also {
            it.duration = breathlessSecondsDuration
            it.fillAfter = true
            it.fillBefore = true
            it.setAnimationListener(breathlessSecondAnimatorListener)
        }
    }

    /**
     * 开始呼吸倒计时
     */
    private fun startBreathCountDown() {
        uploadPracticeLog(UserAction.StartPlay.value)
        val currentItem = mBind.wheelView.currentItem
        mCountDownMilliSeconds = breathPracticeTime[currentItem].toInt() * 60L
        mCountDownTimerSupport.setMillisInFuture(mCountDownMilliSeconds * 1000L)
        mCountDownTimerSupport.start()
    }

    /**
     * 开始准备倒计时
     */
    private fun startReadyCountDown(millisInFuture: Long = 3000L) {
        startBreath()
        mStartCountDownTimerSupport.setMillisInFuture(millisInFuture)
        mStartCountDownTimerSupport.start()
        mSongInfo = SongInfo(
            songId = "0",
            albumId = SpUtil.getInstance().getIntValue(
                "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_MODE_SELECT_STATUS}",
                1
            ).toString(),
            songType = ConsCommon.SONG_TYPE_BREATHING_PRACTICE,
            type = ConsCommon.SONG_TYPE_BREATHING_PRACTICE_BREATH,
            onlyId = DeviceUtil.getCurrentOnlyCode()
        )
    }

    /**
     * 上报练习记录
     *
     * @param action 行为
     * @param totalPlayTime 总的播放时长
     * @param isCompleted 是否完成
     */
    private fun uploadPracticeLog(
        action: String,
        totalPlayTime: Int = 0,
        isCompleted: Boolean = false
    ) {
        if (totalPlayTime == 0 && UserAction.Practice.value == action) return
        mSongInfo?.let {
            UserActionLogMgr
                .getInstance()
                .uploadCourseLog(
                    PlayType.BREATH.name,
                    action,
                    it,
                    totalPlayTime,
                    isCompleted,
                    isCompleted
                )
        }
    }

    /**
     * 开始呼吸
     */
    private fun startBreath() {
        mBind.apply {
            startTime = System.currentTimeMillis()
            groupBreathEnd.visibility = View.GONE
            groupBreathMode.visibility = View.GONE
            tvEndBtn.visibility = View.VISIBLE
        }
        isBreathStart = true
    }

    /**
     * 结束呼吸
     */
    private fun endBreath(isCompleted: String) {
        mBind.ivSelectTimeBg.setBezierCirclePaintShader()
        mBind.apply {
            toolbar.visibility = View.VISIBLE
            isBreathStart = false
            endTime = System.currentTimeMillis()
            tvBreathHint.visibility = View.GONE
            tvCountDown.visibility = View.GONE
            groupBreathEnd.visibility = View.VISIBLE
            tvEndBtn.visibility = View.GONE
            groupBreathMode.visibility = View.VISIBLE
        }
        setBreathHint(R.string.breath_ready)
        releaseAnim()
        mCountDownTimerSupport.stop()
        mStartCountDownTimerSupport.stop()
        updateBgImg(true)
        BGPlayerUtils.instance.pause()
        if (isCompleted == "yes") {
            uploadPracticeLog(UserAction.Practice.value, mTotalPlayTime, true)
            mTotalPlayTime = 0
        } else {
            uploadPracticeLog(UserAction.Practice.value, mTotalPlayTime)
        }
    }

    /**
     * 释放动画
     */
    private fun releaseAnim() {
        readyScale?.setAnimationListener(null)
        readyScale?.cancel()
        readyScale = null

        breathInScale?.setAnimationListener(null)
        breathInScale?.cancel()
        breathInScale = null

        breathlessScale?.setAnimationListener(null)
        breathlessScale?.cancel()
        breathlessScale = null

        breathOutScale?.setAnimationListener(null)
        breathOutScale?.cancel()
        breathOutScale = null

        breathlessSecondScale?.setAnimationListener(null)
        breathlessSecondScale?.cancel()
        breathlessSecondScale = null

        mBind.ivSelectTimeBg.apply {
            clearAnimation()
            invalidate()
        }
    }

    /**
     * 开始准备动画
     */
    private fun startReadyAnim() {
        setAnimByView(readyScale)
    }

    /**
     * 开启呼吸练习动画
     */
    private fun startBreathPracticeAnim() {
        setAnimByView(breathInScale)
    }

    /**
     * 给View设置动画
     */
    private fun setAnimByView(animation: Animation?) {
        if (animation != null) {
            mBind.ivSelectTimeBg.startAnimation(animation)
        }
    }

    /**
     * 关闭Activity
     */
    private fun closeActivity() {
        if (isBreathStart) {
            showToast()
            endBreath("no")
        } else {
            finish()
        }
    }

    /**
     * 展示用户未练习完成退出练习的提示
     */
    @SuppressLint("InflateParams")
    private fun showToast() {
        if (mTotalPlayTime < 60) {
            val view = LayoutInflater.from(this).inflate(R.layout.layout_breath_toast, null)
            val toast = Toast(this)
            toast.duration = Toast.LENGTH_LONG
            toast.setGravity(Gravity.TOP or Gravity.FILL_HORIZONTAL, 0, 0)
            toast.view = view
            toast.show()
        }
    }

    /**
     * 设置呼吸练习提示文字
     */
    private fun setBreathHint(@StringRes ids: Int) {
        mBind.tvBreathHint.text = getString(ids)
    }

    /**
     * 准备开始练习动画监听
     */
    private val readyAnimatorListener by lazy {
        object : Animation.AnimationListener {
            override fun onAnimationRepeat(animation: Animation?) {
            }

            override fun onAnimationEnd(animation: Animation?) {
                startVibrate(this@BreathActivity)
                startBreathPracticeAnim()
            }

            override fun onAnimationStart(animation: Animation?) {
            }
        }
    }

    /**
     * 吸气动画监听
     */
    private val breathInAnimatorListener by lazy {
        object : Animation.AnimationListener {
            override fun onAnimationRepeat(animation: Animation?) {
            }

            override fun onAnimationEnd(animation: Animation?) {
                startVibrate(this@BreathActivity)
                if (mBreathType == 1) {
                    // 呼
                    setAnimByView(breathOutScale)
                } else {
                    // 屏
                    setAnimByView(breathlessScale)
                }
            }

            override fun onAnimationStart(animation: Animation?) {
                setBreathHint(R.string.breath_in)
                BGPlayerUtils.instance.startPlayBackground(mBreathMusicUrl, refreshAudio = true)
            }
        }
    }

    /**
     * 屏气动画监听
     */
    private val breathlessAnimatorListener by lazy {
        object : Animation.AnimationListener {
            override fun onAnimationRepeat(animation: Animation?) {
            }

            override fun onAnimationEnd(animation: Animation?) {
                startVibrate(this@BreathActivity)
                // 呼
                setAnimByView(breathOutScale)
            }

            override fun onAnimationStart(animation: Animation?) {
                setBreathHint(R.string.breathless)
            }
        }
    }

    /**
     * 呼气动画监听
     */
    private val breathOutAnimatorListener by lazy {

        object : Animation.AnimationListener {
            override fun onAnimationRepeat(animation: Animation?) {
            }

            override fun onAnimationEnd(animation: Animation?) {
                startVibrate(this@BreathActivity)
                if (mBreathType == 2) {
                    // 第二次屏
                    setAnimByView(breathlessSecondScale)
                } else {
                    // 吸
                    setAnimByView(breathInScale)
                }
            }

            override fun onAnimationStart(animation: Animation?) {
                setBreathHint(R.string.breath_out)
            }
        }
    }

    /**
     * 屏气动画监听
     */
    private val breathlessSecondAnimatorListener by lazy {
        object : Animation.AnimationListener {
            override fun onAnimationRepeat(animation: Animation?) {
            }

            override fun onAnimationEnd(animation: Animation?) {
                startVibrate(this@BreathActivity)
                // 吸
                setAnimByView(breathInScale)
            }

            override fun onAnimationStart(animation: Animation?) {
                setBreathHint(R.string.breathless)
            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.LOGIN_CHANGE_EVENT || event?.eventCode == ConsEventCode.CHANGE_SUBSCRIBE_EVENT) {
            presenter.apply {
                breathBg()
                breathMode()
            }
        }
    }

    override fun onDestroy() {
        BGPlayerUtils.instance.stop()
        super.onDestroy()
    }

}