package com.imoblife.now.activity.download;


import android.content.DialogInterface;
import android.content.Intent;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.imoblife.commlibrary.base.BackgroundRunner;
import com.imoblife.commlibrary.base.BackgroundTask;
import com.imoblife.commlibrary.base.BaseEvent;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.adapter.DownloadTrackAdapter;
import com.imoblife.now.bean.Course;
import com.imoblife.now.bean.DownLoadInfo;
import com.imoblife.now.bean.ReturnValue;
import com.imoblife.now.bean.Track;
import com.imoblife.now.constant.ConsEventCode;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.model.DownloadMgr;
import com.imoblife.now.model.TrackMgr;
import com.imoblife.now.util.DialogUtil;
import com.imoblife.now.util.ToastUtils;

import com.kongzue.dialog.interfaces.OnDialogButtonClickListener;
import com.kongzue.dialog.util.BaseDialog;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/***
 * ================================================
 * @时间 2017/6/7 on 17:43
 * @作者 Yuan
 * @类名 DownloadProductActivity
 * @描述 我的下载
 * ================================================
 */
public class DownloadTrackActivity extends MvpBaseActivity {

    private static final String TAG = "DownloadProductActivity";
    private RecyclerView recyclerView;
    private DownloadTrackAdapter mAdapter;
    private List<DownLoadInfo> downLoadInfoList;
    private final int EVENT_DELETE_TRACK_DOWNLOAD = 1;
    private final int EVENT_LOAD_TRACK_SERVER = 2;
    private int selectCatId = 0;
    private ImageView backImg;
    private Object delData;
    private Course course;
    private WeakReference<DownloadTrackActivity> mWeakReference;
    @Override
    protected int setContentViewId() {
        return R.layout.activity_download_product;
    }

    @Override
    protected void initView() {
        backImg = (ImageView) findView(R.id.title_back_img);
        backImg.setOnClickListener(listener);
        findView(R.id.title_line).setVisibility(View.VISIBLE);
        TextView title = (TextView) findView(R.id.title_content_text);
        title.setText(course.getTitle());
        mWeakReference = new WeakReference<>(DownloadTrackActivity.this);
        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new DownloadTrackAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setAdapterViewListener((rId, data) -> deleteDownload(data));
        if (course != null) {
            mAdapter.setCourse(course);
            loadDownloadTrack();
        }
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void superInit(Intent intent) {
        super.superInit(intent);
        if (intent != null) {
            if (hasExtra(ConsIntent.BUNDLE_COURSE)) {
                course = (Course) intent.getSerializableExtra(ConsIntent.BUNDLE_COURSE);
                if (course != null) {
                    selectCatId = course.getId();
                } else {
                    finish();
                }
            }
        }
    }


    /**
     * 曲目删除
     */
    private void deleteDownload(Object data) {
        this.delData = data;
        String delTitle = null;
        if (data instanceof Track) {
            Track track = (Track) data;
            delTitle = track.getTitle();
            track.getId();
        }
        DialogUtil.showDialog(mWeakReference.get(), String.format(getString(R.string.is_del_txt), delTitle), new OnDialogButtonClickListener(){
            @Override
            public boolean onClick(BaseDialog baseDialog, View v) {
                showWaitLoading();
                new BackgroundRunner(backgroundTask, EVENT_DELETE_TRACK_DOWNLOAD);
                return false;
            }
        });

    }

    View.OnClickListener listener = v -> {
        switch (v.getId()) {
            case R.id.title_back_img:
                finish();
                break;
        }
    };

    /**
     * 加载下载完成的曲目
     */
    private void loadDownloadTrack() {
        new BackgroundRunner(backgroundTask, EVENT_LOAD_TRACK_SERVER);
    }

    BackgroundTask backgroundTask = new BackgroundTask<ReturnValue>() {
        @Override
        public ReturnValue call(int eventCode, Object... params) {
            ReturnValue returnValue = new ReturnValue();
            switch (eventCode) {
                case EVENT_LOAD_TRACK_SERVER:

                    downLoadInfoList = DownloadMgr.getInstance().queryListByDownloaded();
                    List<Track> tracks = TrackMgr.getInstance().getTracksByDownload(downLoadInfoList, selectCatId);
                    returnValue.setResult(tracks);

                    break;
                case EVENT_DELETE_TRACK_DOWNLOAD:
                    //删除数据库、文件
                    Track track = (Track) delData;
                    ArrayList arrayList = new ArrayList();
                    arrayList.add(String.valueOf(track.getId()));
                    returnValue.setResult(DownloadMgr.getInstance().delDownloadByTrackIds(arrayList));

                    break;
            }
            return returnValue;
        }

        @Override
        public void onResult(int eventCode, ReturnValue t) {
            if (t == null && isFinishing()) {
                return;
            }
            hideWaitLoading();
            switch (eventCode) {
                case EVENT_DELETE_TRACK_DOWNLOAD:
                    if (delData instanceof Track) {
                        loadDownloadTrack();
                    }
                    ToastUtils.showShortToastCenter( getString(R.string.string_txt_track_delete_success));
                    break;
                case EVENT_LOAD_TRACK_SERVER:
                    hideWaitLoading();
                    List<Track> tracks = t.getResult();
                    mAdapter.setDatas(tracks);
                    break;
            }
        }
    };

    @Override
    public void onEventMainThread(BaseEvent event) {
        super.onEventMainThread(event);
        if (event.getEventCode() == ConsEventCode.CHANGE_SUBSCRIBE_EVENT) {
            loadDownloadTrack();
        }
    }
}
