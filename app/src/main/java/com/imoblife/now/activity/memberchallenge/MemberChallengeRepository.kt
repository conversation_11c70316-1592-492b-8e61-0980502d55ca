package com.imoblife.now.activity.memberchallenge

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.MemberChallengeEntity
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiService
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/20
 * 描   述：会员挑战 - 挑战未知的自己 - repository
 */
class MemberChallengeRepository : BaseRepository() {

    /**
     * 会员挑战 - 挑战未知的自己 - 列表
     */
    fun getChallengeList(
        _listData: MutableLiveData<UiStatus<MemberChallengeEntity>>,
        page_id: Int,
        category_id: Int,
        source: String,
    ) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .getChallengeList(page_id, category_id, source)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MemberChallengeEntity>>() {
                override fun onSuccess(response: BaseResult<MemberChallengeEntity>?) {
                    response?.result?.let {
                        _listData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        _listData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    _listData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 会员挑战 - 挑战未知的自己 - 加入挑战赛
     *
     * @param id 挑战赛列表ID
     */
    fun joinSignChallenge(_joinSignChallenge: MutableLiveData<UiStatus<Boolean>>, id: Int) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .signChallenge(id)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<Boolean>>() {
                override fun onSuccess(response: BaseResult<Boolean>?) {
                    response?.result?.let {
                        _joinSignChallenge.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        _joinSignChallenge.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    _joinSignChallenge.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

}