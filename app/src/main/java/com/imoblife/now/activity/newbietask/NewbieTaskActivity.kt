package com.imoblife.now.activity.newbietask

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.LayoutAcNewbieTaskBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import com.imoblife.now.util.ImageLoader

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/3/28
 * 描   述：新手任务 - 激励
 */
class NewbieTaskActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        fun startActivity(context: Context, img: String, imgBg: String) {
            if (!TextUtils.isEmpty(img) && !TextUtils.isEmpty(imgBg)) {
                val intent = Intent(context, NewbieTaskActivity::class.java)
                intent.putExtra(ConsIntent.BUNDLE_NEWBIE_TASK_IMG, img)
                intent.putExtra(ConsIntent.BUNDLE_NEWBIE_TASK_IMG_BG, imgBg)
                context.startActivity(intent)
            }
        }

    }

    private lateinit var mBind: LayoutAcNewbieTaskBinding

    private var mImg = ""
    private var mImgBg = ""

    override fun getLayoutResId() = R.layout.layout_ac_newbie_task

    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentStatusBar().hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .init()
    }

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_NEWBIE_TASK_IMG)) {
                mImg = it.getStringExtra(ConsIntent.BUNDLE_NEWBIE_TASK_IMG) ?: ""
            }

            if (hasExtra(ConsIntent.BUNDLE_NEWBIE_TASK_IMG_BG)) {
                mImgBg = it.getStringExtra(ConsIntent.BUNDLE_NEWBIE_TASK_IMG_BG) ?: ""
            }
        }
    }

    override fun initVM() = ViewModelProvider(this).get(NoViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcNewbieTaskBinding
    }

    override fun initData() {
        mBind.apply {
            ImageLoader.loadImageUrl(this@NewbieTaskActivity, mImg, img)
            ImageLoader.loadImageUrl(this@NewbieTaskActivity, mImgBg, imgBg)
            obBtnView.onDebounceClickListener { finish() }
        }
    }

    override fun startObserve() {}

    override fun onBackPressed() {}

}