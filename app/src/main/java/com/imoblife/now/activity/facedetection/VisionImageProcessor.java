package com.imoblife.now.activity.facedetection;

import android.graphics.Bitmap;

import androidx.camera.core.ImageProxy;

import com.google.mlkit.common.MlKitException;
import com.imoblife.now.bean.FrameMetadata;

import java.nio.ByteBuffer;

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - 视觉图像处理器
 */
public interface VisionImageProcessor {

    void processBitmap(Bitmap bitmap, GraphicOverlay graphicOverlay);

    void processByteBuffer(ByteBuffer data, FrameMetadata frameMetadata, GraphicOverlay graphicOverlay) throws MlKitException;

    void processImageProxy(ImageProxy image, GraphicOverlay graphicOverlay) throws MlKitException;

    void stop();

}
