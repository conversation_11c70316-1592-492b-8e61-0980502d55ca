package com.imoblife.now.activity.breath

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.LayoutAcBreathGuideBinding
import com.imoblife.now.enums.GuideType
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.repository.DeviceGuideRepository
import com.imoblife.now.util.SpUtil
import jp.wasabeef.glide.transformations.BlurTransformation

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/8/5
 * 描   述：呼吸计时 - 引导
 */
class BreathGuideActivity : BaseVMActivity<BreathViewModel>() {

    private lateinit var mBind: LayoutAcBreathGuideBinding

    companion object {

        @JvmStatic
        fun startActivity(context: Context) {
            if (ConfigMgr.getInstance().config.device_dialog != null && ConfigMgr.getInstance().config.device_dialog.breath_guide == 0) {
                val isGuideBreath =
                    SpUtil.getInstance().getBoolenValue(ConsSp.SP_KEY_ENTER_BREATH_GUIDE, false)
                if (isGuideBreath) {
                    BreathActivity.openBreathActivity(context)
                    DeviceGuideRepository().saveLastGuide(GuideType.BREATH_GUIDE.value)
                } else {
                    Intent(context, BreathGuideActivity::class.java).let {
                        context.startActivity(it)
                    }
                }
            } else {
                BreathActivity.openBreathActivity(context)
            }
        }

    }

    override fun initImmersionBar() {}

    override fun getLayoutResId() = R.layout.layout_ac_breath_guide

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this).get(BreathViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcBreathGuideBinding
        mBind.apply {
            clickProxy = ClickProxy()
            // title
            ImmersionBar
                .with(this@BreathGuideActivity)
                .titleBar(toolbar)
                .statusBarDarkFont(false)
                .init()
            includeToolbar.apply {
                titleBackImg.setImageDrawable(
                    ContextCompat.getDrawable(
                        this@BreathGuideActivity,
                        R.mipmap.src_sleep_course_simple_report_title_back
                    )
                )
                titleBackImg.setOnClickListener { onBackPressed() }
                titleContentText.text = getString(R.string.breath_title)
                titleContentText.setTextColor(
                    ContextCompat.getColor(
                        this@BreathGuideActivity,
                        R.color.white
                    )
                )
            }
            Glide
                .with(this@BreathGuideActivity)
                .asBitmap()
                .load(R.mipmap.breath_bg)
                .error(R.mipmap.breath_bg)
                .transform(BlurTransformation(20))
                .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap>?
                    ) {
                        imgBg.setImageBitmap(resource)
                    }
                })
            // 开始动画
            bezierCircleView.onPlay()
        }
    }

    override fun initData() {
        SpUtil.getInstance().saveBooleanToSp(ConsSp.SP_KEY_ENTER_BREATH_GUIDE, true)
        DeviceGuideRepository().saveLastGuide(GuideType.BREATH_GUIDE.value)
    }

    override fun startObserve() {
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                R.id.flStartBreath -> {
                    BreathActivity.openBreathActivity(this@BreathGuideActivity)
                    finish()
                }
                else -> {}
            }
        }

    }

    override fun onDestroy() {
        mBind.bezierCircleView.apply {
            clearAnimation()
            invalidate()
        }
        super.onDestroy()
    }

}