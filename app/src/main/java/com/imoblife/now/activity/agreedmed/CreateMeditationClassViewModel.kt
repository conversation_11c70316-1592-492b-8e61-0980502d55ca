package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.MeditationClassCreateEntity
import com.imoblife.now.bean.MeditationTeamRulesEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-12
 * 描   述：创建约定冥想_ViewModel
 */
class CreateMeditationClassViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { CreateMeditationClassRepository() }

    // 冥想组队规则
    private val _meditationTeamRulesEntity = MutableLiveData<UiStatus<MeditationTeamRulesEntity>>()
    val meditationTeamRulesEntity: LiveData<UiStatus<MeditationTeamRulesEntity>> =
        _meditationTeamRulesEntity

    // 创建冥想班
    private val _meditationClassCreateEntity =
        MutableLiveData<UiStatus<MeditationClassCreateEntity>>()
    val meditationClassCreateEntity: LiveData<UiStatus<MeditationClassCreateEntity>> =
        _meditationClassCreateEntity

    /**
     * 获取冥想组队规则
     */
    fun getMeditationTeamRules() {
        mRepository.getMeditationTeamRules(_meditationTeamRulesEntity)
    }

    /**
     * 创建冥想班
     *
     * @param parameters 参数map
     */
    fun createMeditationClass(parameters: MutableMap<String, String?>) {
        mRepository.createMeditationClass(parameters, _meditationClassCreateEntity)
    }

}