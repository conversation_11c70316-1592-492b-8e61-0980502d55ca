package com.imoblife.now.activity.memberchallenge

import androidx.appcompat.app.AppCompatActivity
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutDialogMemberChallengeStartNewBinding
import com.imoblife.now.view.dialog.BaseBottomSheetDialog

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/21
 * 描   述：会员挑战 - 挑战未知的自己 - 重新开始新选择的挑战 - dialog
 * private val mBlock: ((type: Int) -> Unit)? = null => type 0为否，1为是
 */
class MemberChallengeStartNewDialog(private val mBlock: ((type: Int) -> Unit)? = null) :
    BaseBottomSheetDialog(true) {

    private lateinit var mBind: LayoutDialogMemberChallengeStartNewBinding

    override val viewId = R.layout.layout_dialog_member_challenge_start_new

    override fun initView() {
        mBind = mBinding as LayoutDialogMemberChallengeStartNewBinding
        mBind.apply {
            imgClose.setOnClickListener { dismiss() }
            stvBtnYes.setOnClickListener {
                mBlock?.invoke(1)
                dismiss()
            }
            stvBtnNo.setOnClickListener {
                mBlock?.invoke(0)
                dismiss()
            }
        }
    }

    fun show(context: AppCompatActivity) {
        context.let { show(it.supportFragmentManager, it.javaClass.simpleName) }
    }

}