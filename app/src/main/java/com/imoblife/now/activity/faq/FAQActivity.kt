package com.imoblife.now.activity.faq

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.activity.setting.FeedbackActivity
import com.imoblife.now.adapter.FAQAdapter
import com.imoblife.now.adapter.FAQTabAdapter
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.FAQTabBean
import com.imoblife.now.bean.FAQTableDetailBean
import com.imoblife.now.databinding.ActivityFaqBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.ExpandableViewHoldersUtil
import com.imoblife.now.util.UnicornManager

class FAQActivity : BaseVMActivity<FAQModel>() {

    private var lastSelectTab = 0
    private var loadingHelper: LoadingHelper? = null

    private lateinit var mBind: ActivityFaqBinding

    companion object {
        @JvmStatic
        fun startFAQ(context: Context) {
            Intent(context, FAQActivity::class.java).apply {
                context.startActivity(this)
            }
        }
    }

    private val tabData by lazy {
        mutableListOf<FAQTabBean>()
    }
    private val data by lazy {
        mutableListOf<FAQTableDetailBean>()
    }

    private val mAdapter by lazy {
        FAQAdapter(data)
    }

    private val tabAdapter by lazy {
        FAQTabAdapter(tabData)
    }

    override fun superInit(intent: Intent?) {

    }

    override fun getLayoutResId(): Int = R.layout.activity_faq

    override fun initVM() = ViewModelProvider(this).get(FAQModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityFaqBinding
        mBind.clickProxy = ClickProxy()
        loadingHelper = ToolbarUtils.setToolbar(this, getString(R.string.qa_txt), NavIconType.BACK)
        ExpandableViewHoldersUtil.getInstance().init().setNeedExplanedOnlyOne(true)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        mBind.apply {
            tagCloudView.setOnTagClickListener { _, position, _ ->
                if (lastSelectTab == position) {
                    tabAdapter.setSelectedList(position)
                }
                lastSelectTab = position
                true
            }
            tagCloudView.setOnSelectListener {
                it.forEach { index ->
                    if (tabData.size > index) {
                        val detail = tabData[index].detail
                        data.clear()
                        data.addAll(detail)
                        mAdapter.notifyDataSetChanged()
                    }
                }
            }

            tagCloudView.adapter = tabAdapter
            recyclerView.layoutManager = LinearLayoutManager(this@FAQActivity)
            //清空记录展开还是关闭的缓存数据
            ExpandableViewHoldersUtil.getInstance().resetExpanedList()
            recyclerView.adapter = mAdapter
            mViewModel.getFAQ()
            loadingHelper?.showLoadingView()
        }
    }

    override fun startObserve() {
        mViewModel.let {
            it.faq.observe(this, Observer { uiStatus ->
                loadingHelper?.showContentView()
                if (uiStatus.isSuccess) {
                    tabData.clear()
                    val successData = uiStatus.successData
                    if (successData != null) {
                        tabData.addAll(successData)
                        tabAdapter.notifyDataChanged()
                        if (tabData.size > 0) {
                            tabAdapter.setSelectedList(0)
                            lastSelectTab = 0
                            data.clear()
                            data.addAll(tabData[0].detail)
                            mAdapter.notifyDataSetChanged()
                        }
                    }
                }
            })
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 在线客服
                R.id.fag_btn_go_to_the_service -> {
                    UnicornManager.openQiYuService(this@FAQActivity,
                        getString(R.string.string_faq_page))
                }
                // 意见反馈
                R.id.fag_btn_feedback -> {
                    startActivity(Intent(this@FAQActivity, FeedbackActivity::class.java))
                }
            }
        }

    }

}