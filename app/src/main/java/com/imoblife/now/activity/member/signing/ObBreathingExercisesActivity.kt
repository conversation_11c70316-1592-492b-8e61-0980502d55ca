package com.imoblife.now.activity.member.signing

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.activity.main.MainActivity
import com.imoblife.now.databinding.LayoutAcObBreathingExercisesBinding
import com.imoblife.now.ext.animAlphaHide
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import com.imoblife.now.view.dialog.GuideFirstPracticeDialog
import com.shuyu.gsyvideoplayer.GSYVideoManager

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/7/6
 * 描   述：ob - 过渡 - 1分钟快速减压简短呼吸练习
 */
class ObBreathingExercisesActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            val intent = Intent(context, ObBreathingExercisesActivity::class.java)
            context.startActivity(intent)
        }

    }

    private lateinit var mBind: LayoutAcObBreathingExercisesBinding

    private var mPlayFlag = false

    override fun getLayoutResId() = R.layout.layout_ac_ob_breathing_exercises

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this).get(NoViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutAcObBreathingExercisesBinding
        mBind.apply {
            emptyControlVideo.setUp(
                "android.resource://${packageName}/${R.raw.ob_breathing_exercises}",
                true,
                ""
            )
            emptyControlVideo.setOnPlayAutoCompletion {
                GuideFirstPracticeDialog().showDialog(supportFragmentManager)
            }
            emptyControlVideo.startPlayLogic()
            emptyControlVideo.onVideoPause()
            tvBtnSkip.onDebounceClickListener {
                MainActivity.openMainActivity(
                    this@ObBreathingExercisesActivity,
                    ConfigMgr.getInstance().config.app_default_tab,
                )
            }
            stvBtn.onDebounceClickListener {
                mPlayFlag = true
                imgBg.visibility = View.GONE
                emptyControlVideo.onVideoResume()
                stvBtn.animAlphaHide()
                stvBtn.isEnabled = false
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (mPlayFlag) {
            mBind.emptyControlVideo.onVideoResume()
        }
    }

    override fun onPause() {
        super.onPause()
        mBind.emptyControlVideo.onVideoPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        mBind.emptyControlVideo.setVideoAllCallBack(null)
        mBind.emptyControlVideo.release()
        GSYVideoManager.releaseAllVideos()
    }

    override fun initData() {}

    override fun startObserve() {}

    override fun onBackPressed() {}

}