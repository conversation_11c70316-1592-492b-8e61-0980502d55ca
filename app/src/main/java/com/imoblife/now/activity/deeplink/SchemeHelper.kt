package com.imoblife.now.activity.deeplink

import android.net.Uri
import android.util.Log

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/3/21
 * 描   述：DeepLink - SchemeHelper
 */
object SchemeHelper {

    private var mUri: Uri? = null

    /**
     * 设置待操作的uri
     *
     * @param uri Uri?
     */
    @JvmStatic
    fun setPendingSchemeUri(uri: Uri?) {
        mUri = uri
        mUri?.let { parseUriFromIntent(it) }
    }

    /**
     * 获取待操作的uri
     *
     * @return Uri?
     */
    @JvmStatic
    fun getPendingSchemeUri(): Uri? = mUri

    /**
     * 热启动 - 解析 Uri
     */
    @JvmStatic
    fun parseUriFromIntent(uri: Uri) {
        uri.apply {
            val scheme = scheme
            val host = host
            val params = pathSegments
//            val params0 = params[0]
            val query = query
            // adb shell am start -W -a android.intent.action.VIEW -d "now://com.imoblife.now.deeplink" com.imoblife.now
            // yunyang://www.yunyang.com/courseId/?courseId=168
            Log.e("yunyang", "scheme = $scheme")
            Log.e("yunyang", "host = $host")
            Log.e("yunyang", "params = $params  params = ${params.toString()}")
//            Log.e("yunyang", "params0 = $params0")
            Log.e("yunyang", "query = $query")
        }
    }

}