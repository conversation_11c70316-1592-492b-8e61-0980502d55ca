package com.imoblife.now.activity.member;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.view.View;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.coorchice.library.SuperTextView;
import com.imoblife.commlibrary.base.BaseEvent;
import com.imoblife.commlibrary.mvp.CreatePresenter;
import com.imoblife.now.R;
import com.imoblife.now.activity.promotion.PromotionActivity;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.activity.yoga.YogaPayActivity;
import com.imoblife.now.bean.Coin;
import com.imoblife.now.bean.Course;
import com.imoblife.now.bean.DayMindfulness;
import com.imoblife.now.bean.PromotionCode;
import com.imoblife.now.bean.Subscribe;
import com.imoblife.now.bean.Testing;
import com.imoblife.now.constant.ConsCommon;
import com.imoblife.now.constant.ConsEventCode;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.constant.ConsRequestCode;
import com.imoblife.now.enums.PayWay;
import com.imoblife.now.enums.ProductType;
import com.imoblife.now.model.UserMgr;
import com.imoblife.now.mvp_contract.PaymentContract;
import com.imoblife.now.mvp_presenter.PaymentPresenter;
import com.imoblife.now.payment.ExchangeCenter;
import com.imoblife.now.payment.PayCenter;
import com.imoblife.now.util.ChannelUtils;
import java.io.Serializable;
import java.lang.ref.WeakReference;

/**
 * 结算
 */
@CreatePresenter(presenter = PaymentPresenter.class)
public class PaymentActivity extends MvpBaseActivity<PaymentPresenter> implements PaymentContract.IPaymentView, View.OnClickListener {

    public static final String PAY_TYPE_SUBSCRIBE = "pay_type_subscribe";
    public static final String PAY_TYPE_COIN = "pay_type_coin";
    public static final String PAY_TYPE_JP_COURSE = "pay_type_jp_course";
    public static final String PAY_TYPE_COURSE = "pay_type_course";
    public static final String PAY_TYPE_STATION = "pay_type_station";
    public static final String PAY_TYPE_BOOK = "pay_type_book";
    public static final String PAY_TYPE_TESTING = "pay_type_testing";
    public static final String PAY_TYPE_MINDFULNESS = "pay_type_mindfulness";

    private TextView titleContentText;
    private SuperTextView paymentTypeIcon;
    private TextView paymentTypeNameTxt;
    private TextView payDiscountPriceTxt;
    private TextView payOriginalPriceTxt;
    private TextView selectCodeTxt;
    private RadioButton weChatRadio, aLiPayRadio, huaWeiRadio,miRadio, nowRadio;
    private SuperTextView paymentRechargeTxt, paymentOpenVipTxt;
    //会员折扣
    private TextView paymentJpDiscountTxt;
    //会员可减少钱
    private TextView paymentJpReduceTxt;
    private RelativeLayout paymentNowRrl;
    private TextView payTxt;
    private TextView paymentNowEnoughTxt;
    private RelativeLayout paymentJpOpenVipRly;
    private RelativeLayout paymentPromotionRly;
    private PromotionCode promotionCode;
    private String codeName = "";
    private String payType;
    private String payTitle;
    private float payMoney;
    private int payId;
    private Course payCourse;
    private Course payJpCourse;
    private Coin payCoin;
    private Subscribe paySubscribe;
    private DayMindfulness payDayMindfulness;
    private Testing testing;
    private float payCoinPrice;
    private int course_id = 0;
    private WeakReference mWeakReference;
    public static void openActivity(Context context, String payType, int course_id, Object object) {
        if (context!=null && context instanceof Activity && !((Activity) context).isFinishing()) {
            Intent intent = new Intent(context, PaymentActivity.class);
            intent.putExtra(ConsIntent.BUNDLE_PAY_TYPE, payType);
            intent.putExtra(ConsIntent.BUNDLE_COURSE_ID_KEY, course_id);
            intent.putExtra(ConsIntent.BUNDLE_PAY, (Serializable) object);
            context.startActivity(intent);
        }
    }

    @Override
    protected int setContentViewId() {
        return R.layout.activity_payment;
    }
    @Override
    protected void initData() { }

    @Override
    protected void superInit(Intent intent) {
        super.superInit(intent);

        if (hasExtra(ConsIntent.BUNDLE_PAY_TYPE)) {
            payType = intent.getStringExtra(ConsIntent.BUNDLE_PAY_TYPE);
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_ID_KEY)) {
            course_id = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_ID_KEY, -1);
        }

        if (hasExtra(ConsIntent.BUNDLE_PAY)) {
            if (PAY_TYPE_SUBSCRIBE.equals(payType)) {
                paySubscribe = (Subscribe) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            } else if (PAY_TYPE_COIN.equals(payType)) {
                payCoin = (Coin) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            } else if (PAY_TYPE_COURSE.equals(payType)) {
                payCourse = (Course) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            } else if (PAY_TYPE_JP_COURSE.equals(payType)) {
                payJpCourse = (Course) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            } else if (PAY_TYPE_MINDFULNESS.equals(payType)) {
                payDayMindfulness = (DayMindfulness) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            } else if (PAY_TYPE_BOOK.equals(payType)) {
                payCourse = (Course) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            } else if (PAY_TYPE_STATION.equals(payType)) {
                payCourse = (Course) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            }else if (PAY_TYPE_TESTING.equals(payType)){
                testing= (Testing) intent.getSerializableExtra(ConsIntent.BUNDLE_PAY);
            }
        }
    }

    @Override
    protected void initView() {
        mWeakReference = new WeakReference<>(this);
        ImageView titleBackImg = (ImageView) findView(R.id.title_back_img);
        titleBackImg.setOnClickListener(this);
        titleContentText = (TextView) findView(R.id.title_content_text);
        titleContentText.setText(R.string.payment_txt);

        paymentTypeIcon = (SuperTextView) findView(R.id.payment_type_icon);
        paymentTypeNameTxt = (TextView) findView(R.id.payment_type_name_txt);
        payDiscountPriceTxt = (TextView) findView(R.id.pay_discount_price_txt);
        payOriginalPriceTxt = (TextView) findView(R.id.pay_original_price_txt);
        weChatRadio = (RadioButton) findView(R.id.payment_weChat_radio);
        aLiPayRadio = (RadioButton) findView(R.id.payment_ali_radio);
        huaWeiRadio = (RadioButton) findView(R.id.payment_huaWei_radio);
        miRadio= (RadioButton) findView(R.id.payment_miui_radio);
        nowRadio = (RadioButton) findView(R.id.payment_now_radio);

        paymentNowRrl = (RelativeLayout) findView(R.id.payment_now_rrl);
        selectCodeTxt = (TextView) findView(R.id.select_code_txt);
        paymentNowEnoughTxt = (TextView) findView(R.id.payment_now_enough_txt);
        paymentJpOpenVipRly = (RelativeLayout) findView(R.id.payment_open_vip_rrl);
        paymentPromotionRly = (RelativeLayout) findView(R.id.payment_promotion_rrl);

        paymentRechargeTxt = (SuperTextView) findView(R.id.payment_recharge_txt);
        paymentRechargeTxt.setOnClickListener(this);
        paymentOpenVipTxt = (SuperTextView) findView(R.id.payment_open_vip_txt);
        paymentOpenVipTxt.setOnClickListener(this);

        paymentJpDiscountTxt = (TextView) findView(R.id.payment_jp_discount_txt);
        paymentJpReduceTxt = (TextView) findView(R.id.payment_jp_reduce_txt);

        selectCodeTxt.setOnClickListener(this);
        payTxt = (TextView) findView(R.id.pay_txt);
        payTxt.setOnClickListener(this);

        aLiPayRadio.setChecked(true);
        weChatRadio.setVisibility(View.GONE);
        huaWeiRadio.setVisibility(View.GONE);
        miRadio.setVisibility(View.GONE);

        weChatRadio.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                weChatRadio.setChecked(true);
                aLiPayRadio.setChecked(false);
                nowRadio.setChecked(false);
                huaWeiRadio.setChecked(false);
            }
        });
        aLiPayRadio.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                aLiPayRadio.setChecked(true);
                weChatRadio.setChecked(false);
                nowRadio.setChecked(false);
                huaWeiRadio.setChecked(false);
            }
        });
        huaWeiRadio.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                huaWeiRadio.setChecked(true);
                weChatRadio.setChecked(false);
                aLiPayRadio.setChecked(false);
                nowRadio.setChecked(false);
            }
        });
        miRadio.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                miRadio.setChecked(true);
                huaWeiRadio.setChecked(false);
                weChatRadio.setChecked(false);
                aLiPayRadio.setChecked(false);
                nowRadio.setChecked(false);
            }
        });
        nowRadio.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                nowRadio.setChecked(true);
                weChatRadio.setChecked(false);
                aLiPayRadio.setChecked(false);
                huaWeiRadio.setChecked(false);
                miRadio.setChecked(false);
            }
        });
        //订阅
        if (PAY_TYPE_SUBSCRIBE.equals(payType) && paySubscribe != null) {
            payTitle = paySubscribe.getTitle();
            paymentNowRrl.setVisibility(View.GONE);
            paymentTypeIcon.setDrawable(R.mipmap.icon_payment_type_vip);
            if (paySubscribe.hasDisCount()) {
                payDiscountPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), paySubscribe.getDiscount_price()));
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), paySubscribe.getPrice()));
                payOriginalPriceTxt.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
                payOriginalPriceTxt.getPaint().setAntiAlias(true);
                payMoney = paySubscribe.getDiscount_price();
            } else {
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), paySubscribe.getPrice()));
                payMoney = paySubscribe.getPrice();
            }
            paymentPromotionRly.setVisibility(View.VISIBLE);
            payId = paySubscribe.getId();
            doSettlementPayMoney();
            //now贝
        } else if (PAY_TYPE_COIN.equals(payType) && payCoin != null) {
            payTitle = payCoin.getTitle();
            paymentNowRrl.setVisibility(View.GONE);
            paymentTypeIcon.setDrawable(R.mipmap.icon_payment_type_now);

            if (payCoin.hasDiscount()) {
                payDiscountPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payCoin.getDiscount_price()));
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payCoin.getPrice()));
                payOriginalPriceTxt.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
                payOriginalPriceTxt.getPaint().setAntiAlias(true);
                payMoney = payCoin.getDiscount_price();
            } else {
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payCoin.getPrice()));
                payMoney = payCoin.getPrice();
            }
            paymentPromotionRly.setVisibility(View.GONE);
            payId = payCoin.getId();
            doSettlementPayMoney();
        } else if ((PAY_TYPE_COURSE.equals(payType)
                || PAY_TYPE_BOOK.equals(payType)
                || PAY_TYPE_STATION.equals(payType))
                && payCourse != null) {
            //普通课程
            payTitle = payCourse.getTitle();
            paymentTypeIcon.setUrlImage(payCourse.getThumb_img());
            paymentPromotionRly.setVisibility(View.VISIBLE);
            paymentJpOpenVipRly.setVisibility(View.GONE);
            if (payCourse.hasDiscount()) {
                payDiscountPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payCourse.getDiscount_price()));
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payCourse.getPrice()));
                payOriginalPriceTxt.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
                payOriginalPriceTxt.getPaint().setAntiAlias(true);
                payMoney = payCourse.getDiscount_price();
                payCoinPrice = payCourse.getCoin_discount_price();
            } else {
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payCourse.getPrice()));
                payMoney = payCourse.getPrice();
                payCoinPrice = payCourse.getCoin_price();
            }
            if (UserMgr.getInstance().getUser()!=null && Float.compare(UserMgr.getInstance().getUserCoin(), payCourse.getCoin_price()) >= 0) {
                paymentNowEnoughTxt.setVisibility(View.GONE);
                paymentRechargeTxt.setVisibility(View.GONE);
            } else {
                paymentNowEnoughTxt.setVisibility(View.VISIBLE);
                paymentRechargeTxt.setVisibility(View.VISIBLE);
            }
            payId = payCourse.getId();
            doSettlementPayMoney();
        } else if (PAY_TYPE_JP_COURSE.equals(payType) && payJpCourse != null) {
            //精品课
            payTitle = payJpCourse.getTitle();
            paymentTypeIcon.setUrlImage(payJpCourse.getThumb_img());
            paymentJpOpenVipRly.setVisibility(View.VISIBLE);
            paymentPromotionRly.setVisibility(View.VISIBLE);

            if (payJpCourse.hasDiscount()) {
                payDiscountPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payJpCourse.getDiscount_price()));
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payJpCourse.getPrice()));
                payOriginalPriceTxt.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
                payOriginalPriceTxt.getPaint().setAntiAlias(true);
                payMoney = payJpCourse.getDiscount_price();
                payCoinPrice = payJpCourse.getCoin_discount_price();
            } else {
                payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payJpCourse.getPrice()));
                payMoney = payJpCourse.getPrice();
                payCoinPrice = payJpCourse.getCoin_price();
            }

            //会员用户购买精品课
            if (UserMgr.getInstance().isHasNowVip()) {
                paymentOpenVipTxt.setVisibility(View.VISIBLE);
                paymentOpenVipTxt.setCorner(0);
                paymentOpenVipTxt.setSolid(getResources().getColor(R.color.white));
                paymentOpenVipTxt.setClickable(false);

                paymentJpDiscountTxt.setText("会员优惠");
                paymentJpReduceTxt.setVisibility(View.GONE);

                if (payJpCourse.hasDiscount()) {
                    paymentOpenVipTxt.setText(String.format("-￥%s", payJpCourse.getDiscount_price() - payJpCourse.getVip_price()));
                } else {
                    paymentOpenVipTxt.setText(String.format("-￥%s", payJpCourse.getPrice() - payJpCourse.getVip_price()));
                }

                payMoney = payJpCourse.getVip_price();
                payCoinPrice = payJpCourse.getVip_coin_price();

                if (Float.compare(UserMgr.getInstance().getUserCoin(), payJpCourse.getVip_coin_price()) >= 0) {
                    paymentNowEnoughTxt.setVisibility(View.GONE);
                    paymentRechargeTxt.setVisibility(View.GONE);
                } else {
                    paymentNowEnoughTxt.setVisibility(View.VISIBLE);
                    paymentRechargeTxt.setVisibility(View.VISIBLE);
                }

            } else {
                paymentOpenVipTxt.setVisibility(View.VISIBLE);
                paymentOpenVipTxt.setCorner(R.dimen.qb_px_13);
                paymentOpenVipTxt.setSolid(getResources().getColor(R.color.color_pay_vip));
                paymentOpenVipTxt.setClickable(true);
                paymentOpenVipTxt.setText("成为会员");
                paymentJpDiscountTxt.setText("成为会员立减");
                paymentJpReduceTxt.setVisibility(View.VISIBLE);

                if (payJpCourse.hasDiscount()) {
                    paymentJpReduceTxt.setText(String.format("￥%s元",
                            payJpCourse.getDiscount_price() - payJpCourse.getVip_price()));
                } else {
                    paymentJpReduceTxt.setText(
                            String.format("￥%s元", payJpCourse.getPrice() - payJpCourse.getVip_price()));
                }

                if (Float.compare(UserMgr.getInstance().getUserCoin(), payJpCourse.getCoin_price()) >= 0) {
                    paymentNowEnoughTxt.setVisibility(View.GONE);
                    paymentRechargeTxt.setVisibility(View.GONE);
                } else {
                    paymentNowEnoughTxt.setVisibility(View.VISIBLE);
                    paymentRechargeTxt.setVisibility(View.VISIBLE);
                }
            }
            payId = payJpCourse.getId();
            doSettlementPayMoney();
        } else if (PAY_TYPE_MINDFULNESS.equals(payType) && payDayMindfulness != null) {
            //每日正念
            paymentJpOpenVipRly.setVisibility(View.GONE);
            paymentPromotionRly.setVisibility(View.GONE);
            payTitle = getString(R.string.daily_txt);
            payMoney = payDayMindfulness.getPrice();
            paymentTypeIcon.setUrlImage(payDayMindfulness.getButton_background());
            payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt), payDayMindfulness.getPrice()));
            paymentPromotionRly.setVisibility(View.VISIBLE);

            if (Float.compare(UserMgr.getInstance().getUserCoin(),payDayMindfulness.getCoin_price()) >= 0) {
                paymentNowEnoughTxt.setVisibility(View.GONE);
                paymentRechargeTxt.setVisibility(View.GONE);
            } else {
                paymentNowEnoughTxt.setVisibility(View.VISIBLE);
                paymentRechargeTxt.setVisibility(View.VISIBLE);
            }
            payCoinPrice = payDayMindfulness.getCoin_price();
            payId = payDayMindfulness.getId();
            doSettlementPayMoney();
        }else if (PAY_TYPE_TESTING.equals(payType) && testing!=null){


            //心理测评
            paymentJpOpenVipRly.setVisibility(View.GONE);
            paymentPromotionRly.setVisibility(View.GONE);
            payTitle = testing.getTitle();
            payMoney = Float.parseFloat(testing.getDiscount_price());
            paymentTypeIcon.setUrlImage(testing.getCover_img_s());
            payDiscountPriceTxt.setText(String.format(getString(R.string.price_yuan_txt),Float.parseFloat(testing.getDiscount_price())));
            payOriginalPriceTxt.setText(String.format(getString(R.string.price_yuan_txt),Float.parseFloat(testing.getPrice())));
            payOriginalPriceTxt.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
            payOriginalPriceTxt.getPaint().setAntiAlias(true);


            if (Float.compare(UserMgr.getInstance().getUserCoin(),Float.parseFloat(testing.getDiscount_price())) >= 0) {
                paymentNowEnoughTxt.setVisibility(View.GONE);
                paymentRechargeTxt.setVisibility(View.GONE);
            } else {
                paymentNowEnoughTxt.setVisibility(View.VISIBLE);
                paymentRechargeTxt.setVisibility(View.VISIBLE);
            }
            payCoinPrice = Float.parseFloat(testing.getDiscount_price());
            payId = testing.getId();
        }
        paymentTypeNameTxt.setText(payTitle);
        payTxt.setText(String.format(getString(R.string.pay_sure_txt), payMoney));
    }




    private void doSettlementPayMoney() {
        if (PAY_TYPE_COIN.equals(payType)) {
            getPresenter().settlementPayCoinMoney(String.valueOf(payId), null, null, null, promotionCode);
        } else if (PAY_TYPE_SUBSCRIBE.equals(payType)) {
            getPresenter().settlementPayCoinMoney(null, String.valueOf(payId), null, null, promotionCode);
        } else if (PAY_TYPE_COURSE.equals(payType)
                || PAY_TYPE_JP_COURSE.equals(payType)
                || PAY_TYPE_BOOK.equals(payType)
                ||PAY_TYPE_STATION.equals(payType)) {
            getPresenter().settlementPayCoinMoney(null, null, String.valueOf(payId), null, promotionCode);
        } else if (PAY_TYPE_MINDFULNESS.equals(payType)) {
            getPresenter().settlementPayCoinMoney(null, null, null, String.valueOf(payId), promotionCode);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.title_back_img:
                finish();
                break;
            case R.id.select_code_txt:
                Intent intent = new Intent(PaymentActivity.this, PromotionActivity.class);
                intent.putExtra(ConsIntent.BUNDLE_CODE, codeName);
                intent.putExtra(ConsIntent.BUNDLE_PAY_MONEY, payMoney);
                intent.putExtra(ConsIntent.BUNDLE_FROM_KEY, PaymentActivity.class.getSimpleName());
                startActivityForResult(intent, ConsRequestCode.SEND_REQUEST_SELECT_CODE);
                break;
            case R.id.payment_recharge_txt:
                startActivity(new Intent(this, RechargeActivity.class));
                break;
            case R.id.payment_open_vip_txt:
                YogaPayActivity.startYogaPay(mContext, course_id, ConsCommon.COURSE_TYPE_TEACHER_COURSE);
                break;
            case R.id.pay_txt:
                pay();
                break;
        }
    }

    private void pay() {
        int payWay = 0;
        if (weChatRadio.isChecked()) {
            payWay = PayWay.WX.value();
        } else if (aLiPayRadio.isChecked()) {
            payWay = PayWay.ZFB.value();
        } else if (huaWeiRadio.isChecked()) {
            payWay = PayWay.HW.value();
        } else if (miRadio.isChecked()){
            payWay = PayWay.MI.value();
        }else if (nowRadio.isChecked()) {
            payWay = PayWay.COIN.value();
        }

        if (PAY_TYPE_COURSE.equals(payType)) {
            //普通课购买 可以Now贝购买
            if (payWay == PayWay.COIN.value()) {
                ExchangeCenter.getInstance().payByCoin((Activity) mWeakReference.get(), payCoinPrice, payCourse.getTitle(), payCourse.getId(),ConsCommon.RECHARGE_TYPE_COURSE, codeName);
            } else {
                Subscribe subscribe=new Subscribe();
                subscribe.setId(payCourse.getId());
                subscribe.setTitle(payCourse.getTitle());
                subscribe.setProduct_type(ProductType.COURSE.value());
                subscribe.setIs_sub(false);
                subscribe.setPay_type(payWay);
                subscribe.getParams().setDiscount_code(codeName);
                PayCenter.getInstance().doSubmitPay((AppCompatActivity) mWeakReference.get(),subscribe);
            }
        } else if (PAY_TYPE_STATION.equals(payType)) {
            //普通课购买 可以Now贝购买
            if (payWay == PayWay.COIN.value()) {
                ExchangeCenter.getInstance().payByCoin((Activity) mWeakReference.get(), payCoinPrice, payCourse.getTitle(), payCourse.getId(),ConsCommon.RECHARGE_TYPE_STATION, codeName);
            } else {
                Subscribe subscribe=new Subscribe();
                subscribe.setId(payCourse.getId());
                subscribe.setTitle(payCourse.getTitle());
                subscribe.setProduct_type(ProductType.COURSE.value());
                subscribe.setIs_sub(false);
                subscribe.setPay_type(payWay);
                subscribe.getParams().setDiscount_code(codeName);
                PayCenter.getInstance().doSubmitPay((AppCompatActivity) mWeakReference.get(),subscribe);
            }
        }  else if (PAY_TYPE_BOOK.equals(payType)) {
            //读书购买 可以Now贝购买
            if (payWay == PayWay.COIN.value()) {
                ExchangeCenter.getInstance().payByCoin((Activity) mWeakReference.get(), payCoinPrice, payCourse.getTitle(), payCourse.getId(),ConsCommon.RECHARGE_TYPE_BOOK, codeName);
            } else {
                Subscribe subscribe=new Subscribe();
                subscribe.setId(payCourse.getId());
                subscribe.setTitle(payCourse.getTitle());
                subscribe.setProduct_type(ProductType.COURSE.value());
                subscribe.setIs_sub(false);
                subscribe.setPay_type(payWay);
                subscribe.getParams().setDiscount_code(codeName);
                PayCenter.getInstance().doSubmitPay((AppCompatActivity) mWeakReference.get(),subscribe);
            }
        }else if (PAY_TYPE_JP_COURSE.equals(payType)) {
            //精品课购买 可以Now贝购买
            if (payWay == PayWay.COIN.value()) {
                ExchangeCenter.getInstance().payByCoin((Activity) mWeakReference.get(), payCoinPrice, payJpCourse.getTitle(), payJpCourse.getId(), ConsCommon.RECHARGE_TYPE_TEACHER_COURSE, codeName);
            } else {
                Subscribe subscribe=new Subscribe();
                subscribe.setId(payJpCourse.getId());
                subscribe.setTitle(payJpCourse.getTitle());
                subscribe.setProduct_type(ProductType.COURSE.value());
                subscribe.setIs_sub(false);
                subscribe.setPay_type(payWay);
                subscribe.getParams().setDiscount_code(codeName);
                PayCenter.getInstance().doSubmitPay((AppCompatActivity) mWeakReference.get(),subscribe);
            }
        } else if (PAY_TYPE_MINDFULNESS.equals(payType)) {
            //每日正念购买  可以Now贝购买
            if (payWay == PayWay.COIN.value()) {
                ExchangeCenter.getInstance().payByCoin((Activity) mWeakReference.get(), payCoinPrice, payDayMindfulness.getTitle(), payDayMindfulness.getDaily_id(), ConsCommon.RECHARGE_TYPE_DAILY, codeName);
            } else {
                Subscribe subscribe=new Subscribe();
                subscribe.setId(payDayMindfulness.getDaily_id());
                subscribe.setTitle(payDayMindfulness.getTitle());
                subscribe.setProduct_type(ProductType.COURSE.value());
                subscribe.setIs_sub(false);
                subscribe.setPay_type(payWay);
                subscribe.getParams().setDiscount_code(codeName);
                PayCenter.getInstance().doSubmitPay((AppCompatActivity) mWeakReference.get(),subscribe);
            }
        }else if (PAY_TYPE_TESTING.equals(payType)) {
            //心理测评购买  可以Now贝购买
            if (payWay == PayWay.COIN.value()) {
                ExchangeCenter.getInstance().payByCoin((Activity) mWeakReference.get(), payCoinPrice, testing.getTitle(), testing.getId(), ConsCommon.RECHARGE_TYPE_TESTING, codeName);
            } else {
                Subscribe subscribe=new Subscribe();
                subscribe.setId(testing.getId());
                subscribe.setTitle(testing.getTitle());
                subscribe.setProduct_type(ProductType.TESTING.value());
                subscribe.setIs_sub(false);
                subscribe.setPay_type(payWay);
                subscribe.getParams().setDiscount_code(codeName);
                PayCenter.getInstance().doSubmitPay((AppCompatActivity) mWeakReference.get(),subscribe);
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == ConsRequestCode.SEND_REQUEST_SELECT_CODE) {
            if (data.hasExtra(ConsIntent.BUNDLE_PROMOTION_CODE)) {
                promotionCode = (PromotionCode) data.getSerializableExtra(ConsIntent.BUNDLE_PROMOTION_CODE);
                if (promotionCode != null) {
                    codeName = promotionCode.getCode();
                    selectCodeTxt.setText(String.format("-￥%s", promotionCode.getDiscount_amount()));
                    doSettlementPayMoney();
                }
            }
        }
    }

    @Override
    public void onEventMainThread(BaseEvent event) {
        super.onEventMainThread(event);
        if (
                event.getEventCode() == ConsEventCode.CHANGE_SUBSCRIBE_EVENT||
                        event.getEventCode()==ConsEventCode.PAY_TESTING_SUCCESS_EVENT
        ) {
            finish();
        }
    }

    @Override
    public void showPayCoinCount(float money) {
        payCoinPrice = money;
        if (Float.compare(UserMgr.getInstance().getUserCoin(), payCoinPrice) >= 0) {
            paymentNowEnoughTxt.setVisibility(View.GONE);
            paymentRechargeTxt.setVisibility(View.GONE);
        } else {
            paymentNowEnoughTxt.setVisibility(View.VISIBLE);
            paymentRechargeTxt.setVisibility(View.VISIBLE);
        }
        payTxt.setText(String.format(getString(R.string.pay_sure_txt), money));
    }


}
