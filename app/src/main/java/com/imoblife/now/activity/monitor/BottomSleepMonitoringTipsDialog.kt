package com.imoblife.now.activity.monitor

import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.imoblife.now.R
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutViewSleepMonitoringTipsBinding
import com.imoblife.now.util.SpUtil
import com.imoblife.now.view.dialog.OnClickViewListener

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/26
 * 描   述：睡眠监测提示 - Dialog
 * 确保手机保持充电状态 并放置枕头旁
 */
class BottomSleepMonitoringTipsDialog(context: Context) : Dialog(context, R.style.DialogBottom) {

    private var mBind: LayoutViewSleepMonitoringTipsBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        R.layout.layout_view_sleep_monitoring_tips,
        null,
        false
    )

    private var mOnClickViewListener: OnClickViewListener? = null

    init {
        mBind.apply {
            setContentView(root)
            clickProxy = ClickProxy()
        }
    }

    override fun show() {
        if (!SpUtil.getInstance()
                .getBoolenValue(ConsCommon.SLEEP_MONITORING_TIPS_NOT_SHOW, false)
        ) {
            super.show()
            val attributes = window?.attributes
            attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window?.setGravity(Gravity.BOTTOM)
            window?.attributes = attributes
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 关闭
                R.id.imgClose -> dismiss()
                // 好的
                R.id.stvBtn -> {
                    mOnClickViewListener?.onImgConfirmClick("")
                    dismiss()
                }
                // 不再显示
                R.id.tvBtn -> {
                    SpUtil.getInstance()
                        .saveBooleanToSp(ConsCommon.SLEEP_MONITORING_TIPS_NOT_SHOW, true)
                    dismiss()
                }
                else -> {}
            }
        }

    }

    override fun dismiss() {
        super.dismiss()
        mOnClickViewListener = null
    }

}