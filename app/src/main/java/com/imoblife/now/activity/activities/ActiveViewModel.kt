package com.imoblife.now.activity.activities

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.FoundCourse

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-09-14
 * 描   述：ActivitiesViewModel
 */
class ActiveViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy { ActiveRepository() }

    private val _foundCourse = MutableLiveData<UiStatus<List<FoundCourse>>>()
    val foundCourse: LiveData<UiStatus<List<FoundCourse>>> = _foundCourse

    fun getActivityData(type: Int) {
        mRepository.getActiveData(_foundCourse, type, initPage = true)
    }

    fun getActivityMoreData(type: Int) {
        mRepository.getActiveData(_foundCourse, type, initPage = false)
    }

}