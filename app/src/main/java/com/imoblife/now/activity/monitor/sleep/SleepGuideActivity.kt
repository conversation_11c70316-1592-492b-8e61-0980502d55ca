package com.imoblife.now.activity.monitor.sleep

import android.animation.Animator
import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.SleepMonitorViewModel
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.ActivitySleepGuideBinding
import com.imoblife.now.enums.GuideType
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.repository.DeviceGuideRepository
import com.imoblife.now.util.SpUtil

/**
 * 睡眠监测引导页
 */
class SleepGuideActivity : BaseVMActivity<SleepMonitorViewModel>() {
    companion object{
        fun startSleep(context: Context){
            if (ConfigMgr.getInstance().config.device_dialog!=null && ConfigMgr.getInstance().config.device_dialog.sleep_guide==0){
                var isGuideSleep=SpUtil.getInstance().getBoolenValue(ConsSp.SP_KEY_ENTER_SLEEP_GUIDE,false)
                if (isGuideSleep){
                    SleepMonitorActivity.startSleepMonitor(context)
                    DeviceGuideRepository().saveLastGuide(GuideType.SLEEP_GUIDE.value)
                }else{
                    Intent(context,SleepGuideActivity::class.java).apply {
                        context.startActivity(this)
                    }
                }
            }else{
                SleepMonitorActivity.startSleepMonitor(context)
            }
        }
    }
    private var isLast=false
    private lateinit var mBind:ActivitySleepGuideBinding
    override fun getLayoutResId(): Int {
        return R.layout.activity_sleep_guide
    }
    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentBar().init()
    }
    override fun superInit(intent: Intent?) {}
    override fun initVM() = ViewModelProvider(this).get(SleepMonitorViewModel::class.java)

    override fun initView() {
        mBind= mBinding as ActivitySleepGuideBinding
        mBind.skipTxt.setOnClickListener {
            SleepMonitorActivity.startSleepMonitor(this)
            finish()
        }
        mBind.sleepMonitorNextTxt.setOnClickListener {
            if (isLast){
                SleepMonitorActivity.startSleepMonitor(this)
                finish()
            }else{
                mBind.sleepMonitorNextTxt.text=getString(R.string.string_sleep_guide_start)
                isLast=true
                mBind.sleepMonitorDesTxt1.animate().alpha(0f)
                    .setDuration(300)
                    .setListener(object : Animator.AnimatorListener {
                        override fun onAnimationStart(animator: Animator) {}
                        override fun onAnimationEnd(animator: Animator) {
                            mBind.sleepMonitorDesTxt1.visibility=View.GONE
                            mBind.sleepMonitorDesTxt2.animate().alpha(1f)
                                .setDuration(100)
                                .setListener(object : Animator.AnimatorListener {
                                    override fun onAnimationStart(animation: Animator) {}
                                    override fun onAnimationEnd(animation: Animator) {
                                        mBind.sleepMonitorDesTxt2.visibility=View.VISIBLE
                                    }
                                    override fun onAnimationCancel(animation: Animator) {}
                                    override fun onAnimationRepeat(animation: Animator) {}
                                }).start()
                        }
                        override fun onAnimationCancel(animator: Animator) {}
                        override fun onAnimationRepeat(animator: Animator) {}
                    }).start()
            }
        }
    }

    override fun initData() {
        SpUtil.getInstance().saveBooleanToSp(ConsSp.SP_KEY_ENTER_SLEEP_GUIDE,true)
        DeviceGuideRepository().saveLastGuide(GuideType.SLEEP_GUIDE.value)
    }

    override fun startObserve() {
    }

    override fun onBackPressed() {}
}