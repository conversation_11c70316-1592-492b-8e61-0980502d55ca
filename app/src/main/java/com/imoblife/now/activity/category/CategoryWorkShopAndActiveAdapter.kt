package com.imoblife.now.activity.category

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.util.ImageLoader
import com.makeramen.roundedimageview.RoundedImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - active - 活动 & 首页 - 金刚区 - 分类导航 - workshop - 正念研习社
 */
class CategoryWorkShopAndActiveAdapter :
    BaseQuickAdapter<CategoryNavigationItemEntity, BaseViewHolder>(R.layout.layout_active_item_view) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationItemEntity?) {
        item?.apply {
            val activeItemBgImg = holder.getView<RoundedImageView>(R.id.active_item_bg_img)
            val activeItemBgImgFinishGroup =
                holder.getView<Group>(R.id.active_item_bg_img_finish_group)
            val activeItemPlanTxt = holder.getView<TextView>(R.id.active_item_plan_txt)
            val activeItemSignTxt = holder.getView<TextView>(R.id.active_item_sign_txt)
            val activeItemTitle = holder.getView<TextView>(R.id.active_item_title)
            val activeItemDaysTxt = holder.getView<TextView>(R.id.active_item_days_txt)

            ImageLoader.loadImageUrl(mContext, banner, activeItemBgImg)
            activeItemBgImgFinishGroup.visibility = if (status == 3) View.VISIBLE else View.GONE
            if (!TextUtils.isEmpty(label)) {
                activeItemPlanTxt.visibility = View.VISIBLE
                activeItemPlanTxt.text = label
            } else {
                activeItemPlanTxt.visibility = View.GONE
            }
            activeItemTitle.text = title
            // 1.报名中，显示报名还剩XX多少天 · XX人参加
            // 2.进行中，显示XX人参加
            when (status) {
                1 -> {
                    activeItemDaysTxt.text =
                        if (TextUtils.isEmpty(tex)) baoming else "$tex · $baoming"
                }
                2 -> {
                    if (TextUtils.isEmpty(baoming)) {
                        activeItemDaysTxt.visibility = View.GONE
                    } else {
                        activeItemDaysTxt.visibility = View.VISIBLE
                        activeItemDaysTxt.text = baoming
                    }
                }
            }
            if (TextUtils.isEmpty(subtitle)) {
                activeItemSignTxt.visibility = View.GONE
            } else {
                if (status == 3) {
                    activeItemSignTxt.visibility = View.INVISIBLE
                    return
                }
                activeItemSignTxt.visibility = View.VISIBLE
                activeItemSignTxt.text = subtitle
            }
            holder.itemView.setOnClickListener { pageRoute(mContext) }
        }
    }

}