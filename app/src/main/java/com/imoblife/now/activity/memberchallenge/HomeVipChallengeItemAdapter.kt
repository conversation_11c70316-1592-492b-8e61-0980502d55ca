package com.imoblife.now.activity.memberchallenge

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.MemberChallengeEntity
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.databinding.LayoutViewItemHomeVipChallengeRoundBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.ToastUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/7/22
 * 描   述：首页会员 - 会员挑战赛 - ItemRv - Adapter
 */
class HomeVipChallengeItemAdapter(private val mSubscribe: Subscribe) :
    BaseQuickAdapter<MemberChallengeEntity.ListEntity, BaseViewHolder>(R.layout.layout_view_item_home_vip_challenge_round) {

    private var mBlockSkuDialog: ((subscribe: Subscribe?) -> Unit)? = null

    fun setClickListener(block: ((subscribe: Subscribe?) -> Unit)?) {
        mBlockSkuDialog = block
    }

    private var mBlock: ((position: Int, entity: MemberChallengeEntity.ListEntity) -> Unit)? = null

    fun setClickJoinListener(block: ((position: Int, entity: MemberChallengeEntity.ListEntity) -> Unit)?) {
        mBlock = block
    }

    override fun convert(holder: BaseViewHolder, item: MemberChallengeEntity.ListEntity) {
        holder.getBinding(LayoutViewItemHomeVipChallengeRoundBinding::bind).let { layout ->
            layout.apply {
                ImageLoader.loadImageUrl(mContext, item.icon, img)
                tvContent.text = item.title
                root.setOnClickListener {
                    item.joinChallenge(holder.layoutPosition, mSubscribe)
                }
            }
        }
    }

    /**
     * 加入挑战赛
     */
    private fun MemberChallengeEntity.ListEntity.joinChallenge(
        position: Int,
        subscribe: Subscribe?
    ) {
        when {
            !UserMgr.getInstance().isLogin -> LoginCenter.getInstance().loginControl(mContext)
            !UserMgr.getInstance().isHasNowVip -> mBlockSkuDialog?.invoke(subscribe)
            else -> {
                when (status) {
                    0 -> ToastUtils.showShortToastCenter(mContext.getString(R.string.string_unlock_challenge))
                    1, 3 -> mBlock?.invoke(position, this)
                    else -> {}
                }
            }
        }
    }

}