package com.imoblife.now.activity.breath

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.adapter.MoodCourseAdapter
import com.imoblife.now.databinding.LayoutAcBreathStaticsBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.view.GridSpaceItemDecoration
import com.jaychang.st.SimpleText

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/10/16
 * 描   述：呼吸「历史」
 */
class BreathStaticsActivity : BaseVMActivity<BreathViewModel>() {

    companion object {

        fun startActivity(context: Context) {
            Intent(context, BreathStaticsActivity::class.java).apply {
                context.startActivity(this)
            }
        }

    }

    private lateinit var mBind: LayoutAcBreathStaticsBinding

    private val mBreathStaticsTopDateAdapter by lazy(LazyThreadSafetyMode.NONE) { BreathStaticsTopDateAdapter() }

    private val mRecommendCourseAdapter by lazy(LazyThreadSafetyMode.NONE) { MoodCourseAdapter("呼吸") }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_breath_statics

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[BreathViewModel::class.java]

    override fun initView() {
        mBind = mBinding as LayoutAcBreathStaticsBinding
        mBind.apply {
            imgBack.onDebounceClickListener { onBackPressed() }
            rvGridDate.adapter = mBreathStaticsTopDateAdapter
            rvRecommendCourse.addItemDecoration(GridSpaceItemDecoration(2, 14.dp, 14.dp))
            rvRecommendCourse.adapter = mRecommendCourseAdapter
        }
    }

    override fun initData() {
        mViewModel.getBreathStatics()
    }

    @SuppressLint("SetTextI18n")
    override fun startObserve() {
        mViewModel.breathStatics.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    mBind.apply {
                        // 今日呼吸时长
                        tvBreathStatics.text = "${entity.today_breath_time}分钟"
                        // 累计次数
                        tvAccumulatedTimes.text = SimpleText
                            .from("${entity.total_times} 次")
                            .first("${entity.total_times}")
                            .size(20)
                            .bold()
                        // 累计时长
                        tvAccumulatedDuration.text = SimpleText
                            .from("${entity.total_duration} 分")
                            .first("${entity.total_duration}")
                            .size(20)
                            .bold()
                        // 累计天数
                        tvAccumulatedDay.text = SimpleText
                            .from("${entity.total_days} 天")
                            .first("${entity.total_days}")
                            .size(20)
                            .bold()
                        // 月份
                        tvMonth.text = entity.current_month
                        // 本月 - 日期 - 呼吸状态 - 分钟数
                        mBreathStaticsTopDateAdapter.setNewData(entity.list)
                        // 推荐课程
                        mRecommendCourseAdapter.setNewData(entity.recommend_course)
                    }
                }
            }
        }
    }

}