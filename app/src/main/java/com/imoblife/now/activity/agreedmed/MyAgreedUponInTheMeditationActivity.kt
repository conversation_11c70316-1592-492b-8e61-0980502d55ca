package com.imoblife.now.activity.agreedmed

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.SimpleItemAnimator
import com.drakeet.multitype.MultiTypeAdapter
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.Status
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.activity.ShareActivity
import com.imoblife.now.activity.course.CourseCategoryActivity
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.activity.wallet.AgreedPaymentActivity
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.delegate.*
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.MeditationClassCertificateEntity
import com.imoblife.now.bean.MeditationClassTeamEntity
import com.imoblife.now.bean.Space
import com.imoblife.now.bean.TitleEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.LayoutAcMyAgreedUponInTheMeditationBinding
import com.imoblife.now.databinding.LayoutAgreedMeditationSharePageBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.*
import com.imoblife.now.view.dialog.MeditationGraduateDialog
import com.jaychang.st.SimpleText
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import org.greenrobot.eventbus.EventBus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-13
 * 描   述：我的约定冥想
 */
class MyAgreedUponInTheMeditationActivity :
    BaseVMActivity<MyAgreedUponInTheMeditationViewModel>() {

    companion object {
        fun startActivity(context: Context, teamId: Int?) {
            Intent(context, MyAgreedUponInTheMeditationActivity::class.java).apply {
                putExtra(ConsCommon.MEDITATION_CLASS_TEAM_ID, teamId)
                context.startActivity(this)
            }
        }
    }

    private lateinit var mBind: LayoutAcMyAgreedUponInTheMeditationBinding

    private val mAdapter = MultiTypeAdapter()

    private var mItems = ArrayList<Any>()

    private lateinit var loadingHelper: LoadingHelper

    // 组队id
    private var mTeamId: Int? = 0

    // btn状态
    private var mBtnStatus: Int = -1

    // 1 是我所加入的冥想组队 0 不是
    private var mIsMyJoin: Int = -1

    // 毕业证书_view
    private var mBindShareView: LayoutAgreedMeditationSharePageBinding? = null

    // 分享_创建冥想班成功
    private val mAgreedUponInTheMeditationViewModel by viewModels<AgreedUponInTheMeditationViewModel>()

    override fun getLayoutResId() = R.layout.layout_ac_my_agreed_upon_in_the_meditation

    override fun superInit(intent: Intent?) {
        if (hasExtra(ConsCommon.MEDITATION_CLASS_TEAM_ID)) {
            mTeamId = intent?.getIntExtra(ConsCommon.MEDITATION_CLASS_TEAM_ID, 0)
        }
    }

    override fun initVM() = ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(
        MyAgreedUponInTheMeditationViewModel::class.java
    )

    override fun initView() {
        loadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_my_agreed_upon_in_the_meditation),
            NavIconType.BACK,
            R.menu.menu_share_right_black,
            true
        ) { item ->
            if (item.itemId == R.id.action_share) {
                mTeamId?.let { mAgreedUponInTheMeditationViewModel.getMeditationClassShareParams(it) }
            }
            true
        }
        mBind = mBinding as LayoutAcMyAgreedUponInTheMeditationBinding
        mAdapter.apply {
            register(SpaceViewProvider())
            register(TitleCenterDelegate())
            register(MeditationClassTeamEntity::class.java).to(
                DailyMeditationClassDelegate(),
                MeditationClassContentDelegate()
            ).withKotlinClassLinker { _, entity ->
                when (entity.type) {
                    0 -> DailyMeditationClassDelegate::class
                    else -> MeditationClassContentDelegate::class
                }
            }
            register(MeditationDescDelegate())
            register(MeditationRvProvider())
            items = mItems
        }
        mBind.apply {
            clickProxy = ClickProxy()
            recyclerView.apply {
                (recyclerView.itemAnimator as SimpleItemAnimator?)?.supportsChangeAnimations =
                    false
                adapter = mAdapter
                addItemDecoration(
                    CommonItemDecoration(
                        0,
                        DisplayUtil.dip2px(10f),
                        0,
                        0,
                        0,
                        DisplayUtil.dip2px(10f)
                    )
                )
            }
            smartRefreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    mTeamId?.let {
                        mViewModel.apply {
                            getMeditationClassDetail(it)
                            getCertificate(it)
                        }
                    }
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    mTeamId?.let { mViewModel.getMoreMeditationClassRankList(it) }
                }
            })
        }
    }

    override fun initData() {
        loadingHelper.apply {
            showLoadingView()
            setOnReloadListener {
                mTeamId?.let {
                    mViewModel.apply {
                        getMeditationClassDetail(it)
                        getCertificate(it)
                    }
                }
            }
        }
        mTeamId?.let {
            mViewModel.apply {
                getMeditationClassDetail(it)
                getCertificate(it)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.apply {
            // 冥想班详情
            meditationClassDetailEntity.observe(this@MyAgreedUponInTheMeditationActivity) {
                DialogUtil.hideWaitLoading()
                if (it.isSuccess) {
                    when (it.status) {
                        Status.REFRESHSUCCESS -> {
                            it.successData?.apply {
                                loadingHelper.showContentView()
                                mItems.clear()
                                team?.let { teamEntity ->
                                    mItems.add(Space(1f))
                                    // 0 我的约定冥想_详情
                                    teamEntity.type = 0
                                    mItems.add(teamEntity)
                                    val teamEntityClone =
                                        teamEntity.clone() as MeditationClassTeamEntity
                                    // 1 我的约定冥想_当前进度
                                    teamEntityClone.type = 1
                                    mItems.add(teamEntityClone)
                                    mIsMyJoin = teamEntity.is_my_join
                                    // btn状态
                                    mBind.stvGraduate.apply {
                                        if (teamEntity.my_status_title.isNullOrBlank()) {
                                            visibility = View.GONE
                                        } else {
                                            visibility = View.VISIBLE
                                            text =
                                                if (teamEntity.my_status_desc.isNullOrBlank()) {
                                                    teamEntity.my_status_title
                                                } else {
                                                    SimpleText
                                                        .from(
                                                            getString(
                                                                R.string.string_user_setting_text_subtext,
                                                                teamEntity.my_status_title,
                                                                teamEntity.my_status_desc
                                                            )
                                                        )
                                                        .first(teamEntity.my_status_desc)
                                                        .size(12)
                                                }
                                            mBtnStatus = teamEntity.my_status
                                            when (mBtnStatus) {
                                                // 即将开始 | 进行中_今日已打卡 | 已提前毕业 | 结束了,未完成 | 冥想班已开始
                                                0, 1, 5, 7, 8 -> {
                                                    isClickable = false
                                                    isEnabled = false
                                                    shaderStartColor =
                                                        ContextCompat.getColor(
                                                            this@MyAgreedUponInTheMeditationActivity,
                                                            R.color.color_DBDBDB
                                                        )
                                                    shaderEndColor =
                                                        ContextCompat.getColor(
                                                            this@MyAgreedUponInTheMeditationActivity,
                                                            R.color.color_B2B2B2
                                                        )
                                                }
                                                // 进行中_去打卡 | 加入冥想班 | 可提前毕业 | 查看毕业证
                                                2, 3, 4, 6 -> {
                                                    isClickable = true
                                                    isEnabled = true
                                                    shaderStartColor =
                                                        ContextCompat.getColor(
                                                            this@MyAgreedUponInTheMeditationActivity,
                                                            R.color.main_color
                                                        )
                                                    shaderEndColor =
                                                        ContextCompat.getColor(
                                                            this@MyAgreedUponInTheMeditationActivity,
                                                            R.color.color_user_tv_gift_app_name
                                                        )
                                                }
                                                else -> {
                                                }
                                            }
                                        }
                                    }
                                }
                                summary?.let { summaryEntity ->
                                    mItems.add(Space(10f))
                                    mItems.add(
                                        TitleEntity(
                                            getString(R.string.string_summary_of_Engagement_meditation),
                                            5
                                        )
                                    )
                                    mItems.add(Space(10f))
                                    mItems.add(MeditationClassTeamEntity().also { entity ->
                                        entity.apply {
                                            type = 2
                                            had_clock = summaryEntity.had_clock
                                            need_clock = summaryEntity.need_clock
                                            surplus_day = summaryEntity.join_days
                                            join_count = summaryEntity.success_user
                                            total_money = summaryEntity.surplus_money
                                        }
                                    })
                                }
                                if (!team?.description.isNullOrBlank()) mItems.add(team.description)
                                rank?.let { rankEntity ->
                                    rankEntity.isMyJoin = mIsMyJoin
                                    rankEntity.isLoadMore = false
                                    mItems.add(rankEntity)
                                }
                                mAdapter.notifyDataSetChanged()
                            } ?: loadingHelper.showEmptyView()
                            mBind.smartRefreshLayout.finishRefresh()
                        }
                        Status.MORESUCCESS -> {
                            it.successData?.rank?.let { rankEntity ->
                                rankEntity.isMyJoin = mIsMyJoin
                                rankEntity.isLoadMore = true
                                val size = mItems.size - 1
                                mItems[size] = rankEntity
                                mAdapter.notifyItemChanged(size)
                            }
                            mBind.smartRefreshLayout.finishLoadMore()
                        }
                        Status.NOMOREDATA -> {
                            mBind.smartRefreshLayout.finishLoadMoreWithNoMoreData()
                        }
                        Status.EMPTYDATA -> {
                            loadingHelper.showEmptyView()
                            mBind.smartRefreshLayout.finishRefreshWithNoMoreData()
                        }
                        else -> {
                        }
                    }
                } else {
                    when (it.status) {
                        Status.FAILED -> {
                            loadingHelper.showErrorView()
                            mBind.smartRefreshLayout.finishRefresh(false)
                        }
                        Status.MOREFAIL -> {
                            mBind.smartRefreshLayout.finishLoadMore(false)
                        }
                        else -> {
                        }
                    }
                }
            }

            // 提前毕业
            graduateEarly.observe(this@MyAgreedUponInTheMeditationActivity) {
                DialogUtil.hideWaitLoading()
                if (it.isSuccess) {
                    it.successData?.apply {
                        if (this) {
                            ToastUtils.showShortToast(getString(R.string.string_graduate_early_success))
                            // 刷新冥想详情数据
                            mTeamId?.let { id ->
                                DialogUtil.showWaitLoading()
                                mViewModel.getMeditationClassDetail(id)
                            }
                            // 通知刷新冥想列表数据
                            EventBus.getDefault()
                                .post(BaseEvent(ConsEventCode.REFRESH_MEDITATION_CLASS_DATA))
                        } else ToastUtils.showShortToast(
                            getString(R.string.string_graduate_early_failure)
                        )
                    }
                } else ToastUtils.showShortToast(getString(R.string.default_error))
            }

            // 毕业证书
            meditationClassCertificateEntity.observe(this@MyAgreedUponInTheMeditationActivity) {
                if (it.isSuccess) {
                    it.successData?.apply {
                        prepareShareView(this)
                    }
                }
            }

            // 加入组队
            meditationClassCreateEntity.observe(this@MyAgreedUponInTheMeditationActivity) {
                DialogUtil.hideWaitLoading()
                if (it.isSuccess) {
                    it.successData?.apply {
                        AgreedPaymentActivity.startAgreedPaymentActivity(
                            this@MyAgreedUponInTheMeditationActivity,
                            this
                        )
                    }
                }
            }

        }

        // 分享_创建冥想班成功
        mAgreedUponInTheMeditationViewModel.meditationClassShareParamsEntity.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    entity.apply {
                        ShareActivity.doShareUrl(
                            this@MyAgreedUponInTheMeditationActivity,
                            link,
                            title,
                            desc,
                            img_url
                        )
                    }
                }
            } else ToastUtils.showShortToast(getString(R.string.default_error))
        }
    }

    /**
     * 毕业证书
     * @param meditationClassCertificateEntity 毕业证书info
     */
    private fun prepareShareView(meditationClassCertificateEntity: MeditationClassCertificateEntity) {
        mBindShareView = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.layout_agreed_meditation_share_page,
            null,
            false
        )
        mBindShareView?.apply {
            entity = meditationClassCertificateEntity
            executePendingBindings()
            BitmapUtil.layoutView(
                root,
                DeviceUtil.getScreenSize(this@MyAgreedUponInTheMeditationActivity)[0],
                DeviceUtil.getScreenSize(this@MyAgreedUponInTheMeditationActivity)[1]
            )
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                R.id.stvGraduate -> {
                    if (mBtnStatus != -1) {
                        when (mBtnStatus) {
                            // 进行中_去打卡
                            2 -> {
                                CourseCategoryActivity.openCourseCategory(
                                    this@MyAgreedUponInTheMeditationActivity,
                                    pageId = ConsCommon.PAGE_ID_APPOINTMENT
                                )
                                <EMAIL>()
                            }
                            // 加入冥想班
                            3 -> {
                                if (!UserMgr.getInstance().isLogin) {
                                    LoginCenter.getInstance()
                                        .loginControl(this@MyAgreedUponInTheMeditationActivity)
                                } else {
                                    if (mIsMyJoin == 1) {
                                        ToastUtils.showShortToast(getString(R.string.string_you_have_joined_other_meditation_classes))
                                    } else {
                                        mTeamId?.let {
                                            DialogUtil.showWaitLoading()
                                            mViewModel.joinMeditationTeam(it)
                                        }
                                    }
                                }
                            }
                            // 可提前毕业
                            4 -> {
                                MeditationGraduateDialog().setMeditationGraduateClickListener {
                                    DialogUtil.showWaitLoading()
                                    mViewModel.graduateEarly()
                                }.showDialog(supportFragmentManager)
                            }
                            // 查看毕业证
                            6 -> {
                                Handler().post {
                                    mBindShareView?.root?.let {
                                        val bitmap = BitmapUtil.loadBitmapFromView(it)
                                        bitmap?.apply {
                                            ShareActivity.shareBitmap(
                                                this@MyAgreedUponInTheMeditationActivity,
                                                bitmap,
                                                false
                                            )
                                        }
                                    }
                                }
                            }
                            else -> {
                            }
                        }
                    }
                }
            }
        }

    }

}