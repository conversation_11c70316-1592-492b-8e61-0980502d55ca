package com.imoblife.now.activity.agreedmed.calendar

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import androidx.core.content.ContextCompat
import com.haibin.calendarview.Calendar
import com.haibin.calendarview.MonthView
import com.imoblife.now.R

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-14
 * 描   述：MeditationMonthView
 */
class MeditationMonthView(context: Context) : MonthView(context) {

    private var mRadius = 0

    override fun onPreviewHook() {
        mRadius = mItemWidth.coerceAtMost(mItemHeight) / 5 * 2
        mSchemePaint.style = Paint.Style.FILL
        mSchemeTextPaint.color = ContextCompat.getColor(context, R.color.white)
    }

    override fun onDrawSelected(
        canvas: Canvas?,
        calendar: Calendar?,
        x: Int,
        y: Int,
        hasScheme: Boolean,
    ) = true

    override fun onDrawScheme(canvas: Canvas?, calendar: Calendar?, x: Int, y: Int) {
        val cx = x + mItemWidth / 2
        val cy = y + mItemHeight / 2
        canvas?.drawCircle(cx.toFloat(), cy.toFloat(), mRadius.toFloat(), mSchemePaint)
    }

    override fun onDrawText(
        canvas: Canvas?,
        calendar: Calendar?,
        x: Int,
        y: Int,
        hasScheme: Boolean,
        isSelected: Boolean,
    ) {
        val baselineY = mTextBaseLine + y
        val cx = x + mItemWidth / 2

        canvas?.let {
            calendar?.apply {
                when {
                    hasScheme -> {
                        it.drawText(calendar.day.toString(),
                            cx.toFloat(),
                            baselineY,
                            mSchemeTextPaint)
                    }
                    else -> {
                        it.drawText(calendar.day.toString(), cx.toFloat(), baselineY,
                            if (calendar.isCurrentDay) mCurDayTextPaint else if (calendar.isCurrentMonth) mCurMonthTextPaint else mOtherMonthTextPaint)
                    }
                }
            }
        }
    }

}