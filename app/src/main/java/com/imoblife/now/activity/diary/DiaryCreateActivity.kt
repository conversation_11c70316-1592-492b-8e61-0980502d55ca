package com.imoblife.now.activity.diary

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.Diary
import com.imoblife.now.bean.DiaryCreate
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityDiaryCreateBinding
import com.imoblife.now.enums.UserAction
import com.imoblife.now.model.UserMgr
import com.imoblife.now.util.NetworkUtils
import com.imoblife.now.util.ToastUtils

class DiaryCreateActivity : BaseVMActivity<DiaryViewModel>(), View.OnClickListener {

    companion object {
        //书写日志
        const val LOG_TYPE_DIARY = "diary"

        @JvmStatic
        @JvmOverloads
        fun startDiaryCreateActivity(
            context: Context,
            courseId: Int = 0,
            sectionId: Int = 0,
            title: String = "我的日记",
            logType: String = LOG_TYPE_DIARY,
            diaryId: Int = 0,
            diaryContent: String = ""
        ) {
            Intent(context, DiaryCreateActivity::class.java).let {
                it.putExtra(ConsIntent.BUNDLE_COURSE_ID, courseId)
                it.putExtra(ConsIntent.BUNDLE_SECTION_ID, sectionId)
                it.putExtra(ConsIntent.BUNDLE_TITLE, title)
                it.putExtra(ConsIntent.BUNDLE_LOG_TYPE, logType)
                it.putExtra(ConsIntent.BUNDLE_DIARY_ID, diaryId)
                it.putExtra(ConsIntent.BUNDLE_DIARY_CONTENT, diaryContent)
                context.startActivity(it)
            }
        }

    }

    private var mCourseId: Int = 0
    private var mSectionId = 0
    private var mTitle: String = "我的日记"
    private var mLogType: String = LOG_TYPE_DIARY
    private var diaryId: Int = 0
    private var diaryContent: String = ""

    private lateinit var mBind: ActivityDiaryCreateBinding

    override fun getLayoutResId(): Int = R.layout.activity_diary_create

    override fun superInit(intent: Intent?) {
        mCourseId = intent?.getIntExtra(ConsIntent.BUNDLE_COURSE_ID, mCourseId) ?: mCourseId
        mSectionId = intent?.getIntExtra(ConsIntent.BUNDLE_SECTION_ID, mSectionId) ?: mSectionId
        mTitle = intent?.getStringExtra(ConsIntent.BUNDLE_TITLE) ?: mTitle
        mLogType = intent?.getStringExtra(ConsIntent.BUNDLE_LOG_TYPE) ?: mLogType
        diaryId = intent?.getIntExtra(ConsIntent.BUNDLE_DIARY_ID, diaryId) ?: diaryId
        diaryContent = intent?.getStringExtra(ConsIntent.BUNDLE_DIARY_CONTENT) ?: diaryContent
    }

    override fun initVM() = ViewModelProvider(this).get(DiaryViewModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityDiaryCreateBinding
        mBind.apply {
            commonTitle.apply {
                titleContentText.text = getString(R.string.diary_title)
                titleBackImg.setOnClickListener(this@DiaryCreateActivity)
                titleMoreTxt.let {
                    it.setTextColor(Color.parseColor("#C1C1C1"))
                    it.text = getString(R.string.save)
                }
                titleMoreTxt.setOnClickListener(this@DiaryCreateActivity)
                edtDiaryContent.addTextChangedListener(object : TextWatcher {
                    override fun afterTextChanged(s: Editable?) {}
                    override fun beforeTextChanged(
                        s: CharSequence?,
                        start: Int,
                        count: Int,
                        after: Int
                    ) {
                    }

                    override fun onTextChanged(
                        s: CharSequence?,
                        start: Int,
                        before: Int,
                        count: Int
                    ) {
                        if (edtDiaryContent.text.isNotEmpty()) {
                            if (edtDiaryContent.text.length > 3000) {
                                ToastUtils.showShortToastCenter(getString(R.string.string_the_maximum_word_limit_has_been_exceeded))
                            }
                            titleMoreTxt.setTextColor(Color.parseColor("#66ABFE"))
                        } else {
                            titleMoreTxt.setTextColor(Color.parseColor("#C1C1C1"))
                        }
                    }
                })
            }
        }
    }

    override fun initData() {
        if (diaryId != 0) {
            mBind.edtDiaryContent.setText(diaryContent)
        }
    }

    override fun startObserve() {
        mViewModel.let { viewModel ->
            viewModel.userActionLog.observe(this) {
                if (it.isSuccess) {
                    ToastUtils.showLongToast(getString(R.string.life_memories))
                    val diary = Diary()
                    diary.create_time = (System.currentTimeMillis() / 1000).toInt()
                    diary.content = mBind.edtDiaryContent.text.toString()
                    diary.title = ""
                    diary.id = diaryId
                    viewModel.mDiaryChangeLiveData.value = diary
                    finish()
                    // 学习计划 - Task
                    UserMgr.getInstance().queryStudyPlanTime()
                } else {
                    if (NetworkUtils.isNetworkAvailable()) {
                        it.failureData?.let { failureData ->
                            ToastUtils.showLongToast(failureData)
                        }
                    } else {
                        ToastUtils.showLongToast(getString(R.string.network_connection_error))
                    }
                }
            }
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.title_back_img -> {
                finish()
            }
            R.id.title_more_txt -> {
                if (UserMgr.getInstance().isLogin) {
                    if (diaryId == 0) {
                        submitDiary()
                    } else {
                        editDiary()
                    }
                } else {
                    ToastUtils.showShortToastCenter(getString(R.string.string_save_diary_tip))
                    LoginCenter.getInstance().loginControl(this, LoginCenter.LoginStyleDialog)
                }
            }
        }
    }

    private fun submitDiary() {
        val diaryContent = mBind.edtDiaryContent.text.toString()
        if (TextUtils.isEmpty(diaryContent)) {
            return
        }
        val diaryCreateBean = DiaryCreate()
        diaryCreateBean.course_id = mCourseId.toString()
        diaryCreateBean.section_id = mSectionId.toString()
        diaryCreateBean.log_type = mLogType
        diaryCreateBean.content = diaryContent
        diaryCreateBean.title = mTitle
        val data = mutableListOf<DiaryCreate>()
        data.add(diaryCreateBean)
        val diaryCreateJson = Gson().toJson(data)
        mViewModel.userActionLog(UserAction.Diary.value, diaryCreateJson)
    }

    private fun editDiary() {
        val diaryContent = mBind.edtDiaryContent.text.toString()
        if (TextUtils.isEmpty(diaryContent)) {
            return
        }
        mViewModel.editDiary(diaryId, diaryContent)
    }

}