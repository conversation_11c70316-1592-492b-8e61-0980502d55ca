package com.imoblife.now.activity.memberchallenge

import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import com.imoblife.now.R
import com.imoblife.now.activity.member.SubscribeActivity
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.databinding.LayoutDialogMemberChallengeSkuBinding
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.util.ChannelUtils
import com.imoblife.now.view.dialog.BaseBottomSheetDialog
import com.jaychang.st.SimpleText

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/20
 * 描   述：会员挑战 - 挑战未知的自己 - sku - dialog
 */
class MemberChallengeSkuDialog(private var mSubscribe: Subscribe) : BaseBottomSheetDialog(true) {

    private lateinit var mBind: LayoutDialogMemberChallengeSkuBinding

    override val viewId = R.layout.layout_dialog_member_challenge_sku

    override fun initView() {
        mBind = mBinding as LayoutDialogMemberChallengeSkuBinding
        mBind.apply {
            val first = "￥${mSubscribe.display_price}"
            val second = mSubscribe.subtitle
            val third = "￥${mSubscribe.price}"
            val four = "\n${mSubscribe.title}"
            tvContent.text = SimpleText.from(first + second + third + four)
                .first(first)
                .textColor(R.color.color_vip_start)
                .size(16)
                .bold()
                .first(second)
                .textColor(R.color.color_vip_start)
                .size(12)
                .first(third)
                .strikethrough()
                .size(12)
                .textColor(R.color.color_8d8d8d8)
                .first(four)
                .size(10)
//            if (ChannelUtils.isHuaWeiChannel()) {
//                payWayView.visibility = View.GONE
//            } else {
//                payWayView.visibility = View.VISIBLE
//            }
            payWayView.visibility = View.GONE
            imgClose.setOnClickListener { dismiss() }
            tvContent.setOnClickListener {
                // 页面来源 - 挑战赛 pageOrigin 为 4
                SubscribeActivity.openSubscribeActivity(requireActivity(), pageOrigin = 4)
                dismiss()
            }
            stvBtn.setOnClickListener {
//                if (payWayView.isVisible) mSubscribe.pay_type = payWayView.getPayWay()
//                PayCenter.getInstance()
//                    .doSubmitPay(requireActivity() as AppCompatActivity, mSubscribe)
//                dismiss()
                // 页面来源 - 挑战赛 pageOrigin 为 4
                SubscribeActivity.openSubscribeActivity(requireActivity(), pageOrigin = 4)
                dismiss()
            }
        }
    }

    fun show(context: AppCompatActivity) {
        context.let { show(it.supportFragmentManager, it.javaClass.simpleName) }
    }

}