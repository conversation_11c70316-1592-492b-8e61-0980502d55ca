package com.imoblife.now.activity.monitor.history

import android.content.Context
import androidx.constraintlayout.widget.ConstraintLayout
import com.drakeet.multitype.ViewDelegate
import com.imoblife.now.bean.AllSleepHistoryEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/28
 * 描   述：所有睡眠记录 - ContentDelegate
 */
class AllSleepHistoryContentDelegate :
    ViewDelegate<AllSleepHistoryEntity.ListEntity, AllSleepHistoryContentItemView>() {

    override fun onBindView(view: AllSleepHistoryContentItemView, item: AllSleepHistoryEntity.ListEntity) {
        view.setItemData(item)
    }

    override fun onCreateView(context: Context) = AllSleepHistoryContentItemView(context).also {
        val constraintLayout =
            ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
        it.layoutParams = constraintLayout
    }

}