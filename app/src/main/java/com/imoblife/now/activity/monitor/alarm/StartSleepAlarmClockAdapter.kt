package com.imoblife.now.activity.monitor.alarm

import android.widget.SeekBar
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.StartSleepAlarmClockCallBack
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.LayoutViewStartSleepAlarmClockBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.util.SpUtil

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：开启 - 睡眠闹钟 - Adapter
 */
class StartSleepAlarmClockAdapter :
    BaseQuickAdapter<String, BaseViewHolder>(R.layout.layout_view_start_sleep_alarm_clock) {

    private var mCallBack: StartSleepAlarmClockCallBack? = null

    fun setStartSleepAlarmClockCallBack(callback: StartSleepAlarmClockCallBack) {
        mCallBack = callback
    }

    override fun convert(holder: BaseViewHolder, item: String) {
        holder.getBinding(LayoutViewStartSleepAlarmClockBinding::bind).apply {
            checkBox.isChecked =
                SpUtil.getInstance().getBoolenValue(ConsCommon.SLEEP_ALARM_CLOCK_CHECK_STATE, true)
            checkBox.setOnCheckedChangeListener { _, isChecked ->
                mCallBack?.checkBoxIsChecked(isChecked)
            }
            seekBar.progress = (SpUtil.getInstance()
                .getFloatValue(ConsCommon.SLEEP_ALARM_CLOCK_SOUND_SIZE, 0.5F) * 100).toInt()
            seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean
                ) {
                    mCallBack?.seekBarProgressChanged(progress)
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                }
            })
        }
    }

}