package com.imoblife.now.activity.course;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.vectordrawable.graphics.drawable.Animatable2Compat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.integration.webp.decoder.WebpDrawable;
import com.bumptech.glide.integration.webp.decoder.WebpDrawableTransformation;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CenterInside;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.imoblife.commlibrary.base.BaseEvent;
import com.imoblife.commlibrary.mvp.CreatePresenter;
import com.imoblife.commlibrary.utils.LoadingHelper;
import com.imoblife.now.R;
import com.imoblife.now.activity.base.MvpBaseActivity;
import com.imoblife.now.adapter.CourseVLStyleAdapter;
import com.imoblife.now.adapter.decoration.CommonItemDecoration;
import com.imoblife.now.adapter.loading.NavIconType;
import com.imoblife.now.adapter.loading.ToolbarUtils;
import com.imoblife.now.bean.Course;
import com.imoblife.now.bean.MedTypeTitle;
import com.imoblife.now.constant.ConsClickOrigin;
import com.imoblife.now.constant.ConsEventCode;
import com.imoblife.now.constant.ConsIntent;
import com.imoblife.now.constant.ConsSp;
import com.imoblife.now.databinding.ActivityCourseListBinding;
import com.imoblife.now.mvp_contract.CourseListContract;
import com.imoblife.now.mvp_presenter.CourseListPresenter;
import com.imoblife.now.util.DisplayUtil;
import com.imoblife.now.util.SpUtil;
import com.imoblife.now.view.EmptyViewUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

@CreatePresenter(presenter = CourseListPresenter.class)
public class CourseListActivity extends MvpBaseActivity<CourseListPresenter> implements CourseListContract.ICourseListView {

    private ActivityCourseListBinding mBind;

    private CourseVLStyleAdapter mAdapter;
    private MedTypeTitle mMedTypeTitle;
    private int catId;
    private int mType;
    private String mTitle;
    public static final int LISTEN = 0;//最近收听
    public static final int CATEGORY = 1;//类别
    public static final int MICRO = 2;//微课
    private String origin;
    private LoadingHelper loadingHelper;

    // 神策数据 - 当前页面id
    private int mPageId;
    // 神策数据 - 当前页面 - 分类id
    private int mCategoryId;

    // 停留在最近收听 - 最近播放 ｜ 我常收听 tab
    private int mLastPosition = 0;

    public static void openCourseListActivity(Context context, int type, MedTypeTitle medTypeTitle, int pageId, int categoryId) {
        Intent intent = new Intent(context, CourseListActivity.class);
        intent.putExtra(ConsIntent.COURSE_LIST_TYPE, type);
        intent.putExtra(ConsIntent.MED_TYPE_TITLE, medTypeTitle);
        intent.putExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, pageId);
        intent.putExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, categoryId);
        context.startActivity(intent);
    }

    @Override
    protected int setContentViewId() {
        return 0;
    }

    @Override
    protected void superInit(Intent intent) {
        super.superInit(intent);
        if (hasExtra(ConsIntent.COURSE_LIST_TYPE)) {
            mType = intent.getIntExtra(ConsIntent.COURSE_LIST_TYPE, -1);
        }
        if (hasExtra(ConsIntent.MED_TYPE_TITLE)) {
            mMedTypeTitle = (MedTypeTitle) getIntent().getSerializableExtra(ConsIntent.MED_TYPE_TITLE);
        }
        if (mMedTypeTitle != null) {
            catId = mMedTypeTitle.getId();
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID)) {
            mPageId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_PAGE_ID, 0);
        }
        if (hasExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID)) {
            mCategoryId = intent.getIntExtra(ConsIntent.BUNDLE_COURSE_CATEGORY_ID, 0);
        }
        if (CATEGORY == mType && mMedTypeTitle != null) {
            origin = ConsClickOrigin.COURSE_PAGE_ORIGIN_MED + mMedTypeTitle.getId();
            mTitle = mMedTypeTitle.getTitle();
        } else if (LISTEN == mType) {
            mTitle = "最近收听";
            origin = ConsClickOrigin.COURSE_PAGE_ORIGIN_LISTENING;
        } else if (MICRO == mType) {
            mTitle = "微课";
            origin = ConsClickOrigin.COURSE_PAGE_ORIGIN_found;
        }
    }

    @Override
    protected void initDataBinding() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_course_list);
        mBind.setLifecycleOwner(this);
    }

    @Override
    protected void initView() {
        loadingHelper = ToolbarUtils.setToolbar(this, mTitle, NavIconType.BACK);
        if (mType == LISTEN) {
            mBind.llContainer.setVisibility(View.VISIBLE);
            selectRecentPlay(true);
            mAdapter = new CourseVLStyleAdapter(origin, LISTEN);
            mAdapter.setRemovePlayingLog(courseId -> {
                getPresenter().removePlayingLog(courseId);
                // 最近收听移除课程 => 刷新首页练习 - 最近收听课程（底部3个圆UI）
                EventBus.getDefault().post(new BaseEvent(ConsEventCode.REFRESH_HOME_PRACTICE_FROM_LISTEN_COURSE_LIST));
            });
            // LISTEN - type - 最近收听
            mBind.tvRecentPlay.setOnClickListener(v -> {
                getPresenter().getListenRecord(mPageId, mCategoryId, 0);
                selectRecentPlay(true);
            });
            // LISTEN - type - 我常收听
            mBind.tvOftenListen.setOnClickListener(v -> {
                getPresenter().getListenRecord(mPageId, mCategoryId, 1);
                selectRecentPlay(false);
            });
        } else {
            mBind.llContainer.setVisibility(View.GONE);
            mAdapter = new CourseVLStyleAdapter(origin);
        }
        mAdapter.setParentCategoryName(mTitle);
        mAdapter.setCategoryName(mTitle);
        mBind.recycler.setAdapter(mAdapter);
        mBind.recycler.addItemDecoration(new CommonItemDecoration(0, DisplayUtil.dip2px(18), 0, DisplayUtil.dip2px(18), 0, DisplayUtil.dip2px(18)));
        mBind.smartRefresh.setOnRefreshListener(refreshLayout -> getData());
    }

    /**
     * LISTEN - type
     * 最近收听 & 我常收听
     *
     * @param bool 是否选中最近收听
     */
    private void selectRecentPlay(boolean bool) {
        if (bool) {
            mLastPosition = 0;
            mBind.tvRecentPlay.setTextColor(ContextCompat.getColor(mContext, R.color.black100));
            mBind.stvRecentPlay.setVisibility(View.VISIBLE);
            mBind.tvOftenListen.setTextColor(ContextCompat.getColor(mContext, R.color.color_BBBBBB));
            mBind.stvOftenListen.setVisibility(View.GONE);
        } else {
            mLastPosition = 1;
            mBind.tvRecentPlay.setTextColor(ContextCompat.getColor(mContext,R.color.color_BBBBBB));
            mBind.stvRecentPlay.setVisibility(View.GONE);
            mBind.tvOftenListen.setTextColor(ContextCompat.getColor(mContext,R.color.black100));
            mBind.stvOftenListen.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void initData() {
        loadingHelper.showLoadingView();
        getData();
    }

    private void getData() {
        if (CATEGORY == mType && mMedTypeTitle != null) {
            getPresenter().getCourseList(catId, mPageId, mCategoryId);
        } else if (LISTEN == mType) {
            getPresenter().getListenRecord(mPageId, mCategoryId, mLastPosition);
        } else if (MICRO == mType) {
            getPresenter().getMicroCourseList(mPageId, mCategoryId);
        }
    }


    public void hideRefresh() {
        if (mBind.smartRefresh.isRefreshing()) {
            mBind.smartRefresh.finishRefresh();
        }
    }

    @Override
    public void getCourseListSuccess(List<Course> data) {
        hideRefresh();
        loadingHelper.showContentView();
        if (data == null || data.size() == 0) {
            mAdapter.setEmptyView(EmptyViewUtils.getEmptyRecentlyPlayedView(this, v -> {
                loadingHelper.showLoadingView();
                getData();
            }));
        } else {
            mAdapter.setNewData(data);
        }
        // 最近收听显示 - 删除引导
        if (mType == LISTEN) {
            imgGuideDeleteOnce();
        }
    }

    /**
     * 删除引导只动一次
     */
    private void imgGuideDeleteOnce() {
        if (SpUtil.getInstance().getBoolenValue(ConsSp.SP_KEY_COURSE_LIST_DELETE_GUIDE, true)) {
            mBind.imgGuideDelete.setVisibility(View.VISIBLE);
            Glide
                    .with(this)
                    .load(R.mipmap.img_drag_start_delete_guide)
                    .optionalTransform(WebpDrawable.class, new WebpDrawableTransformation(new CenterInside()))
                    .addListener(new RequestListener<Drawable>() {
                        @Override
                        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                            WebpDrawable webpDrawable = (WebpDrawable) resource;
                            webpDrawable.setLoopCount(1);
                            webpDrawable.registerAnimationCallback(new Animatable2Compat.AnimationCallback() {
                                @Override
                                public void onAnimationEnd(Drawable drawable) {
                                    super.onAnimationEnd(drawable);
                                    webpDrawable.unregisterAnimationCallback(this);
                                    mBind.imgGuideDelete.setVisibility(View.GONE);
                                    SpUtil.getInstance().saveBooleanToSp(ConsSp.SP_KEY_COURSE_LIST_DELETE_GUIDE, false);
                                }
                            });
                            return false;
                        }
                    })
                    .into(mBind.imgGuideDelete);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BaseEvent event) {
        if (isFinishing() || event == null) {
            return;
        }
        // 登陆状态更新｜退出登陆｜收藏状态 => 刷新 - 睡眠 - 大自然声
        if (event.getEventCode() == ConsEventCode.NATURAL_SOUND_COLLECT_CHANGE_EVENT
                || event.getEventCode() == ConsEventCode.LOGIN_CHANGE_EVENT
                || event.getEventCode() == ConsEventCode.LOGIN_OUT_SUCCESS
        ) {
            getData();
        }
    }

}
