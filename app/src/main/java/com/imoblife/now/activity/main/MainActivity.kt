package com.imoblife.now.activity.main

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Looper
import android.os.PersistableBundle
import android.text.TextUtils
import android.view.Gravity
import androidx.activity.viewModels
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.viewpager.widget.ViewPager
import com.flyco.tablayout.SlidingTabLayout
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.ActivityStackManager
import com.imoblife.now.MyApplication
import com.imoblife.now.R
import com.imoblife.now.activity.memberchallenge.MemberChallengeActivity
import com.imoblife.now.activity.memberchallenge.MemberChallengeViewModel
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.ChallengeToastEntity
import com.imoblife.now.bean.TabBean
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.ActivityMainBinding
import com.imoblife.now.databinding.LayoutViewMainMeditationTipBinding
import com.imoblife.now.enums.LoginStateType
import com.imoblife.now.fragment.home.HomeFragment
import com.imoblife.now.fragment.home.PracticeFragment
import com.imoblife.now.fragment.meditation.MeditationTabFragment
import com.imoblife.now.fragment.mine.UserFragment
import com.imoblife.now.fragment.sleep.SleepTabFragment
import com.imoblife.now.hms.HmsManager
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserActionLogMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.player.PlayCenter
import com.imoblife.now.push.MobPushCenter
import com.imoblife.now.repository.DeviceGuideRepository
import com.imoblife.now.repository.NoticeRepository
import com.imoblife.now.repository.UserRepository
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.ChannelUtils
import com.imoblife.now.util.CommonUtil
import com.imoblife.now.util.DateUtil
import com.imoblife.now.util.DeviceUtil
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.HuaWeiSubcontractingAttributionUtils
import com.imoblife.now.util.NetworkUtils
import com.imoblife.now.util.SpUtil
import com.imoblife.now.util.TimeUtils.getCurrentDate
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.util.UnicornManager
import com.imoblife.now.util.optimizefgplusvp.BlankPlaceHolderFragment
import com.imoblife.now.util.optimizefgplusvp.TitlePageNewAdapter
import com.imoblife.now.view.CustomTabLayout
import com.imoblife.now.view.dialog.AppUpdateReminderDialog
import com.imoblife.now.viewmodel.AdViewModel
import com.qiyukf.nimlib.sdk.NimIntent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import per.goweii.anylayer.AnyLayer
import per.goweii.anylayer.dialog.DialogLayer
import per.goweii.anylayer.ktx.setAlign
import per.goweii.anylayer.ktx.setAnimStyle
import per.goweii.anylayer.ktx.setContentView
import per.goweii.anylayer.ktx.setGravity
import per.goweii.anylayer.popup.PopupLayer
import java.lang.ref.WeakReference
import java.util.Collections

// 首页
const val PAGE_HOME_INDEX = 0

// 练习
const val PAGE_PRACTICE_INDEX = 1

// 睡眠
const val PAGE_SLEEP_INDEX = 2

// 冥想
const val PAGE_MED_INDEX = 3

// 我的
const val PAGE_MINE_INDEX = 4

class MainActivity : BaseVMActivity<MainModel>() {

    companion object {
        @JvmStatic
        fun openMainActivity(context: Context, pageId: Int, isForceLogin: String = "") {
            val intent = Intent(context, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra(ConsIntent.MAIN_TAB_POSITION, pageId)
            if (!TextUtils.isEmpty(isForceLogin)) intent.putExtra(
                ConsIntent.BUNDLE_FORCE_LOGIN, isForceLogin
            )
            context.startActivity(intent)
        }
    }

    var isForceLogin = ""
    lateinit var mainBind: ActivityMainBinding
    private var mIndex = PAGE_HOME_INDEX
    private var prevExit: Long = -1
    private val mTitles by lazy(LazyThreadSafetyMode.NONE) {
        listOf(
            "首页",
            "练习",
            "睡眠",
            "冥想",
            "我的"
        )
    }
    private val mHomeFragment by lazy { HomeFragment.geInstance() }
    private val mPracticeFragment by lazy { PracticeFragment.newInstance() }
    private val meditationFragment by lazy { MeditationTabFragment() }
    private val sleepFragment by lazy { SleepTabFragment() }
    private val userFragment by lazy { UserFragment() }
    private val mFragments by lazy(LazyThreadSafetyMode.NONE) {
        listOf(
            mHomeFragment,
            mPracticeFragment,
            sleepFragment,
            meditationFragment,
            userFragment
        )
    }
    private lateinit var weakReference: WeakReference<MainActivity>
    private var mHasReplacedAllEmptyFragments = false
    private val mBlankPlaceHolderFragmentList = ArrayList<Fragment>().also {
        it.add(BlankPlaceHolderFragment.newInstance())
        it.add(BlankPlaceHolderFragment.newInstance())
        it.add(BlankPlaceHolderFragment.newInstance())
        it.add(BlankPlaceHolderFragment.newInstance())
        it.add(BlankPlaceHolderFragment.newInstance())
    }
    private val mTabFragmentList = ArrayList<Fragment>()
    private val pageAdapter by lazy {
        TitlePageNewAdapter(supportFragmentManager, mTabFragmentList, mTitles)
    }


    fun getMeditationFragmentLinearLayoutCompat(): LinearLayoutCompat? =
        meditationFragment.getMedLinearLayoutCompat()

    fun getMeditationFragmentSlidingTabLayout(): SlidingTabLayout? =
        meditationFragment.getMedSlidingTabLayout()

    fun getMeditationFragmentImg(): AppCompatImageView? = meditationFragment.getMedSlidingImg()

    var mMedItemCommonFgFlag = true

    private var mJoinViewLayer: PopupLayer? = null
    private var mJobDismissPopup: Job? = null
    private val mMemberChallengeViewModel by viewModels<MemberChallengeViewModel>()

    // 网络数据回来时 - 请求 - 底部菜单-我的 - 资源位
    private var mNetTabHostFlag = 0

    // 是否首次直接定位到首页练习页卡 | 是否需要做问卷
    private val mLocationPracticePage by lazy(LazyThreadSafetyMode.NONE) {
        ConfigMgr.getInstance().config.isStartQuestion || SpUtil.getInstance()
            .getBoolenValue(ConsSp.SP_KEY_LAST_TAB_PRACTICE, false)
    }

    private val mAdViewModel: AdViewModel by viewModels()

    // 好评弹窗 - 每次启动App只会弹出一次「接口控制是否弹出」
    var mShowGoodCommentDialog = true

    // ob支付成功 - 强制登陆
    var mObPaymentSuccessful = false

    override fun superInit(intent: Intent?) {
        mIndex = if (hasExtra(ConsIntent.MAIN_TAB_POSITION)) {
            intent?.getIntExtra(ConsIntent.MAIN_TAB_POSITION, PAGE_HOME_INDEX) ?: PAGE_HOME_INDEX
        } else {
            ConfigMgr.getInstance().config.app_default_tab
        }

        if (hasExtra(ConsIntent.BUNDLE_FORCE_LOGIN)) isForceLogin =
            intent?.getStringExtra(ConsIntent.BUNDLE_FORCE_LOGIN) ?: ""
        intent?.let {
            if (it.hasExtra(NimIntent.EXTRA_NOTIFY_CONTENT)) {
                if (EmptyUtils.isNotEmpty(userFragment)) {
                    userFragment.enterCustomerService(getString(R.string.string_reply_notification))
                } else {
                    UnicornManager.openQiYuService(
                        this, getString(R.string.string_reply_notification)
                    )
                }
                // 最好将intent清掉，以免从堆栈恢复时又打开客服窗口
                setIntent(Intent())
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        superInit(intent)
        setViewpager(mIndex)
        replaceBlankFragmentsIfNeed(mIndex)
    }

    override fun getLayoutResId(): Int = R.layout.activity_main

    override fun initVM() = ViewModelProvider(this).get(MainModel::class.java)

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .statusBarDarkFont(true)
            .init()
    }

    @SuppressLint("InflateParams")
    override fun initView() {
        // 神策数据 - App进入首页
        SensorsDataEvent.appEnterHome()
        if (isForceLogin == "Login" && !UserMgr.getInstance().isLogin) {
            mObPaymentSuccessful = true
            LoginCenter.getInstance()
                .loginControl(ActivityStackManager.getInstance().currentActivity)
                .setLoginState(LoginStateType.FORCED.value)
        }
        MobPushCenter.addMobPushReceiver()
        mainBind = mBinding as ActivityMainBinding
        weakReference = WeakReference<MainActivity>(this)
        if (ChannelUtils.isHuaWeiChannel()) {
            HmsManager.getInstance().checkUpdate(weakReference.get())
        }
        MobPushCenter.pushRouteData?.let {
            CommonUtil.goNextBannerOrWebView(this, it)
            MobPushCenter.pushRouteData = null
        }
        // 是否首次直接定位到首页练习页卡 | 是否需要做问卷
        if (mLocationPracticePage) mIndex = PAGE_PRACTICE_INDEX
        initViewAndListener()
        // 上报华为 - 分包 - 归因信息 - 华为渠道每个设备检查一次
        if (ChannelUtils.isHuaWeiChannel()) {
            if (SpUtil.getInstance()
                    .getBoolenValue(ConsSp.SP_KEY_HUA_WEI_SUBCONTRACTING_ATTRIBUTION, true)
            ) {
                // 上报华为 - 分包 - 归因信息
                HuaWeiSubcontractingAttributionUtils.getTrackId(this, DeviceUtil.getPackageName())
                    ?.let {
                        DeviceGuideRepository().uploadHuaWeiAttribution(it)
                    }
                SpUtil.getInstance()
                    .saveBooleanToSp(ConsSp.SP_KEY_HUA_WEI_SUBCONTRACTING_ATTRIBUTION, false)
            }
        }
    }

    override fun initData() {
        mViewModel.apply {
            checkVersion()
            getTabHost()
        }
        if (UserMgr.getInstance().isLogin) mMemberChallengeViewModel.getChallengeList()
        ConfigMgr.getInstance().updateConfig()
        Looper.myQueue().addIdleHandler {
            if (!NetworkUtils.isNetworkAvailable()) {
                ToastUtils.showShortToast(getString(R.string.string_ob_questionnaire_no_net_txt))
            }
            false
        }
    }

    override fun initSavedInstanceState(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            mIndex = savedInstanceState.getInt(ConsCommon.HOME_SHOW_CURRENT_FRAGMENT, mIndex)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt(ConsCommon.HOME_SHOW_CURRENT_FRAGMENT, mIndex)
        preventFragmentReconstruction(outState)
    }

    override fun onSaveInstanceState(outState: Bundle, outPersistentState: PersistableBundle) {
        super.onSaveInstanceState(outState, outPersistentState)
        preventFragmentReconstruction(outState)
    }

    /**
     * Androidx防止Fragment重建
     */
    private fun preventFragmentReconstruction(outState: Bundle) {
        outState.getBundle("androidx.lifecycle.BundlableSavedStateRegistry.key")?.let {
            it.remove("android:support:fragments")
            it.remove("android:fragments")
        }
    }

    override fun startObserve() {
        mViewModel.also {
            lifecycleScope.launchWhenResumed {
                it.checkVersion.observe(this@MainActivity) { uiStatus ->
                    if (uiStatus.isSuccess) {
                        uiStatus.successData?.upgrade_config?.apply {
                            val lastVersion = SpUtil.getInstance().getStringValue(
                                ConsSp.SP_KEY_UPDATE_APP_VERSION,
                                DeviceUtil.getClientVersionName()
                            )
                            val showDate =
                                SpUtil.getInstance().getString(ConsSp.SP_KEY_UPDATE_APP_DATE)
                            val currentDate = getCurrentDate()
                            // 1 强制升级  2提示升级  3弱提示升级
                            when (upgrade_type) {
                                // 强制升级 - 无法跳过或者关闭，不升级无法进行其他的操作
                                1 -> {
                                    AppUpdateReminderDialog(
                                        true,
                                        upgrade_title,
                                        upgrade_tip,
                                        getString(R.string.string_force_upgrade_txt)
                                    ).showDialog(
                                        supportFragmentManager
                                    )
                                    SensorsDataEvent.versionUpgradePopupsShow(getString(R.string.string_force_upgrade_txt))
                                }
                                // 强提示升级 - 可跳过或者关闭，按用户活跃天，每天提示一次
                                2 -> {
                                    if (showDate != currentDate) {
                                        SpUtil.getInstance().saveStringToSp(
                                            ConsSp.SP_KEY_UPDATE_APP_DATE,
                                            currentDate
                                        )
                                        AppUpdateReminderDialog(
                                            false,
                                            upgrade_title,
                                            upgrade_tip,
                                            getString(R.string.string_strong_prompt_upgrade_txt)
                                        ).showDialog(
                                            supportFragmentManager
                                        )
                                        SensorsDataEvent.versionUpgradePopupsShow(getString(R.string.string_strong_prompt_upgrade_txt))
                                    }
                                }
                                // 弱提示升级 - 可跳过或者关闭，一个版本只出现一次
                                3 -> {
                                    if (upgrade_version != lastVersion) {
                                        SpUtil.getInstance().saveStringToSp(
                                            ConsSp.SP_KEY_UPDATE_APP_VERSION,
                                            upgrade_version
                                        )
                                        AppUpdateReminderDialog(
                                            false,
                                            upgrade_title,
                                            upgrade_tip,
                                            getString(R.string.string_weak_prompt_upgrade_txt)
                                        ).showDialog(
                                            supportFragmentManager
                                        )
                                        SensorsDataEvent.versionUpgradePopupsShow(getString(R.string.string_weak_prompt_upgrade_txt))
                                    }
                                }

                                else -> {}
                            }
                        }
                    }
                }
            }
            it.tabHost.observe(this) { uiStatus ->
                if (uiStatus.isSuccess) {
                    mNetTabHostFlag += 1
                    val result = uiStatus.successData as ArrayList<TabBean>
                    mainBind.tabLayout.tabData = result
                    result.forEachIndexed { index, tabBean ->
                        if (NoticeRepository.isNeedShowNotice(tabBean)) {
                            if (tabBean.notice_type == 1) {
                                mainBind.tabLayout.setPointDot(index, true)
                            } else if (tabBean.notice_type == 2) {
                                mainBind.tabLayout.setNoticeImg(index, true, tabBean.notice_bubble)
                            }
                        }
                    }
                    if (mNetTabHostFlag == 2) {
                        mAdViewModel.getCampaignTriggerAds(ConsCommon.POSITION_IN_BOTTOM_MENU_ME)
                    }
                }
            }
        }
        // 会员挑战赛 - tips
        mMemberChallengeViewModel.listData.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    if (EmptyUtils.isNotEmpty(entity.toast)) {
                        entity.toast.join = 0
                        initTopViewOverlay(entity.toast)
                    }
                }
            }
        }
        // 活动触发广告 - 底部菜单 - 我的「红点｜气泡」
        mAdViewModel.campaignTriggerAds.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { ad ->
                    if (TextUtils.isEmpty(ad.imgURL)) {
                        mainBind.tabLayout.setNoticeImg(PAGE_MINE_INDEX, false, ad.imgURL)
                    } else {
                        if (SpUtil.getInstance().getStringValue(
                                ConsSp.SP_KEY_HOME_BOTTOM_NAVIGATION_MINE_IMG,
                                ""
                            ) != DateUtil.getDateYMD()
                        ) {
                            mainBind.tabLayout.setNoticeImg(PAGE_MINE_INDEX, true, ad.imgURL)
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 0元挑战赛 - 你的Now挑战计划已报名成功
        if (UserMgr.getInstance().isLogin) UserRepository().challengeRoundRegistrationSuccessful()
        //上传失败的练习使用记录(网络)
        lifecycleScope.launch(Dispatchers.IO) {
            delay(3000)
            UserActionLogMgr.getInstance().upLoadListenFailLogs()
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        mainBind.mainViewpager.clearOnPageChangeListeners()
        mJoinViewLayer?.dismiss(false)
        mJobDismissPopup?.cancel()
    }

    private fun initViewAndListener() {
        mainBind.mainViewpager.offscreenPageLimit = mFragments.size
        mTabFragmentList.apply {
            clear()
            addAll(mBlankPlaceHolderFragmentList)
            Collections.replaceAll(
                this, mBlankPlaceHolderFragmentList[mIndex], mFragments[mIndex]
            )
        }
        mainBind.mainViewpager.adapter = pageAdapter
        mainBind.mainViewpager.clearOnPageChangeListeners()
        mainBind.mainViewpager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {}
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int,
            ) {
            }

            override fun onPageSelected(position: Int) {
                mIndex = position
                mainBind.tabLayout.setCurrentTab(position)
                mainBind.viewLine.setBackgroundColor(
                    ContextCompat.getColor(
                        this@MainActivity, if (PAGE_SLEEP_INDEX == position) {
                            R.color.black100
                        } else {
                            R.color.login_gray_color
                        }
                    )
                )
                mainBind.tabLayout.setBackgroundColor(
                    ContextCompat.getColor(
                        this@MainActivity, if (PAGE_SLEEP_INDEX == position) {
                            R.color.color_sleep_tab_bg
                        } else {
                            R.color.color_white
                        }
                    )
                )
                ImmersionBar.with(this@MainActivity).reset().init()
                when (position) {
                    PAGE_HOME_INDEX, PAGE_PRACTICE_INDEX -> {
                        ImmersionBar
                            .with(this@MainActivity)
                            .transparentStatusBar()
                            .statusBarDarkFont(true)
                            .init()
                    }

                    PAGE_SLEEP_INDEX -> {
                        ImmersionBar
                            .with(this@MainActivity)
                            .transparentStatusBar()
                            .statusBarDarkFont(false)
                            .init()
                    }

                    PAGE_MED_INDEX -> {
                        when (meditationFragment.getMedTabCurrentPosition()) {
                            0 -> {
                                ImmersionBar
                                    .with(this@MainActivity)
                                    .statusBarColor(R.color.color_white)
                                    .statusBarDarkFont(true)
                                    .init()
                            }

                            1 -> {
                                if (meditationFragment.getMedTabCurrentHasAd()) {
                                    ImmersionBar
                                        .with(this@MainActivity)
                                        .statusBarColor(R.color.color_white)
                                        .statusBarDarkFont(true)
                                        .init()
                                } else {
                                    if (mMedItemCommonFgFlag) {
                                        ImmersionBar
                                            .with(this@MainActivity)
                                            .statusBarColor(R.color.color_white)
                                            .statusBarDarkFont(true)
                                            .init()
                                        mMedItemCommonFgFlag = false
                                    } else {
                                        ImmersionBar
                                            .with(this@MainActivity)
                                            .transparentStatusBar()
                                            .statusBarDarkFont(false)
                                            .init()
                                    }
                                }
                            }

                            else -> {
                                if (mMedItemCommonFgFlag) {
                                    ImmersionBar
                                        .with(this@MainActivity)
                                        .statusBarColor(R.color.color_white)
                                        .statusBarDarkFont(true)
                                        .init()
                                    mMedItemCommonFgFlag = false
                                } else {
                                    ImmersionBar
                                        .with(this@MainActivity)
                                        .transparentStatusBar()
                                        .statusBarDarkFont(false)
                                        .init()
                                }
                            }
                        }
                    }

                    else -> {
                        ImmersionBar
                            .with(this@MainActivity)
                            .statusBarColor(R.color.color_white)
                            .fitsSystemWindows(true)
                            .statusBarDarkFont(true)
                            .init()
                    }
                }
                when (position) {
                    PAGE_HOME_INDEX -> {
                        SensorsDataEvent.menuClick("首页", "底部", "推荐")
                    }

                    PAGE_PRACTICE_INDEX -> {
                        SensorsDataEvent.menuClick("练习", "底部", "")
                    }

                    PAGE_SLEEP_INDEX -> {
                        SensorsDataEvent.menuClick("睡眠", "底部", "")
                    }

                    PAGE_MED_INDEX -> {
                        SensorsDataEvent.menuClick("冥想", "底部", "推荐")
                    }

                    PAGE_MINE_INDEX -> {
                        SensorsDataEvent.menuClick("我的", "底部", "")
                        mainBind.tabLayout.setNoticeImg(PAGE_MINE_INDEX, false, "")
                        SpUtil.getInstance().saveStringToSp(
                            ConsSp.SP_KEY_HOME_BOTTOM_NAVIGATION_MINE_IMG,
                            DateUtil.getDateYMD()
                        )
                    }

                    else -> {}
                }
                if (position == PAGE_PRACTICE_INDEX) {
                    SpUtil.getInstance().saveBooleanToSp(ConsSp.SP_KEY_LAST_TAB_PRACTICE, true)
                } else if (position == PAGE_HOME_INDEX) {
                    SpUtil.getInstance().saveBooleanToSp(ConsSp.SP_KEY_LAST_TAB_PRACTICE, false)
                }
            }
        })

        mainBind.tabLayout.setListener(object : CustomTabLayout.OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                mIndex = position
                if (!mHasReplacedAllEmptyFragments) {
                    replaceBlankFragmentsIfNeed(mIndex)
                }
                mainBind.mainViewpager.currentItem = position
                try {
                    val tabBean = mainBind.tabLayout.tabData[position]
                    if (tabBean.isShowNotice) {
                        mainBind.tabLayout.setPointDot(position, false)
                        mainBind.tabLayout.setNoticeImg(mIndex, false, tabBean.notice_bubble)
                        NoticeRepository.isRecordNotice(tabBean)
                    }
                } catch (e: Throwable) {
                    e.printStackTrace()
                }
            }

            override fun onTabReselect(position: Int) {}
        })
        setViewpager(mIndex)
    }

    private fun setViewpager(position: Int) {
        mainBind.mainViewpager.currentItem = position
    }

    private fun replaceBlankFragmentsIfNeed(tabId: Int) {
        if (mHasReplacedAllEmptyFragments) {
            return
        }
        val tabRealIndex: Int = mBlankPlaceHolderFragmentList.indexOf(mTabFragmentList[tabId])
        if (tabRealIndex > -1) {
            if (Collections.replaceAll(
                    mTabFragmentList,
                    mBlankPlaceHolderFragmentList[tabRealIndex],
                    mFragments[tabRealIndex]
                )
            ) {
                pageAdapter.refreshFragments(mTabFragmentList)
                var hasAllReplaced = true
                for (fragment in mTabFragmentList) {
                    if (fragment is BlankPlaceHolderFragment) {
                        hasAllReplaced = false
                        break
                    }
                }
                if (hasAllReplaced) {
                    mBlankPlaceHolderFragmentList.clear()
                }
                mHasReplacedAllEmptyFragments = hasAllReplaced
            }
        }
    }

    override fun onBackPressed() {
        if (System.currentTimeMillis() - prevExit < 3000) {
            if (PlayCenter.getInstance().isPlaying) {
                Intent(Intent.ACTION_MAIN).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    addCategory(Intent.CATEGORY_HOME)
                    startActivity(this)
                }
            } else {
                SpUtil.getInstance().removeValue("diary")
                MyApplication.getInstance().appExit()
            }
        } else {
            if (PlayCenter.getInstance().isPlaying) {
                ToastUtils.showShortToastBottom(resources.getString(R.string.string_click_come_home))
            } else {
                ToastUtils.showShortToastBottom(resources.getString(R.string.finish_massege))
            }
            prevExit = System.currentTimeMillis()
        }
    }

    /**
     * 首页会员 - 会员挑战赛 - tips
     *
     * @param item ToastEntity
     */
    private fun initTopViewOverlay(item: ChallengeToastEntity.ToastEntity) {
        mJoinViewLayer?.dismiss(false)
        val mBindJoinView: LayoutViewMainMeditationTipBinding = DataBindingUtil.inflate(
            layoutInflater, R.layout.layout_view_main_meditation_tip, ConstraintLayout(this).also {
                it.layoutParams = ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                )
            }, true
        )
        mJoinViewLayer = AnyLayer.popup(mainBind.viewPoint).setAlign(
            PopupLayer.Align.Direction.VERTICAL,
            PopupLayer.Align.Horizontal.CENTER,
            PopupLayer.Align.Vertical.BELOW,
            true
        ).setContentView(mBindJoinView.root).setGravity(Gravity.CENTER)
            .setAnimStyle(DialogLayer.AnimStyle.TOP)
        mBindJoinView.apply {
            entity = item
            if (item.join == 0) {
                root.setOnClickListener {
                    MemberChallengeActivity.startActivity(
                        this@MainActivity, page_from_name = getString(R.string.string_home_page)
                    )
                }
            } else root.setOnClickListener(null)
            executePendingBindings()
        }
        mJoinViewLayer?.show()
        mJobDismissPopup = lifecycleScope.launch {
            delay(3000)
            mJoinViewLayer?.dismiss()
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.LOGIN_CHANGE_EVENT) {
            mMemberChallengeViewModel.getChallengeList()
            mAdViewModel.getCampaignTriggerAds(ConsCommon.POSITION_IN_BOTTOM_MENU_ME)
        } else if (event?.eventCode == ConsEventCode.CHANGE_SUBSCRIBE_EVENT) {
            mViewModel.getTabHost()
        }
    }

}