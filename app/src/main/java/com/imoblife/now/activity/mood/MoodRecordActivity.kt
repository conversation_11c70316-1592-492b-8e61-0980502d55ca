package com.imoblife.now.activity.mood

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.ActivityStackManager
import com.imoblife.now.R
import com.imoblife.now.adapter.MoodCourseAdapter
import com.imoblife.now.adapter.MoodDayAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.ActivityMoodRecordBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.SpUtil
import com.jaychang.st.SimpleText
import kotlinx.coroutines.delay

class MoodRecordActivity : BaseVMActivity<MoodModel>() {

    private lateinit var moodDayAdapter: MoodDayAdapter
    private lateinit var moodCourseAdapter: MoodCourseAdapter
    private var moodId: Int = 0

    companion object {

        fun startRecordMood(context: Context, moodId: Int = 0) {
            Intent(context, MoodRecordActivity::class.java).apply {
                putExtra(ConsIntent.BUNDLE_MOOD_ID, moodId)
                context.startActivity(this)
            }
        }

    }

    private lateinit var mBind: ActivityMoodRecordBinding

    override fun getLayoutResId() = R.layout.activity_mood_record

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .init()
    }

    override fun superInit(intent: Intent?) {
        if (hasExtra(intent, ConsIntent.BUNDLE_MOOD_ID)) {
            moodId = intent?.getIntExtra(ConsIntent.BUNDLE_MOOD_ID, 0) ?: 0
        }
    }

    override fun initVM() =
        ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(MoodModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityMoodRecordBinding
        mBind.apply {
            superTextView2.apply {
                shaderStartColor = Color.parseColor(
                    SpUtil.getInstance()
                        .getStringValue(ConsSp.SP_KEY_MOOD_GRADIENT_START_COLOR, "#D4949E")
                )
                shaderEndColor = Color.parseColor(
                    SpUtil.getInstance()
                        .getStringValue(ConsSp.SP_KEY_MOOD_GRADIENT_END_COLOR, "#BF8CAB")
                )
            }
            leftImg.setOnClickListener {
                ActivityStackManager.getInstance()
                    .finishSingleActivityByClass(MoodActivity::class.java)
                onBackPressed()
            }
            rightImg.setOnClickListener {
                MoodHistoryActivity.startHistoryMood(
                    this@MoodRecordActivity,
                    null
                )
            }
            checkHistory.setOnClickListener {
                MoodHistoryActivity.startHistoryMood(
                    this@MoodRecordActivity,
                    null
                )
            }
            moodDayAdapter = MoodDayAdapter()
            moodLogRecycler.adapter = moodDayAdapter
            moodLogRecycler.isNestedScrollingEnabled = false
            moodLogRecycler.layoutManager = GridLayoutManager(this@MoodRecordActivity, 7)

            moodCourseAdapter = MoodCourseAdapter(getString(R.string.string_mood_record))
            recommendRecycler.layoutManager = GridLayoutManager(this@MoodRecordActivity, 2)
            recommendRecycler.isNestedScrollingEnabled = false
            recommendRecycler.adapter = moodCourseAdapter
            recommendRecycler.addItemDecoration(
                CommonItemDecoration(
                    DisplayUtil.dip2px(15f),
                    0,
                    DisplayUtil.dip2px(15f),
                    0,
                    DisplayUtil.dip2px(15f),
                    0
                )
            )
        }
    }

    override fun initData() {
        mViewModel.apply {
            getWeekOfYear()
            getWeekDayMood()
            getMoodCourse(moodId)
            getEssayDays()
        }
        lifecycleScope.launchWhenResumed {
            delay(600)
            mBind.motionLayout.apply {
                setTransition(R.id.start, R.id.end)
                transitionToEnd()
            }
        }
    }

    override fun startObserve() {
        mViewModel.apply {
            yearWeek.observe(this@MoodRecordActivity) {
                mBind.monthTxt.text = getString(R.string.string_a_certain_month, it)
            }
            weekDayMood.observe(this@MoodRecordActivity) {
                moodDayAdapter.setNewData(it.successData)
            }
            moodCourse.observe(this@MoodRecordActivity) {
                moodCourseAdapter.setNewData(it.successData)
            }
            // 心情记录 - 总天数
            essayDays.observe(this@MoodRecordActivity) {
                if (it.isSuccess) {
                    it.successData?.let { entity ->
                        ImageLoader.loadImageUrl(this@MoodRecordActivity, entity.today_record, mBind.imgMood)
                        mBind.tvAccumulatedRecords.text = SimpleText
                            .from("${entity.times} 次")
                            .first("${entity.times}")
                            .size(20)
                            .bold()
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == ConsRequestCode.SEND_REQUEST_MOOD_DETAIL && resultCode == Activity.RESULT_OK) {
            mViewModel.apply {
                getWeekDayMood()
                getEssayDays()
            }
        }
    }

}