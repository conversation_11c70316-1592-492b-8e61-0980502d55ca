package com.imoblife.now.activity.category

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.CategoryNavigationEntity
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.bean.Course
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.net.*
import io.reactivex.Observable

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/6
 * 描   述：首页 - 金刚区 - 分类导航 - Repository
 */
class CategoryNavigationRepository : BaseRepository() {

    /**
     * 首页 - 金刚区 - 分类导航
     */
    fun getCategoryData(
        _listData: MutableLiveData<UiStatus<CategoryNavigationEntity>>,
        _refreshState: MutableLiveData<Boolean>,
        type: Int,
        page_id: Int,
        category_id: Int,
        isReadCache: Boolean
    ) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .getCategoryList(type, page_id, category_id)
            .onErrorResumeNext { _: Throwable? -> Observable.never() }
            .map(SaveCatchMap.getMapFunction("CATEGORY_NAVIGATION_CACHE$type"))
            .publish(
                PublishFunction.getFunction(
                    CatchObserver.getCacheObservable<BaseResult<CategoryNavigationEntity>>(
                        "CATEGORY_NAVIGATION_CACHE$type",
                        isReadCache
                    )
                )
            )
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<CategoryNavigationEntity>>() {
                override fun onSuccess(response: BaseResult<CategoryNavigationEntity>?) {
                    response?.result?.let {
                        _listData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        _listData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    _listData.value = UiStatus(false, null, msg, Status.FAILED)
                }

                override fun onComplete() {
                    _refreshState.value = isReadCache
                }
            })
    }

    /**
     * 首页 - 金刚区 - 分类导航 - 分类Item
     */
    fun getCategoryItemData(
        _listData: MutableLiveData<UiStatus<List<CategoryNavigationItemEntity>>>,
        type: String,
        cat_id: Int,
        page_id: Int,
        category_id: Int,
    ) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .getCategoryMore(type, cat_id, page_id, category_id)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<CategoryNavigationItemEntity>>>() {
                override fun onSuccess(response: BaseResult<List<CategoryNavigationItemEntity>>?) {
                    response?.result?.let {
                        _listData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        _listData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    _listData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 首页 - 推荐 - 此刻TOP5
     */
    fun getItemCourseData(
        _listData: MutableLiveData<UiStatus<List<Course>>>,
        type: String,
        cat_id: Int,
        page_id: Int,
        category_id: Int,
    ) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .getItemCourseData(type, cat_id, page_id, category_id)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<Course>>>() {
                override fun onSuccess(response: BaseResult<List<Course>>?) {
                    response?.result?.let {
                        _listData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        _listData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    _listData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

}