package com.imoblife.now.activity.main

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.VipPlanPopupEntity
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.LayoutAcFirstTrainingStartBinding
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.model.CourseMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.SpUtil
import com.imoblife.now.viewmodel.HomeViewModel
import com.jaychang.st.SimpleText

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/3/17
 * 描   述：首训 - 会员计划 - 开启 - Activity
 */
class FirstTrainingStartActivity : BaseVMActivity<HomeViewModel>() {

    companion object {

        fun startActivity(activity: Activity) {
            val intent = Intent(activity, FirstTrainingStartActivity::class.java)
            activity.startActivity(intent)
            activity.overridePendingTransition(R.anim.enter_anim_slide, R.anim.exit_anim_slide)
        }

    }

    private lateinit var mBind: LayoutAcFirstTrainingStartBinding

    private var mVipPlanPopupEntity: VipPlanPopupEntity? = null

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .statusBarDarkFont(false)
            .init()
    }

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[HomeViewModel::class.java]

    override fun getLayoutResId(): Int = R.layout.layout_ac_first_training_start

    override fun initView() {
        mBind = mBinding as LayoutAcFirstTrainingStartBinding
        SensorsDataEvent.practiceQuidanceExposure(3)
    }

    @SuppressLint("SetTextI18n")
    override fun initData() {
        val entityValue = SpUtil.getInstance().getStringValue(ConsSp.SP_KEY_HOME_PRACTICE_FIRST_TRAINING, "")
        if (!TextUtils.isEmpty(entityValue)) {
            try {
                mVipPlanPopupEntity = Gson().fromJson(entityValue, VipPlanPopupEntity::class.java)
                mVipPlanPopupEntity?.plan_course?.let {
                    SensorsDataEvent.membershipProgramBottomPopup(
                        it.id,
                        it.title ?: "",
                        it.id,
                        it.section_id,
                        0,
                    )
                }
            } catch (e: Exception) {
            }
            mBind.apply {
                tvTip.text = SimpleText
                    .from("一次冥想，就有78%几率杀死自我内耗")
                    .first("78%")
                    .textColor(R.color.color_FF4747)

                stvBtn.onDebounceClickListener {
                    if (UserMgr.getInstance().isLogin) {
                        doPlay()
                    } else {
                        LoginCenter
                            .getInstance()
                            .loginControl(this@FirstTrainingStartActivity, LoginCenter.LoginStyleDialog)
                    }
                }
            }
        }
    }

    private fun doPlay() {
        mVipPlanPopupEntity?.plan_course?.let {
            SensorsDataEvent.membershipProgramBottomPopup(
                it.plan_id,
                it.plan_title ?: "",
                it.course_id,
                it.section_id,
                1,
            )
            CourseMgr
                .getInstance()
                .playSingleCourseNoTransitionPage(it.course_id, it.section_id, -1, -1)
            finish()
        }
    }

    override fun startObserve() {}

    override fun onBackPressed() {}

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.LOGIN_CHANGE_EVENT) doPlay()
    }

}