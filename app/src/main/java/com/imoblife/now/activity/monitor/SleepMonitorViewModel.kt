package com.imoblife.now.activity.monitor

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseViewModel
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.AllSleepHistoryEntity
import com.imoblife.now.bean.GenerateSleepReportEntity
import com.imoblife.now.bean.MonitoringReportCourseEntity
import com.imoblife.now.bean.MonitoringReportEntity
import com.imoblife.now.bean.SleepAlarmClockEntity
import com.imoblife.now.bean.SleepMonitorCompleteSkuEntity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/26
 * 描   述：睡眠监测 - ViewModel
 */
class SleepMonitorViewModel : BaseViewModel<Any?>() {

    private val mSleepMonitorRepository by lazy { SleepMonitorRepository() }

    private val _uploadSleepMonitorLiveData = MutableLiveData<UiStatus<List<GenerateSleepReportEntity>>>()
    val uploadSleepMonitorLiveData: LiveData<UiStatus<List<GenerateSleepReportEntity>>> = _uploadSleepMonitorLiveData

    // 按月获取所有睡眠历程历程
    private val _monthSleepList = MutableLiveData<UiStatus<List<AllSleepHistoryEntity>>>()
    val monthSleepList: LiveData<UiStatus<List<AllSleepHistoryEntity>>> = _monthSleepList

    // 获取当次睡眠监测报告
    private val _monitoringReport = MutableLiveData<UiStatus<MonitoringReportEntity>>()
    val monitoringReport: LiveData<UiStatus<MonitoringReportEntity>> = _monitoringReport

    // 获取睡眠历程
    private val _sleepRecord = MutableLiveData<UiStatus<MonitoringReportCourseEntity>>()
    val sleepRecord: LiveData<UiStatus<MonitoringReportCourseEntity>> = _sleepRecord

    // 获取铃声列表
    private val _alarmList = MutableLiveData<UiStatus<SleepAlarmClockEntity>>()
    val alarmList: LiveData<UiStatus<SleepAlarmClockEntity>> = _alarmList

    // 删除记录
    private val _deleteRecord = MutableLiveData<UiStatus<Boolean>>()
    val deleteRecord: LiveData<UiStatus<Boolean>> = _deleteRecord

    // 睡眠监测 - 完成 - sku
    private val _sleepMonitorCompleteSku =
        MutableLiveData<UiStatus<SleepMonitorCompleteSkuEntity>>()
    val sleepMonitorCompleteSku: LiveData<UiStatus<SleepMonitorCompleteSkuEntity>> =
        _sleepMonitorCompleteSku

    fun uploadSleepMonitorData() {
        mSleepMonitorRepository.uploadSleepMonitorData(_uploadSleepMonitorLiveData)
    }

    /**
     * 按月获取所有睡眠历程历程
     */
    fun getMonthSleepList() {
        mSleepMonitorRepository.getMonthSleepList(initPage = true, _monthSleepList)
    }

    /**
     * 按月获取所有睡眠历程历程 - 加载更多
     */
    fun getMonthSleepListLoadMore() {
        mSleepMonitorRepository.getMonthSleepList(initPage = false, _monthSleepList)
    }

    /**
     * 获取当次睡眠监测报告
     *
     * @param cursor 服务器返回的游标 0 <= ？左（启用，可点击） ｜ 0 = 当天 ｜ 0 > ？右（未启用，不可点击）
     */
    fun getMonitoringReport(cursor: Int = 0) {
        mSleepMonitorRepository.getMonitoringReport(_monitoringReport, cursor)
    }

    /**
     * 获取睡眠历程
     *
     * @param type   0: 天；1：周；2：月；3：年
     * @param cursor 上次请求服务器返回 —— 左加；右减
     */
    fun getSleepRecord(type: Int, cursor: Int = 0) {
        mSleepMonitorRepository.getSleepRecord(_sleepRecord, type, cursor)
    }

    /**
     * 获取铃声列表
     */
    fun getAlarmList() {
        mSleepMonitorRepository.getAlarmList(_alarmList)
    }

    /**
     * 删除记录
     */
    fun deleteSleepRecord(id: Int){
        mSleepMonitorRepository.deleteSleepRecord(id,_deleteRecord)
    }

    /**
     * 睡眠监测 - 完成 - sku
     */
    fun getSleepMonitorCompleteSku() {
        mSleepMonitorRepository.getSleepMonitorCompleteSku(_sleepMonitorCompleteSku)
    }

}