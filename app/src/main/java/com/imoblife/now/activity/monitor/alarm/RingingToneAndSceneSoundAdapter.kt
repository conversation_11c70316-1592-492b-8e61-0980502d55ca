package com.imoblife.now.activity.monitor.alarm

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.activity.monitor.AudioFrequencyCallBack
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.SleepAlarmClockEntity
import com.imoblife.now.databinding.LayoutViewRingingToneAndSceneSoundBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.getBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：铃声 | 轻唤醒场景音 - 睡眠闹钟 - Adapter
 * mType == 0 为闹钟铃声
 * mType == 1 为轻唤醒场景音
 */
class RingingToneAndSceneSoundAdapter(
    private val mType: Int,
    private val mCallBack: AudioFrequencyCallBack
) : BaseQuickAdapter<SleepAlarmClockEntity, BaseViewHolder>(R.layout.layout_view_ringing_tone_and_scene_sound) {

    private val mAudioFrequency by lazy(LazyThreadSafetyMode.NONE) { AudioFrequencyAdapter(mType) }

    private val mDecorationV by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(20.dp, 20.dp, 20.dp, 20.dp, 20.dp, 20.dp)
    }

    override fun convert(holder: BaseViewHolder, item: SleepAlarmClockEntity) {
        holder.getBinding(LayoutViewRingingToneAndSceneSoundBinding::bind).let { layout ->
            layout.tvTitle.text =
                if (mType == 0) mContext.getString(R.string.string_alarm_bell) else mContext.getString(
                    R.string.string_light_wake_up_scene_sound
                )
            layout.recyclerView.apply {
                removeItemDecoration(mDecorationV)
                addItemDecoration(mDecorationV)
                adapter = mAudioFrequency
                if (mType == 0) mAudioFrequency.setNewData(item.alarm) else mAudioFrequency.setNewData(
                    item.wake_up_lightly
                )
            }
            mAudioFrequency.setAudioFrequencyCallBack(mCallBack)
        }
    }

}