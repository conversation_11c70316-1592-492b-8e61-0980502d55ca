package com.imoblife.now.activity.main

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.LayoutAcPopularCoursesBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.viewmodel.HomeViewModel

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/8/25
 * 描   述：热门课程Top10
 */
class PopularCoursesActivity : BaseVMActivity<HomeViewModel>() {

    companion object {

        fun startActivity(context: Context, listType: String, typeTitle: String) {
            Intent(context, PopularCoursesActivity::class.java).apply {
                putExtra(ConsIntent.BUNDLE_POPULAR_COURSES_TYPE, listType)
                putExtra(ConsIntent.BUNDLE_POPULAR_COURSES_TITLE, typeTitle)
                context.startActivity(this)
            }
        }

    }

    private lateinit var mBind: LayoutAcPopularCoursesBinding

    private lateinit var mLoadingHelper: LoadingHelper

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) { PopularCoursesAdapter() }

    private var mListType = ""

    private var mTypeTitle = ""

    override fun getLayoutResId() = R.layout.layout_ac_popular_courses

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_POPULAR_COURSES_TYPE)) {
                mListType = it.getStringExtra(ConsIntent.BUNDLE_POPULAR_COURSES_TYPE) ?: ""
            }
            if (hasExtra(ConsIntent.BUNDLE_POPULAR_COURSES_TITLE)) {
                mTypeTitle = it.getStringExtra(ConsIntent.BUNDLE_POPULAR_COURSES_TITLE)
                    ?: getString(R.string.string_popular_courses_top_ten_txt)
            }
        }
    }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .titleBar(findViewById(R.id.toolbar))
            .transparentBar()
            .statusBarDarkFont(true)
            .init()
    }

    override fun initVM() = ViewModelProvider(this).get(HomeViewModel::class.java)

    override fun initView() {
        mLoadingHelper = LoadingHelper(this)
        mBind = mBinding as LayoutAcPopularCoursesBinding
        mBind.apply {
            tvContent.text = mTypeTitle
            tvTitle.onDebounceClickListener { onBackPressed() }
            recyclerView.apply {
                addItemDecoration(CommonItemDecoration(12.dp, 12.dp, 20.dp, 12.dp, 20.dp, 12.dp))
                adapter = mAdapter
            }
        }
    }

    override fun initData() {
        mLoadingHelper.apply {
            showLoadingView()
            setOnReloadListener { mViewModel.getPopularityList(mListType) }
        }
        mViewModel.getPopularityList(mListType)
    }

    override fun startObserve() {
        mViewModel.apply {
            popularityList.observe(this@PopularCoursesActivity) {
                if (it.isSuccess) {
                    it.successData?.let { list ->
                        if (list.isEmpty()) {
                            mLoadingHelper.showEmptyView()
                        } else {
                            mLoadingHelper.showContentView()
                            mAdapter.setNewData(list)
                        }
                    }
                } else {
                    mLoadingHelper.showErrorView()
                }
            }
        }
    }

}