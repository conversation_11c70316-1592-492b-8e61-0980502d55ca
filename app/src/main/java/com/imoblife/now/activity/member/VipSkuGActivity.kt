package com.imoblife.now.activity.member

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.ActivityStackManager
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.SkuStyleVipImgAdapter
import com.imoblife.now.bean.AdResourceBean
import com.imoblife.now.bean.SubSkuInfo
import com.imoblife.now.bean.Subscribe
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityVipSkuGBinding
import com.imoblife.now.ext.preLoadImg
import com.imoblife.now.ext.startVipSkuActivity
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.dialog.PopupActivityRulesAlterDialog
import com.imoblife.now.view.dialog.PopupHalfAlterImgDialog
import com.imoblife.now.viewmodel.PaymentViewModel
import com.jaychang.st.SimpleText
import org.greenrobot.eventbus.EventBus

/**
 * 版    权：纳沃科技@版权所有
 * 描   述：全屏 - 订阅 - 横版「2｜3「方块」」｜竖版「横条」
 */
class VipSkuGActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        fun startActivity(
            context: Context,
            adResourceBean: AdResourceBean,
            isPracticeFm: Boolean,
            // 是否为二次全屏
            isSecondFull: Boolean = false
        ) {
            Intent(context, VipSkuGActivity::class.java).apply {
                putExtra(ConsIntent.BUNDLE_AD_RESOURCE, adResourceBean)
                putExtra(ConsIntent.BUNDLE_IS_PRACTICE_PAGE, isPracticeFm)
                putExtra(ConsIntent.BUNDLE_IS_SECOND_FULL, isSecondFull)
                context.startActivity(this)
                (context as Activity).overridePendingTransition(R.anim.bottom_in, R.anim.bottom_out)
            }
        }

    }

    override fun finish() {
        // 弹出首训 - 点击关闭全屏时
        if (!mIsPurchase && mIsPracticeFm) {
            EventBus.getDefault().post(BaseEvent(ConsEventCode.VIP_PLAN_BOTTOM_DIALOG_ACTION_CLICK))
        }
        if (!mIsPurchase) {
            mSkuInfo?.let {
                if (EmptyUtils.isNotEmpty(it.auto_open_sku_page)) {
                    startVipSkuActivity(it.auto_open_sku_page, isSecondFull = true)
                }
            }
        }
        super.finish()
        //关闭窗体动画显示
        overridePendingTransition(0, R.anim.bottom_out)
    }

    private lateinit var mBind: ActivityVipSkuGBinding
    private var skuStyleHorizontalAdapter: SkuStyleVipImgAdapter? = null
    private var mSubscribe: Subscribe? = null
    private val popupHalfAlterDialog by lazy { PopupHalfAlterImgDialog(this) }
    private var isClickCloseVipPage = false
    private var isShowCancelDialogPage = false
    private var mAdResourceBean: AdResourceBean? = null
    private var mSkuInfo: SubSkuInfo? = null

    // 是否购买成功
    private var mIsPurchase = false

    // 全屏模板 - 是否来自首页练习
    private var mIsPracticeFm = false

    // 是否为二次全屏
    private var mIsSecondFull = false

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_AD_RESOURCE)) {
                mAdResourceBean =
                    it.getSerializableExtra(ConsIntent.BUNDLE_AD_RESOURCE) as AdResourceBean
            }
            if (hasExtra(ConsIntent.BUNDLE_IS_PRACTICE_PAGE)) {
                mIsPracticeFm = it.getBooleanExtra(ConsIntent.BUNDLE_IS_PRACTICE_PAGE, false)
            }
            if (hasExtra(intent, ConsIntent.BUNDLE_IS_SECOND_FULL)) {
                mIsSecondFull = it.getBooleanExtra(ConsIntent.BUNDLE_IS_SECOND_FULL, false)
            }
        }
    }

    override fun getLayoutResId() = R.layout.activity_vip_sku_g

    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentStatusBar().init()
    }

    override fun initVM() = ViewModelProvider(this).get(PaymentViewModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityVipSkuGBinding
        lifecycle.addObserver(mBind.paySubmitTxt)
        mBind.paySubmitTxt.initBackGroundRes()
        mBind.paySubmitTxt.setNewContentTip()
        mBind.paySubmitTxt.setSubmitPayOnClickListener {
            skuStyleHorizontalAdapter?.getSubscribe()?.let {
                val entity = it
                entity.pre_advertising_id = mAdResourceBean?.id ?: 0
                entity.pre_source_id = mAdResourceBean?.source_id ?: 0
                entity.pre_source_type = mAdResourceBean?.source_type ?: 0
                entity?.params?.course_id = mAdResourceBean?.pre_course_id ?: 0
                mBind.subProtocolPrivacy.isAgreePrivacy(entity, "首页全屏") {
                    PayCenter.getInstance().doSubmitPay(this, entity)
                }
            } ?: let {
                ToastUtils.showShortToastCenter(getString(R.string.string_please_select_subscribe))
            }
        }
        mBind.closeImg.setOnClickListener {
            mSkuInfo?.let {
                isClickCloseVipPage = true
                it.isIs_full_resource = true
                adSkuPageClose(
                    this,
                    mViewModel,
                    it,
                    isShowCancelDialogPage,
                    skuId = skuStyleHorizontalAdapter?.getSubscribe()?.id ?: -1
                )
            } ?: finish()
        }
    }

    override fun initData() {
        mAdResourceBean?.let { getAdSkuPageData(mViewModel, it) } ?: finish()
    }

    override fun startObserve() {
        mViewModel.adPageSkuList.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    entity.preLoadImg(this)
                    setSukInfo(entity)
                }
            }
        }
        mViewModel.adPageCancelSkuList.observe(this) { uiStatus ->
            showHalfDialog(uiStatus, popupHalfAlterDialog)
            if (isClickCloseVipPage) {
                isShowCancelDialogPage = true
            }
        }
    }

    private fun setSukInfo(subSkuInfo: SubSkuInfo?) {
        subSkuInfo?.let {
            mSkuInfo = it
//            it.sku_list.firstNotNullOf { subscribe -> mSubscribe = subscribe }
            mBind.paySubmitTxt.setPayButtonData(it.pay_button)
            ImageLoader.loadImageUrl(this, it.banner, mBind.topHeaderImg)
            ImageLoader.loadImageUrl(this, it.bottom_img, mBind.imgBottom)
            ImageLoader.loadImageUrl(
                this,
                it.close_img,
                mBind.closeImg,
                R.mipmap.icon_vip_sku_close
            )

            if (subSkuInfo.style == 1) {
                skuStyleHorizontalAdapter =
                    SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_dynamic_img_size)
                mBind.skuRecycler.layoutManager =
                    LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
            } else {
                skuStyleHorizontalAdapter = when (subSkuInfo.sku_list.size) {
                    2 -> SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_style_e)
                    else -> SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_style_a)
                }
                mBind.skuRecycler.layoutManager =
                    LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
            }
            // 设置挑战赛规则
            if (!TextUtils.isEmpty(subSkuInfo.rule)) {
                mBind.tvRule.visibility = View.VISIBLE
                mBind.tvRule.text = SimpleText
                    .from(getString(R.string.string_read_challenge_activity_rules))
                    .first(getString(R.string.string_challenge_activity_rules))
                    .textColor(R.color.main_color)
                    .onClick(mBind.tvRule) { _, _, _ ->
                        PopupActivityRulesAlterDialog(ActivityStackManager.getInstance().currentActivity as AppCompatActivity).setContentString(
                            subSkuInfo.rule
                        )
                    }
            } else {
                mBind.tvRule.visibility = View.GONE
            }
            val isShouAutoCloseable = if (ConfigMgr.getInstance().config.isAuto_vip_privacy_ad) {
                !ConfigMgr.getInstance().config.auto_vip_privacy_special_ad_list.contains(mSkuInfo!!.source_id)
            } else {
                false
            }
            skuStyleHorizontalAdapter?.setActionBlock { _ ->
                mBind.subProtocolPrivacy.setAgreementCheckboxFalse()
                skuStyleHorizontalAdapter?.getSubscribe()?.let { sub ->
//                    mBind.subProtocolPrivacy.setData(
//                        sub,
//                        ConfigMgr.getInstance().config.isAuto_vip_privacy_ad
//                    )
                    mBind.subProtocolPrivacy.setData(sub, isShouAutoCloseable)
                }
            }
            mBind.skuRecycler.adapter = skuStyleHorizontalAdapter
            skuStyleHorizontalAdapter?.setNewData(subSkuInfo.sku_list)
            if (subSkuInfo.sku_list.size > 1) {
                mBind.skuRecycler.visibility = View.VISIBLE
            } else {
                mBind.skuRecycler.visibility = View.GONE
            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.PAY_CANCEL_EVENT || event?.eventCode == ConsEventCode.PAY_FAIL_EVENT) {
            mSkuInfo?.let {
                it.isIs_full_resource = true
                adSkuPageCancelPay(
                    mViewModel,
                    it,
                    skuId = skuStyleHorizontalAdapter?.getSubscribe()?.id ?: -1
                )
            }
        } else if (event?.eventCode == ConsEventCode.PAY_SUCCESS_EVENT) {
            mIsPurchase = true
            if (!UserMgr.getInstance().isLogin) {
                LoginCenter.getInstance().loginControl(this)
            }
            finish()
        }
    }

    override fun onBackPressed() {}

}