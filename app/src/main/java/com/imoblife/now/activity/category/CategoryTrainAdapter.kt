package com.imoblife.now.activity.category

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.coorchice.library.SuperTextView
import com.imoblife.now.R
import com.imoblife.now.bean.CategoryNavigationItemEntity
import com.imoblife.now.ext.pageRoute
import com.imoblife.now.util.ImageLoader
import com.makeramen.roundedimageview.RoundedImageView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/4/7
 * 描   述：首页 - 金刚区 - 分类导航 - 行动营
 */
class CategoryTrainAdapter :
    BaseQuickAdapter<CategoryNavigationItemEntity, BaseViewHolder>(R.layout.layout_item_training) {

    override fun convert(holder: BaseViewHolder, item: CategoryNavigationItemEntity?) {
        item?.apply {
            val trainImg = holder.getView<RoundedImageView>(R.id.train_img)
            val trainTitleTxt = holder.getView<TextView>(R.id.train_title_txt)
            val trainSubtitleTxt = holder.getView<TextView>(R.id.train_subtitle_txt)
            val trainSignUpTimeTxt = holder.getView<TextView>(R.id.train_sign_up_time_txt)
            val trainSignUpUserTxt = holder.getView<TextView>(R.id.train_sign_up_user_txt)
            val trainStateTxt = holder.getView<SuperTextView>(R.id.train_state_txt)
            val trainClickTxt = holder.getView<SuperTextView>(R.id.train_click_txt)

            ImageLoader.loadImageUrl(mContext, thumb_img, trainImg)
            trainTitleTxt.text = title
            trainSubtitleTxt.text = description
            trainSignUpTimeTxt.text = tex
            trainSignUpUserTxt.text = baoming
            trainStateTxt.text = subtitle
            trainClickTxt.text = button_title
            holder.itemView.setOnClickListener { pageRoute(mContext, holder.layoutPosition) }
        }
    }

}