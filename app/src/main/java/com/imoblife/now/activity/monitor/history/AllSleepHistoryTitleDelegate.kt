package com.imoblife.now.activity.monitor.history

import android.content.Context
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.LinearLayoutCompat
import com.drakeet.multitype.ViewDelegate
import com.imoblife.now.R
import com.imoblife.now.ext.dp

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-12-2
 * 描   述：所有睡眠记录 - TitleDelegate
 */
class AllSleepHistoryTitleDelegate : ViewDelegate<String, AppCompatTextView>() {

    override fun onBindView(view: AppCompatTextView, item: String) {
        view.text = item
    }

    override fun onCreateView(context: Context) = AppCompatTextView(context).also {
        it.apply {
            setTextAppearance(R.style.Widget_App_Text_White_FirstTitle)
            val linearLayoutCompat =
                LinearLayoutCompat.LayoutParams(
                    LinearLayoutCompat.LayoutParams.MATCH_PARENT,
                    LinearLayoutCompat.LayoutParams.WRAP_CONTENT
                )
            linearLayoutCompat.setMargins(20.dp, 14.dp, 0, 14.dp)
            layoutParams = linearLayoutCompat
        }
    }

}