package com.imoblife.now.activity.facedetection

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.FaceDetectionEntity
import com.imoblife.now.bean.FaceDetectionLimitEntity
import com.imoblife.now.bean.FaceDetectionReportEntity
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiService
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024-11-19
 * 描   述：人脸检测 - Repository
 */
class FaceDetectionRepository : BaseRepository() {

    /**
     * 上报 reportDeepFace - 图片
     */
    fun reportDeepFace(img: String, realImg: String, liveData: MutableLiveData<UiStatus<FaceDetectionEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .reportDeepFace(img, realImg)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<FaceDetectionEntity>>() {
                override fun onSuccess(response: BaseResult<FaceDetectionEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    liveData.value = UiStatus(
                        isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED
                    )
                }
            })
    }

    /**
     * 人脸检测 - 报告
     */
    fun getReportDetail(reportId: String, liveData: MutableLiveData<UiStatus<FaceDetectionReportEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .getReportDetail(reportId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<FaceDetectionReportEntity>>() {
                override fun onSuccess(response: BaseResult<FaceDetectionReportEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    liveData.value = UiStatus(
                        isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED
                    )
                }
            })
    }

    /**
     * 人脸检测 - 限制
     */
    fun checkDFRight(liveData: MutableLiveData<UiStatus<FaceDetectionLimitEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiService::class.java)
            .checkDFRight()
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<FaceDetectionLimitEntity>>() {
                override fun onSuccess(response: BaseResult<FaceDetectionLimitEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(isSuccess = true, successData = it)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    liveData.value = UiStatus(
                        isSuccess = false,
                        successData = null,
                        failureData = msg,
                        status = Status.FAILED
                    )
                }
            })
    }

}