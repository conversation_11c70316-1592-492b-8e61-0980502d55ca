package com.imoblife.now.activity.monitor.alarm

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpFrameCacheStrategy
import com.bumptech.glide.integration.webp.decoder.WebpFrameLoader
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.activity.member.SubscribeActivity
import com.imoblife.now.activity.monitor.AudioFrequencyCallBack
import com.imoblife.now.bean.SleepAlarmClockEntity
import com.imoblife.now.databinding.LayoutViewAudioFrequencySleepAlarmClockBinding
import com.imoblife.now.ext.getBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.player.BGPlayerUtils
import com.imoblife.now.player.PlayCenter

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：音频 - 睡眠闹钟 - Adapter
 * mType == 0 为闹钟铃声
 * mType == 1 为轻唤醒场景音
 */
class AudioFrequencyAdapter(private val mType: Int) :
    BaseQuickAdapter<SleepAlarmClockEntity.SleepAlarmClockItemEntity, BaseViewHolder>(R.layout.layout_view_audio_frequency_sleep_alarm_clock) {

    private var mCallBack: AudioFrequencyCallBack? = null

    fun setAudioFrequencyCallBack(callback: AudioFrequencyCallBack) {
        mCallBack = callback
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun convert(
        holder: BaseViewHolder,
        item: SleepAlarmClockEntity.SleepAlarmClockItemEntity
    ) {
        holder.getBinding(LayoutViewAudioFrequencySleepAlarmClockBinding::bind).let { layout ->
            layout.stvImg.apply {
                isShowState2 = item.is_vip == 1
                Glide
                    .with(mContext)
                    .asBitmap()
                    .load(item.thumb_img)
                    .set(
                        WebpFrameLoader.FRAME_CACHE_STRATEGY,
                        WebpFrameCacheStrategy.AUTO
                    )
                    .into(object : SimpleTarget<Bitmap>() {
                        override fun onResourceReady(
                            resource: Bitmap,
                            transition: Transition<in Bitmap>?
                        ) {
                            setDrawable(resource)
                        }
                    })
                layout.img.isShowImg(item)
                setOnClickListener {
                    PlayCenter.getInstance().playerControl.pauseMusic()
                    if (item.is_vip == 1 && !UserMgr.getInstance().isHasNowVip) {
                        SubscribeActivity.openSubscribeActivity(
                            context = mContext,
                            pageOrigin = 5
                        )
                        return@setOnClickListener
                    }
                    data.forEach { entity ->
                        if (item.id == entity.id) {
                            entity.isSelect = true
                            mCallBack?.selectRingingTone(mType, entity.url)
                            BGPlayerUtils.instance.startPlayBackground(entity.url)
                            layout.img.isShowImg(entity)
                        } else {
                            entity.isSelect = false
                            layout.img.isShowImg(entity)
                        }
                    }
                    notifyDataSetChanged()
                }
            }
        }
    }

    /**
     * 更新选中状态 - 显示隐藏img
     *
     * @receiver AppCompatImageView
     * @param item SleepAlarmClockEntity.SleepAlarmClockItemEntity
     */
    private fun AppCompatImageView.isShowImg(item: SleepAlarmClockEntity.SleepAlarmClockItemEntity) {
        visibility = if (item.isSelect) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

}
