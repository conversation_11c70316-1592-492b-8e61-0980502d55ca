package com.imoblife.now.activity.monitor

import android.content.Context
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.util.SpUtil
import com.imoblife.now.util.TimeUtils
import com.imoblife.now.util.alarmmanager.AlarmManagerUtils
import java.util.Calendar

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/6/1
 * 描   述：SettingAlarmClockUtil
 */
object SettingAlarmClockUtil {

    /**
     * 设置闹钟与轻唤醒
     *
     * @param context 上下文
     * @param hour 时
     * @param minute 分
     */
    fun settingAlarmClock(context: Context, hour: Int, minute: Int) {
        // 闹钟铃声｜轻唤醒场景音 - 某一音频
        val ringingTone = SpUtil.getInstance()
            .getStringValue(
                ConsCommon.SLEEP_ALARM_CLOCK_RINGING_TONE,
                ConsCommon.SLEEP_ALARM_CLOCK_RINGING_TONE_URL
            )
        val sceneSound = SpUtil.getInstance()
            .getStringValue(
                ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING,
                ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_URL
            )
        // 闹钟
        AlarmManagerUtils.setAlarm(
            context,
            0,
            hour,
            minute,
            0,
            0,
            0,
            "",
            2,
            ringingTone,
            0
        )
        val reduce = SpUtil.getInstance()
            .getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_MINUTE, 30)
        val time = TimeUtils.reduceTime(hour, minute, reduce)
        // 轻唤醒
        AlarmManagerUtils.setAlarm(
            context,
            0,
            time.first,
            time.second,
            0,
            1,
            0,
            "",
            1,
            sceneSound,
            1
        )
    }

    /**
     * 毫秒 - 获取闹钟与轻唤醒时间
     *
     * @param hour 时
     * @param minute 分
     */
    fun setAlarmClock(hour: Int, minute: Int): Pair<Long, Long> {
        // 闹钟
        val calendar = Calendar.getInstance()
        calendar.set(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH),
            hour,
            minute,
            0
        )
        val triggerAtMillis = AlarmManagerUtils.computingTriggerAtMillis(0, calendar.timeInMillis)
        val reduce = SpUtil.getInstance()
            .getIntValue(ConsCommon.SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_MINUTE, 30)
        val time = triggerAtMillis - reduce * 60 * 1000
        return Pair(triggerAtMillis, time)
    }

}