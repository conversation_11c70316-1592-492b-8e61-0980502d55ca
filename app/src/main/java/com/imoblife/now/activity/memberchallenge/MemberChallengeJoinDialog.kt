package com.imoblife.now.activity.memberchallenge

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.imoblife.now.MyApplication.Companion.getInstance
import com.imoblife.now.R
import com.imoblife.now.activity.course.CourseCategoryActivity
import com.imoblife.now.databinding.LayoutViewMemberChallengeJoinDialogBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/7/21
 * 描   述：会员挑战 - 成功加入挑战，快去进行冥想练习
 */
class MemberChallengeJoinDialog(mContext: Context, private val mTitle: String) :
    Dialog(mContext, R.style.dialog) {

    private lateinit var mBind: LayoutViewMemberChallengeJoinDialogBinding

    companion object {

        fun showDialog(context: Context, title: String) {
            MemberChallengeJoinDialog(context, title).showDialog()
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBind = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.layout_view_member_challenge_join_dialog,
            null,
            false
        )
        setContentView(mBind.root)
        mBind.apply {
            clickProxy = ClickProxy()
            tvSubTitle.text =
                context.getString(R.string.string_the_challenge_journey_has_started, mTitle)
            executePendingBindings()
        }
        setCancelable(false)
    }

    fun showDialog() {
        try {
            if (isShowing) {
                dismiss()
            }
            super.show()
            val attributes = window?.attributes
            attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window?.attributes = attributes
        } catch (i: Throwable) {
            i.printStackTrace()
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 马上去
                R.id.stvBtn -> {
                    dismiss()
                    CourseCategoryActivity.openCourseCategory(
                        context,
                        true,
                        getInstance().getString(R.string.string_found_course_title),
                        -1,
                        -1
                    )
                }
                // 稍后再去
                R.id.tvBtn -> {
                    dismiss()
                }
                else -> {}
            }
        }

    }

}