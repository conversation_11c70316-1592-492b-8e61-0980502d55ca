package com.imoblife.now.activity.member

import android.view.View
import android.view.ViewGroup
import androidx.annotation.NonNull
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.RecyclerView
import com.imoblife.now.R
import com.youth.banner.adapter.BannerAdapter
import com.youth.banner.util.BannerUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/6/25
 * 描   述：订阅｜会员中心 - 竖SkuList - banner - 购买清单
 */
class SubScribeBannerPurchaseListAdapter(dataList: MutableList<String>) :
    BannerAdapter<String, SubScribeBannerPurchaseListAdapter.TopLineHolder>(dataList) {

    /**
     * 创建ViewHolder
     *
     * @return XViewHolder
     */
    override fun onCreateHolder(parent: ViewGroup, viewType: Int) =
        TopLineHolder(BannerUtils.getView(parent, R.layout.layout_view_banner_sub_purchase_list))

    /**
     * 绑定布局数据
     *
     * @param holder   XViewHolder
     * @param data     数据实体
     * @param position 当前位置
     * @param size     总数
     */
    override fun onBindView(holder: TopLineHolder, data: String?, position: Int, size: Int) {
        holder.tvContent.text =
            holder.tvContent.context.getString(R.string.string_just_joined_the_membership_txt, data)
    }

    class TopLineHolder(@NonNull view: View) : RecyclerView.ViewHolder(view) {
        var tvContent: AppCompatTextView = view.findViewById(R.id.tvContent)
    }

}
