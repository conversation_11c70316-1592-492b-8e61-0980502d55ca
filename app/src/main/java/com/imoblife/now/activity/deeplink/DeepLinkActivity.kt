package com.imoblife.now.activity.deeplink

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.imoblife.now.ActivityStackManager
import com.imoblife.now.activity.welcome.WelcomeActivity

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/3/21
 * 描   述：DeepLink - 深度链接落地页
 */
class DeepLinkActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (null == savedInstanceState) {
            dispatchIntent(intent)
        }
        finish()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        dispatchIntent(intent)
        finish()
    }

    private fun dispatchIntent(intent: Intent?) {
        if (null == intent) {
            return
        }
        val uri: Uri = intent.data ?: return
        if (ActivityStackManager.getInstance().runningActivityCount == 1) {
            Log.e("yunyang", "冷启动")
            // 冷启动
            SchemeHelper.setPendingSchemeUri(uri)
            WelcomeActivity.startActivity(this)
        } else {
            Log.e("yunyang", "热启动")
            // 热启动
            SchemeHelper.parseUriFromIntent(uri)
        }
    }

}