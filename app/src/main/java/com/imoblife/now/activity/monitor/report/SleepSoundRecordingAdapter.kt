package com.imoblife.now.activity.monitor.report

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.imoblife.now.R
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.MonitoringReportSoundCategoryItemEntity
import com.imoblife.now.databinding.LayoutViewSleepMonitoringReportSoundRecordingBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.util.ExpandableViewHoldersUtil

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/26
 * 描   述：睡眠监测报告 - 睡眠声音记录 - Adapter
 */
class SleepSoundRecordingAdapter(private val mLifecycleOwner: LifecycleOwner) :
    RecyclerView.Adapter<SleepSoundRecordingAdapter.SleepSoundRecordingAdapterHolder>() {

    private var mBlockAction: ((url: String, bool: Boolean) -> Unit)? = null

    fun setBlockAction(block: ((url: String, bool: Boolean) -> Unit)) {
        mBlockAction = block
    }

    // 睡眠监测报告 - 睡眠声音记录 - play - Decoration
    private val mSleepSoundRecordingPlayDecoration by lazy(LazyThreadSafetyMode.NONE) {
        CommonItemDecoration(30.dp, 30.dp, 4.dp, 0, 4.dp, 0)
    }

    private var mData: MutableList<MonitoringReportSoundCategoryItemEntity> = mutableListOf()

    @SuppressLint("NotifyDataSetChanged")
    fun setNewData(data: MutableList<MonitoringReportSoundCategoryItemEntity>) {
        mData.apply {
            clear()
            addAll(data)
            notifyDataSetChanged()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        SleepSoundRecordingAdapterHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.layout_view_sleep_monitoring_report_sound_recording,
                parent,
                false
            )
        )

    override fun onBindViewHolder(holder: SleepSoundRecordingAdapterHolder, position: Int) {
        holder.mBind?.apply {
            val entity = mData[position]
            tvTitle.text = entity.sound_title
            tvSubTitle.text =
                holder.itemView.context.getString(R.string.string_paragraph_txt, entity.amount)
            recyclerView.apply {
                removeItemDecoration(mSleepSoundRecordingPlayDecoration)
                addItemDecoration(mSleepSoundRecordingPlayDecoration)
                // 睡眠监测报告 - 睡眠声音记录 - play - Adapter
                val sleepSoundRecordingPlayAdapter = SleepSoundRecordingPlayAdapter(mLifecycleOwner)
                adapter = sleepSoundRecordingPlayAdapter
                if (!entity.list.isNullOrEmpty()) {
                    sleepSoundRecordingPlayAdapter.setNewData(entity.list)
                }
                sleepSoundRecordingPlayAdapter.setBlockAction { url, bool ->
                    mBlockAction?.invoke(url, bool)
                }
            }
            ExpandableViewHoldersUtil.getInstance().addPositionInExpaned(0)
            holder.mKeepOneHolder?.bind(holder, position)
            llContainer.setOnClickListener {
                holder.mKeepOneHolder?.toggle(holder)
            }
        }
    }

    override fun getItemCount() = mData.size

    @Suppress("UNCHECKED_CAST")
    inner class SleepSoundRecordingAdapterHolder(itemView: View) :
        RecyclerView.ViewHolder(itemView), ExpandableViewHoldersUtil.Expandable {

        val mBind =
            DataBindingUtil.bind<LayoutViewSleepMonitoringReportSoundRecordingBinding>(itemView)

        var mKeepOneHolder: ExpandableViewHoldersUtil.KeepOneHolder<SleepSoundRecordingAdapterHolder>? =
            null

        init {
            mKeepOneHolder =
                ExpandableViewHoldersUtil.getInstance().keepOneHolder as? ExpandableViewHoldersUtil.KeepOneHolder<SleepSoundRecordingAdapterHolder>?
        }

        override fun getExpandView() = mBind?.recyclerView as View

        override fun doCustomAnim(isOpen: Boolean) {
            mBind?.apply {
                if (isOpen) {
                    ExpandableViewHoldersUtil.getInstance().rotateExpandIcon(img, 180F, 0F)
                } else {
                    ExpandableViewHoldersUtil.getInstance().rotateExpandIcon(img, 0F, 180F)
                }
            }
        }
    }

}