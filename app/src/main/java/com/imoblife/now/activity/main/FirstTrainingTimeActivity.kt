package com.imoblife.now.activity.main

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.now.R
import com.imoblife.now.activity.setting.BuzzerActivity
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.bean.WeekDayEntity
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.constant.ConsSp
import com.imoblife.now.databinding.LayoutAcFirstTrainingTimeBinding
import com.imoblife.now.ext.dp
import com.imoblife.now.ext.onDebounceClickListener
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.mvvm.NoViewModel
import com.imoblife.now.util.DateUtil
import com.imoblife.now.util.NotificationUtil
import com.imoblife.now.util.SpUtil
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.GridSpaceItemDecoration
import com.imoblife.now.view.dialog.OpenNotificationDialog
import com.jaychang.st.SimpleText

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/3/17
 * 描   述：首训 - 会员计划 - 设置练习提醒 - activity
 */
class FirstTrainingTimeActivity : BaseVMActivity<NoViewModel>() {

    companion object {

        fun startActivity(activity: Activity) {
            val intent = Intent(activity, FirstTrainingTimeActivity::class.java)
            activity.startActivity(intent)
            activity.overridePendingTransition(R.anim.enter_anim_slide, R.anim.exit_anim_slide)
        }

    }

    private lateinit var mBind: LayoutAcFirstTrainingTimeBinding

    private var mHour = 7
    private var mMinute = 25

    // 选中 - 日期
    private var mWeekDayValue: String = ""

    override fun getLayoutResId() = R.layout.layout_ac_first_training_time

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[NoViewModel::class.java]

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .statusBarDarkFont(true)
            .init()
    }

    override fun initView() {
        mBind = mBinding as LayoutAcFirstTrainingTimeBinding
    }

    @SuppressLint("SetTextI18n")
    override fun initData() {
        mBind.apply {
            tvTitle.text = "开始之前\n别忘记设置提醒 \uD83D\uDD57"
            tvTip.text = SimpleText
                .from("85%设置的人更快达成了自己的目标")
                .first("85%")
                .textColor(R.color.color_FF4747)
            mHour = DateUtil.getHour()
            mMinute = DateUtil.getMinute()
            numberPickerStart.value = mHour
            numberPickerEnd.value = mMinute
            numberPickerStart.setOnValueChangedListener { _, _, hour -> mHour = hour }
            numberPickerEnd.setOnValueChangedListener { _, _, minute -> mMinute = minute }
            val adapter = FirstTrainingTimeAdapter { mWeekDayValue = it }
            mBind.recyclerView.addItemDecoration(GridSpaceItemDecoration(7, 8.dp, 8.dp))
            mBind.recyclerView.adapter = adapter
            val list: MutableList<WeekDayEntity> = ArrayList()
            mWeekDayValue = SpUtil.getInstance().getStringValue(ConsSp.SP_KEY_WEEK_TIME, "")
            if (!TextUtils.isEmpty(mWeekDayValue)) {
                val split = mWeekDayValue.split(",")
                for (i in split.indices) {
                    when (i) {
                        0 -> list.add(WeekDayEntity("周日", split[i] == "1"))
                        1 -> list.add(WeekDayEntity("周一", split[i] == "1"))
                        2 -> list.add(WeekDayEntity("周二", split[i] == "1"))
                        3 -> list.add(WeekDayEntity("周三", split[i] == "1"))
                        4 -> list.add(WeekDayEntity("周四", split[i] == "1"))
                        5 -> list.add(WeekDayEntity("周五", split[i] == "1"))
                        6 -> list.add(WeekDayEntity("周六", split[i] == "1"))
                        else -> {}
                    }
                }
            } else {
                list.add(WeekDayEntity("周日", false))
                list.add(WeekDayEntity("周一", false))
                list.add(WeekDayEntity("周二", false))
                list.add(WeekDayEntity("周三", false))
                list.add(WeekDayEntity("周四", false))
                list.add(WeekDayEntity("周五", false))
                list.add(WeekDayEntity("周六", false))
            }
            adapter.setNewData(list)

            stvBtn.onDebounceClickListener {
                if (UserMgr.getInstance().isLogin) {
                    if (NotificationUtil.isNotificationEnable(this@FirstTrainingTimeActivity)) {
                        saveBuzzer()
                    } else {
                        OpenNotificationDialog().showDialog(
                            this@FirstTrainingTimeActivity,
                            ConsRequestCode.SEND_REQUEST_SETTING_NOTIFY
                        )
                    }
                } else {
                    LoginCenter.getInstance().loginControl(
                        this@FirstTrainingTimeActivity, LoginCenter.LoginStyleDialog
                    )
                }
            }
            stvRefuse.onDebounceClickListener { nextPage() }
        }
    }

    override fun startObserve() {}

    /**
     * 设置提醒
     */
    private fun saveBuzzer() {
        if (NotificationUtil.isNotificationEnable(this@FirstTrainingTimeActivity)) {
            BuzzerActivity.savePracticeRemind(1, mHour, mMinute, 1, mWeekDayValue)
            ToastUtils.showShortToastCenter(getString(R.string.string_set_successfully))
            nextPage()
        }
    }

    private fun nextPage() {
        FirstTrainingStartActivity.startActivity(this@FirstTrainingTimeActivity)
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == ConsRequestCode.SEND_REQUEST_SETTING_NOTIFY) {
            saveBuzzer()
        }
    }

    override fun onBackPressed() {}

}