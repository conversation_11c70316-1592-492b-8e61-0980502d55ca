package com.imoblife.now.activity.breath

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.core.app.ActivityOptionsCompat
import androidx.databinding.DataBindingUtil
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.view.wheelview.view.WheelView
import com.imoblife.now.R
import com.imoblife.now.activity.user.LoginCenter
import com.imoblife.now.adapter.ArrayWheelAdapter
import com.imoblife.now.databinding.ActivityBreathTimerBinding
import com.imoblife.now.model.UserMgr
import com.imoblife.now.mvvm.BaseActivity
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.RxTimer
import com.imoblife.now.viewmodel.getBreathViewModel

/**
 * 呼吸计时器
 */
class BreathTimerActivity : BaseActivity(), View.OnClickListener {

    companion object {
        @JvmStatic
        fun openTimerActivity(mContext: Context) {
            if (UserMgr.getInstance().isLogin) {
                Intent(mContext, BreathTimerActivity::class.java).apply {
                    mContext.startActivity(this)
                }
            } else {
                LoginCenter.getInstance()
                    .loginControl(mContext as Activity?, LoginCenter.LoginStyleDialog)
            }
        }
    }

    private val timingTime by lazy {
        mutableListOf<String>().apply {
            add("5")
            add("10")
            add("20")
            add("30")
            add("60")
        }
    }

    private val rxTimer by lazy {
        RxTimer()
    }

    private var isStartCountDown = false

    private lateinit var mBind: ActivityBreathTimerBinding

    override fun getLayoutResId() = 0

    override fun initView() {
        mBind = DataBindingUtil.setContentView(this, R.layout.activity_breath_timer)
        mBind.apply {
            lifecycleOwner = this@BreathTimerActivity
            toolbar.title = ""
            toolbarCenterTitleTv.text = getString(R.string.breath_timer)
            initWheelView(wvMinute)
            ivClose.setOnClickListener(this@BreathTimerActivity)
            tvStart.setOnClickListener(this@BreathTimerActivity)
            ImageLoader.loadImageLocal(
                this@BreathTimerActivity,
                R.mipmap.breath_count_down_bg,
                R.mipmap.breath_count_down_bg,
                ivBg
            )
        }
    }

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .titleBar(R.id.toolbar)
            .init()
    }

    private fun initWheelView(wheelView: WheelView) {
        wheelView.setTextSize(30F)
        wheelView.setLineSpacingMultiplier(1.5F)
        wheelView.setDividerColor(Color.TRANSPARENT)
        wheelView.setTextColorOut(Color.WHITE)
        wheelView.setTextColorCenter(Color.WHITE)
    }

    override fun initData() {
        mBind.apply {
            wvMinute.adapter = ArrayWheelAdapter(timingTime)
            wvMinute.currentItem = timingTime.indexOf("10")
        }
        getBreathViewModel(application, this)
            .breathViewModel.observe(this) {
                stopCountDown()
            }
    }

    private fun startCountDown() {
        isStartCountDown = true
        mBind.apply {
            tvStart.setBackgroundResource(R.drawable.breath_white_border_round)
            tvStart.setTextColor(Color.parseColor("#FFFFFF"))
            tvStart.text = getString(R.string.stop)
            groupCountDown.visibility = View.GONE
            tvCountDown.visibility = View.VISIBLE
        }
        startTiming()
    }

    private fun stopCountDown() {
        isStartCountDown = false
        rxTimer.cancel()
        mBind.apply {
            tvStart.setBackgroundResource(R.drawable.breath_white_round)
            tvStart.setTextColor(Color.parseColor("#000000"))
            tvStart.text = getString(R.string.start)
            tvCountDown.visibility = View.GONE
            groupCountDown.visibility = View.VISIBLE
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_close -> {
                finish()
            }
            R.id.tv_start -> {
                if (!isStartCountDown) {
                    startCountDown()
                } else {
                    stopCountDown()
                }
            }
            else -> {}
        }
    }

    //开始定时
    private fun startTiming(millisInFuture: Long = 3000L) {
        rxTimer.timerDown(0, millisInFuture, object : RxTimer.RxAction {
            override fun onComplete() {
                isStartCountDown = false
                mBind.tvCountDown.visibility = View.GONE
                jumpStartTimerActivity()
            }

            override fun action(number: Long) {
                if (!isStartCountDown)
                    isStartCountDown = true
                val timer = (number / 1000 + 1).toString()
                mBind.tvCountDown.text = timer
            }
        })
    }

    private fun jumpStartTimerActivity() {
        val minute = mBind.wvMinute.currentItem
        val second = timingTime[minute].toInt() * 60
        Intent(this, BreathStartTimerActivity::class.java).apply {
            val bundle = Bundle()
            bundle.putInt("COUNT_DOWN_TIME", second)
            this.putExtras(bundle)
            val optionsCompat =
                ActivityOptionsCompat
                    .makeSceneTransitionAnimation(
                        this@BreathTimerActivity,
                        mBind.tvStart,
                        "transition_timer"
                    )
            startActivity(this, optionsCompat.toBundle())
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        rxTimer.cancel()
    }

}