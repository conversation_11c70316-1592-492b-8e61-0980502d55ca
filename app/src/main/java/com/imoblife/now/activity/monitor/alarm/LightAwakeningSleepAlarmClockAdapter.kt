package com.imoblife.now.activity.monitor.alarm

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.databinding.LayoutViewLightAwakeningSleepAlarmClockBinding
import com.imoblife.now.ext.getBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：轻唤醒 - 睡眠闹钟 - Adapter
 */
class LightAwakeningSleepAlarmClockAdapter(private val mBlock: (() -> Unit)? = null) :
    BaseQuickAdapter<Int, BaseViewHolder>(R.layout.layout_view_light_awakening_sleep_alarm_clock) {

    override fun convert(holder: BaseViewHolder, item: Int) {
        holder.getBinding(LayoutViewLightAwakeningSleepAlarmClockBinding::bind).apply {
            tvTime.text = mContext.getString(R.string.string_minute_count, item)
            mBlock?.let { action ->
                root.setOnClickListener {
                    action.invoke()
                }
            }
        }
    }

}