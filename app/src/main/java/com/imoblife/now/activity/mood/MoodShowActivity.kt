package com.imoblife.now.activity.mood

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.adapter.MoodShowAdapter
import com.imoblife.now.bean.MoodLog
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityMoodShowBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.dialog.MoodDiaryDeleteAlterDialog
import org.greenrobot.eventbus.EventBus

class MoodShowActivity : BaseVMActivity<MoodModel>() {

    private lateinit var moodShowAdapter: MoodShowAdapter
    private var mMood: MoodLog? = null
    private val mMoodLogs by lazy(LazyThreadSafetyMode.NONE) { arrayListOf<MoodLog>() }

    // 右上角是否为删除｜编辑
    private var mIsRemoveOrEdit = false

    // 内容文本
    private var mContent: String = ""

    companion object {
        @JvmStatic
        fun startShowMood(context: Activity, mood: MoodLog, requestCode: Int) {
            Intent(context, MoodShowActivity::class.java).apply {
                putExtra(ConsIntent.BUNDLE_MOOD_DATE, mood)
                context.startActivityForResult(this, requestCode)
            }
        }
    }

    private lateinit var mBind: ActivityMoodShowBinding

    override fun getLayoutResId() = R.layout.activity_mood_show

    override fun initImmersionBar() = ImmersionBar.with(this).transparentStatusBar().init()

    override fun superInit(intent: Intent?) {
        if (hasExtra(intent, ConsIntent.BUNDLE_MOOD_DATE)) {
            mMood = intent?.getSerializableExtra(ConsIntent.BUNDLE_MOOD_DATE) as MoodLog?
        }
    }

    override fun initVM() =
        ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(MoodModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityMoodShowBinding
        mBind.apply {
            mMood?.let { entity ->
                stvBg.apply {
                    shaderStartColor = Color.parseColor(entity.background_start_color)
                    shaderEndColor = Color.parseColor(entity.background_end_color)
                }
            }
            moodTitle.apply {
                leftImg.setOnClickListener { onBackPressed() }
                tvTitle.text = getString(R.string.string_mood_diary_txt)
                rightTitle.apply {
                    text = getString(R.string.delete_txt)
                    setTextColor(
                        ContextCompat.getColor(
                            this@MoodShowActivity, R.color.color_FF5151
                        )
                    )
                    solid = ContextCompat.getColor(this@MoodShowActivity, R.color.white)
                    setOnClickListener {
                        if (mIsRemoveOrEdit) {
                            // 编辑
                            mMood?.let { entity ->
                                DialogUtil.showWaitLoading()
                                mViewModel.editSaveDiary(
                                    entity.id,
                                    entity.content_type ?: "",
                                    mContent
                                )
                            }
                        } else {
                            // 删除
                            MoodDiaryDeleteAlterDialog(this@MoodShowActivity) {
                                DialogUtil.showWaitLoading()
                                mMoodLogs[viewpager.currentItem].apply {
                                    mViewModel.deleteMoodLog(id, content_type ?: "")
                                }
                            }.showDialog()
                        }
                    }
                }
            }
            moodShowAdapter = MoodShowAdapter()
            viewpager.orientation = ViewPager2.ORIENTATION_HORIZONTAL
            viewpager.adapter = moodShowAdapter
            // vp2移除滑动到边界的阴影
            val view: View = viewpager.getChildAt(0)
            if (view is RecyclerView) {
                view.overScrollMode = View.OVER_SCROLL_NEVER
            }
            moodShowAdapter.setBlockAction { bool, content ->
                mContent = content
                mIsRemoveOrEdit = bool
                moodTitle.rightTitle.apply {
                    if (bool) {
                        text = getString(R.string.save)
                        setTextColor(
                            ContextCompat.getColor(
                                this@MoodShowActivity,
                                R.color.main_color
                            )
                        )
                    } else {
                        text = getString(R.string.delete_txt)
                        setTextColor(
                            ContextCompat.getColor(
                                this@MoodShowActivity,
                                R.color.color_FF5151
                            )
                        )
                    }
                }
            }
        }
    }

    override fun initData() {
        mMood?.let {
            mMoodLogs.add(it)
            moodShowAdapter.setNewData(mMoodLogs)
        } ?: run {
            setResult(Activity.RESULT_OK)
            finish()
        }
    }

    override fun startObserve() {
        mViewModel.apply {
            deleteMoodLog.observe(this@MoodShowActivity) {
                DialogUtil.hideWaitLoading()
                if (it.isSuccess) {
                    Thread {
                        Thread.sleep(100)
                        setResult(Activity.RESULT_OK)
                        finish()
                    }.start()
                } else {
                    ToastUtils.showShortToast(it.failureData)
                }
            }
            // 编辑保存我的心情记录
            editSaveDiary.observe(this@MoodShowActivity) {
                DialogUtil.hideWaitLoading()
                if (it.isSuccess) {
                    it.successData?.let { bool ->
                        if (bool) {
                            mMood?.let { entity ->
                                entity.content = mContent
                                mMoodLogs.clear()
                                mMoodLogs.add(entity)
                                moodShowAdapter.setNewData(mMoodLogs)
                                // 通知心情日记历史页面刷新数据
                                EventBus.getDefault()
                                    .post(BaseEvent(ConsEventCode.REFRESH_MOOD_HISTORY_MOOD_LOG))
                            }
                            ToastUtils.showShortToast(getString(R.string.save_hint))
                        } else {
                            ToastUtils.showShortToast(getString(R.string.string_failed_to_save_please_try_again))
                        }
                    }
                        ?: ToastUtils.showShortToast(getString(R.string.string_failed_to_save_please_try_again))
                } else {
                    ToastUtils.showShortToast(getString(R.string.string_failed_to_save_please_try_again))
                }
            }
        }
    }

}