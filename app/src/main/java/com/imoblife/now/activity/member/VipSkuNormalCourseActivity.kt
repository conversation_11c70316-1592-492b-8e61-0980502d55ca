package com.imoblife.now.activity.member

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.adapter.SkuStyleVipImgAdapter
import com.imoblife.now.bean.SubSkuInfo
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.databinding.ActivityVipSkuNormalCourseBinding
import com.imoblife.now.ext.preLoadImg
import com.imoblife.now.ext.removeAnim
import com.imoblife.now.ext.slideInFromLeft
import com.imoblife.now.ext.slideOutToRight
import com.imoblife.now.model.ConfigMgr
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.payment.PayCenter
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.viewmodel.PaymentViewModel

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/3/18
 * 描   述：课程内容营销全屏页
 *
 * 界面UI结构 =>
 * 头部 - 静图
 * 中部 - 课程标题
 * 底部 - 静图
 * 底部悬浮「sku - RecyclerView & 自定义View - 悬浮按钮｜倒计时」
 */
class VipSkuNormalCourseActivity : BaseVMActivity<PaymentViewModel>() {

    companion object {

        @JvmStatic
        fun startActivity(context: Activity, courseId: Int) {
            Intent(context, VipSkuNormalCourseActivity::class.java).apply {
                putExtra(ConsIntent.BUNDLE_VIP_SKU_NORMAL_COURSE_ID, courseId)
                context.startActivity(this)
                context.slideInFromLeft()
            }
        }

    }

    private lateinit var mBind: ActivityVipSkuNormalCourseBinding

    private var skuStyleHorizontalAdapter: SkuStyleVipImgAdapter? = null

    private var mSkuInfo: SubSkuInfo? = null

    private var mCourseId = 0

    override fun superInit(intent: Intent?) {
        intent?.let {
            if (hasExtra(ConsIntent.BUNDLE_VIP_SKU_NORMAL_COURSE_ID)) {
                mCourseId = it.getIntExtra(ConsIntent.BUNDLE_VIP_SKU_NORMAL_COURSE_ID, 0)
            }
        }
    }

    override fun getLayoutResId() = R.layout.activity_vip_sku_normal_course

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .init()
    }

    override fun initVM() = ViewModelProvider(this)[PaymentViewModel::class.java]

    override fun initView() {
        mBind = mBinding as ActivityVipSkuNormalCourseBinding
        mBind.apply {
            lifecycle.addObserver(bottomVipTimerView)
            recyclerSku.removeAnim()
            bottomVipTimerView.setSubmitPayOnClickListener {
                skuStyleHorizontalAdapter?.getSubscribe()?.let {
                    mBind.subProtocolPrivacy.isAgreePrivacy(it, "课程内容营销全屏页") {
                        PayCenter.getInstance().doSubmitPay(this@VipSkuNormalCourseActivity, it)
                    }
                } ?: let {
                    ToastUtils.showShortToastCenter(getString(R.string.string_please_select_subscribe))
                }
            }
            closeImg.setOnClickListener { finish() }
        }
    }

    override fun initData() {
        mViewModel.getNormalCourseSku(mCourseId)
    }

    override fun startObserve() {
        mViewModel.normalCourseSku.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    updateUI(entity)
                }
            }
        }
    }

    /**
     * 更新UI
     *
     * @param entity SubSkuInfo
     */
    private fun updateUI(entity: SubSkuInfo) {
        entity.preLoadImg(this)
        setSukInfo(entity)
    }

    private fun setSukInfo(subSkuInfo: SubSkuInfo?) {
        subSkuInfo?.let {
            mSkuInfo = it

            SensorsDataEvent.contentMarketingPageExposure(it.course_type_name)

            mBind.apply {
                tvContent.text = it.course_name
                tvContentBottom.text = it.prompt_message

                ImageLoader.loadImageUrl(
                    this@VipSkuNormalCourseActivity,
                    it.banner,
                    imgTop
                )
                ImageLoader.loadImageUrl(
                    this@VipSkuNormalCourseActivity,
                    it.bottom_img,
                    imgBottom
                )
                ImageLoader.loadImageUrl(
                    this@VipSkuNormalCourseActivity,
                    it.close_img,
                    closeImg,
                    R.mipmap.icon_vip_sku_close
                )

                val bgColor = "#FFFCF5"
                slContainer.setLayoutBackground(Color.parseColor(bgColor))
                bottomVipTimerView.setBgColor(bgColor)
                subProtocolPrivacy.setBgColor(bgColor)

                if (subSkuInfo.style == 1) {
                    skuStyleHorizontalAdapter =
                        SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_dynamic_img_size)
                    recyclerSku.layoutManager =
                        LinearLayoutManager(
                            this@VipSkuNormalCourseActivity,
                            LinearLayoutManager.VERTICAL,
                            false
                        )
                } else {
                    skuStyleHorizontalAdapter =
                        SkuStyleVipImgAdapter(R.layout.layout_sku_vip_item_style_a, it.isLocalFlag)
                    recyclerSku.layoutManager =
                        LinearLayoutManager(
                            this@VipSkuNormalCourseActivity,
                            LinearLayoutManager.HORIZONTAL,
                            false
                        )
                }
                skuStyleHorizontalAdapter?.setActionBlock { _ -> setSubPrivacy() }
                recyclerSku.adapter = skuStyleHorizontalAdapter
                skuStyleHorizontalAdapter?.setNewData(subSkuInfo.sku_list)
                bottomVipTimerView.setPayButtonData(subSkuInfo.pay_button)
            }
        }
    }

    private fun setSubPrivacy() {
        mBind.subProtocolPrivacy.setAgreementCheckboxFalse()
        skuStyleHorizontalAdapter?.getSubscribe()?.let { sub ->
            mBind.subProtocolPrivacy.setData(
                sub,
                ConfigMgr.getInstance().config.isAuto_vip_privacy_ob
            )
            mBind.bottomVipTimerView.setSubscribeFlagImg(sub.sku_offer_tag_url)
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        event?.let {
            when (it.eventCode) {
                ConsEventCode.PAY_SUCCESS_EVENT -> finish()

                ConsEventCode.PAY_IN_PROGRESS_EVENT -> DialogUtil.showWaitLoading(
                    this,
                    getString(R.string.string_order_validation_txt),
                    false
                )

                else -> {}
            }
        }
    }

    override fun onBackPressed() {}

    override fun finish() {
        super.finish()
        slideOutToRight()
    }

}