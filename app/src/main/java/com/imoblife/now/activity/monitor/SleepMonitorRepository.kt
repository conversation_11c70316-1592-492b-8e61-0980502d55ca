package com.imoblife.now.activity.monitor

import androidx.lifecycle.MutableLiveData
import com.czt.mp3recorder.MP3Recorder
import com.imoblife.now.bean.*
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiServiceSleep
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers
import com.imoblife.now.repository.OssRepository
import com.imoblife.now.sleep.SleepMonitorDb
import com.imoblife.now.sleep.SleepSoundBite
import com.imoblife.now.util.EmptyUtils
import com.imoblife.now.util.XLog
import io.reactivex.Observable
import org.json.JSONArray
import java.io.File

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/5/25
 * 描   述：睡眠监测 - Repository
 */
class SleepMonitorRepository : BaseRepository() {

    private val ossRepository by lazy { OssRepository() }
    private val sleepMonitorDb by lazy { SleepMonitorDb() }

    // 按月获取所有睡眠历程历程
    private var mCursorAllSleepHistory: Int = 1

    fun uploadSleepMonitorData(mutableLiveData: MutableLiveData<UiStatus<List<GenerateSleepReportEntity>>>? = null) {
        XLog.e("tag", "SleepMonitorRepository=====uploadSleepMonitorData===========1")
        Observable.create<String> {
            Thread.sleep(100)
            val sleepLog = sleepMonitorDb.getAllSleepRecord()
            XLog.e("tag", "SleepMonitorRepository=====sleepLog===========${sleepLog}")
            var logJson = JSONArray()
            sleepLog.forEach { sleepAndMonitor ->
                var sleepRecord = sleepAndMonitor.getSleepRecord()
                var monitorRecord = JSONArray()
                sleepAndMonitor.sleepMonitors?.forEach { monitor ->
                    val itemData =
                        "${monitor.point_time / 1000}|${monitor.decibel}|${monitor.move}|${monitor.light}"
                    monitorRecord.put(itemData)
                }
                sleepRecord.setPoint_list(monitorRecord.toString())
                logJson.put(sleepRecord.toJson())
            }
            XLog.e("tag", "SleepMonitorRepository=====uploadSleepMonitorData===========$logJson")
            it.onNext(logJson.toString())
        }.flatMap {
            ApiClient.getInstance().createService(ApiServiceSleep::class.java)
                .sleepMonitorRecord(it)
        }.compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<GenerateSleepReportEntity>>>() {
                override fun onSuccess(response: BaseResult<List<GenerateSleepReportEntity>>?) {
                    XLog.e("tag", "SleepMonitorRepository=====response===========$response")
                    sleepMonitorDb.deleteAllSleepRecord()
                    response?.result?.let {
                        mutableLiveData?.value = UiStatus(true, it)
                    } ?: let {
                        mutableLiveData?.value = UiStatus(isSuccess = true, null)
                    }
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    mutableLiveData?.value = UiStatus(isSuccess = false, null)
                }
            })


    }

    fun uploadSleepSoundBite(soundBite: SleepSoundBite) {
        val filePath = soundBite.local_path ?: return
        val file = File(filePath)
        if (!file.exists() || file.length() == 0L) {
            return
        }
        XLog.e("uploadSleepSoundBite=========>${soundBite.toString()}")
        ApiClient.getInstance().createService(ApiServiceSleep::class.java)
            .uploadSoundClass(
                soundBite.sleep_only_id,
                soundBite.start_time / 1000,
                soundBite.end_time / 1000,
                soundBite.sound_features
            )
            .compose(RxSchedulers.composeIO2IO())
            .subscribe(object : BaseObserver<BaseResult<SoundBiteId>>() {
                override fun onSuccess(response: BaseResult<SoundBiteId>?) {
                    response?.result?.id?.let {
                        if (it > 0) {
                            upLoadFile(it, filePath)
                        } else {
                            deleteFile(filePath)
                        }
                    } ?: deleteFile(filePath)
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    MP3Recorder.deleteFile(soundBite.local_path)
                }
            })

    }

    fun bindSleepSoundBiteUrl(soundBiteId: Int, soundBiteUrl: String) {
        ApiClient.getInstance().createService(ApiServiceSleep::class.java)
            .bindSoundBiteUrl(soundBiteId, soundBiteUrl)
            .compose(RxSchedulers.composeIO2IO())
            .subscribe(object : BaseObserver<BaseResult<Boolean>>() {
                override fun onSuccess(response: BaseResult<Boolean>?) {}
            })
    }

    private fun upLoadFile(soundBiteId: Int, file: String) {
        ossRepository.getOssInfo(File(file), ossUploadFileListener = object :
            OssRepository.OSSUploadFileListener {
            override fun uploadFile(isSuccess: Boolean, fileUrl: String) {
                XLog.e("SleepMonitorRepository=====upLoadFile=======${isSuccess}====${fileUrl}")
                if (isSuccess) {
                    bindSleepSoundBiteUrl(soundBiteId, fileUrl)
                }
                deleteFile(file)
            }
        })
    }

    private fun deleteFile(file: String? = null) {
        try {
            MP3Recorder.deleteFile(file)
        } catch (th: Throwable) {
            th.printStackTrace()
            XLog.i("======录音文件删除失败====")
        }
    }

    /**
     * 按月获取所有睡眠历程历程
     */
    fun getMonthSleepList(
        initPage: Boolean,
        liveData: MutableLiveData<UiStatus<List<AllSleepHistoryEntity>>>
    ) {
        if (initPage) mCursorAllSleepHistory = 1
        ApiClient
            .getInstance()
            .createService(ApiServiceSleep::class.java)
            .getMonthSleepList(mCursorAllSleepHistory, 20)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<AllSleepHistoryEntity>>>() {
                override fun onSuccess(response: BaseResult<List<AllSleepHistoryEntity>>?) {
                    response?.result?.let {
                        if (EmptyUtils.isNotEmpty(it)) {
                            if (mCursorAllSleepHistory == 1) {
                                liveData.value =
                                    UiStatus(true, it, null, Status.REFRESHSUCCESS)
                            } else {
                                liveData.value =
                                    UiStatus(true, it, null, Status.MORESUCCESS)
                            }
                            mCursorAllSleepHistory++
                        } else {
                            checkStatus(liveData)
                        }
                    } ?: checkStatus(liveData)
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    if (mCursorAllSleepHistory == 1) {
                        liveData.value = UiStatus(false, null, null, Status.FAILED)
                    } else {
                        liveData.value =
                            UiStatus(false, null, null, Status.MOREFAIL)
                    }
                }
            })
    }

    /**
     * 检查 - 按月获取所有睡眠历程历程 - cursor 状态
     */
    private fun checkStatus(liveData: MutableLiveData<UiStatus<List<AllSleepHistoryEntity>>>) {
        if (mCursorAllSleepHistory == 1) {
            liveData.value = UiStatus(true, null, null, Status.EMPTYDATA)
        } else {
            liveData.value = UiStatus(true, null, null, Status.NOMOREDATA)
        }
    }

    /**
     * 获取当次睡眠监测报告
     *
     * @param cursor 服务器返回的游标 0 <= ？左（启用，可点击） ｜ 0 = 当天 ｜ 0 > ？右（未启用，不可点击）
     */
    fun getMonitoringReport(
        liveData: MutableLiveData<UiStatus<MonitoringReportEntity>>,
        cursor: Int
    ) {
        ApiClient
            .getInstance()
            .createService(ApiServiceSleep::class.java)
            .getMonitoringReport(cursor)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MonitoringReportEntity>>() {
                override fun onSuccess(response: BaseResult<MonitoringReportEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        liveData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 获取睡眠历程
     */
    fun getSleepRecord(
        liveData: MutableLiveData<UiStatus<MonitoringReportCourseEntity>>,
        type: Int,
        cursor: Int
    ) {
        ApiClient
            .getInstance()
            .createService(ApiServiceSleep::class.java)
            .getSleepRecord(type, cursor)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<MonitoringReportCourseEntity>>() {
                override fun onSuccess(response: BaseResult<MonitoringReportCourseEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        liveData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 获取铃声列表
     */
    fun getAlarmList(liveData: MutableLiveData<UiStatus<SleepAlarmClockEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiServiceSleep::class.java)
            .alarmList
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<SleepAlarmClockEntity>>() {
                override fun onSuccess(response: BaseResult<SleepAlarmClockEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        liveData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 获取铃声列表
     */
    fun deleteSleepRecord(sleepId: Int, liveData: MutableLiveData<UiStatus<Boolean>>) {
        ApiClient
            .getInstance()
            .createService(ApiServiceSleep::class.java)
            .deleteSleepRecord(sleepId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<Boolean>>() {
                override fun onSuccess(response: BaseResult<Boolean>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        liveData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

    /**
     * 睡眠监测 - 完成 - sku
     */
    fun getSleepMonitorCompleteSku(liveData: MutableLiveData<UiStatus<SleepMonitorCompleteSkuEntity>>) {
        ApiClient
            .getInstance()
            .createService(ApiServiceSleep::class.java)
            .firstReport
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<SleepMonitorCompleteSkuEntity>>() {
                override fun onSuccess(response: BaseResult<SleepMonitorCompleteSkuEntity>?) {
                    response?.result?.let {
                        liveData.value = UiStatus(true, it, "", Status.REFRESHSUCCESS)
                    } ?: let {
                        liveData.value = UiStatus(true, null, "", Status.EMPTYDATA)
                    }
                }

                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.FAILED)
                }
            })
    }

}