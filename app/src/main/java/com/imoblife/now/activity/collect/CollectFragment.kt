package com.imoblife.now.activity.collect

import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.ConcatAdapter
import com.flyco.tablayout.SlidingTabLayout
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.mvvm.BaseVMFragment
import com.imoblife.now.R
import com.imoblife.now.adapter.LikeCourseAdapter
import com.imoblife.now.adapter.LikeTrackAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.adapter.home.HomeAdAdapter
import com.imoblife.now.bean.CollectTrackBean
import com.imoblife.now.bean.Course
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.FragmentCollectBinding
import com.imoblife.now.statistics.SensorsDataEvent
import com.imoblife.now.util.DisplayUtil
import com.imoblife.now.view.EmptyViewUtils
import com.imoblife.now.viewmodel.AdViewModel

class CollectFragment() : BaseVMFragment<CollectViewModel>() {

    constructor(index: Int) : this() {
        type = index
    }

    private val mAdViewModel by viewModels<AdViewModel>()
    private lateinit var mBind: FragmentCollectBinding
    private val mHomeAdAdapter by lazy {
        HomeAdAdapter(mData = mutableListOf(),
            mHasAd = true,
            mShowClose = true)
    }

    private var type: Int = 0

    companion object {
        fun geInstance(index: Int): CollectFragment {
            return CollectFragment(index)
        }
    }

    private val likeCourseAdapter by lazy { LikeCourseAdapter() }
    private val likeTrackAdapter by lazy { LikeTrackAdapter() }

    private var mConcatAdapter: ConcatAdapter? = null

    override fun getLayoutResId() = R.layout.fragment_collect

    override fun initVM() = ViewModelProvider(this).get(CollectViewModel::class.java)

    override fun initView() {
        mBind=mBinding as FragmentCollectBinding
        if (type == 0) {
            mConcatAdapter = ConcatAdapter(mHomeAdAdapter, likeCourseAdapter)
            mBind.recyclerView.adapter = mConcatAdapter
            likeCourseAdapter.setAdapterOnItemClick { _, data ->
                if (data is Course){
                    mViewModel?.addCollectCourse(data)
                    SensorsDataEvent.collection(
                        data.id,
                        data.title,
                        -1,
                        "",
                        getString(R.string.string_big_course_txt),
                        false,
                        getString(R.string.string_my_collect_list)
                    )

                }
            }
        } else if (type == 1) {
            mConcatAdapter = ConcatAdapter(mHomeAdAdapter, likeTrackAdapter)
            mBind.recyclerView.adapter = mConcatAdapter
            likeTrackAdapter.setAdapterOnItemClick { _, data ->
                val dataTrack = data as CollectTrackBean
                mViewModel?.addCollectTrack(dataTrack.course_id, data.section_id)
                SensorsDataEvent.collection(
                    dataTrack.course_id,
                    dataTrack.title,
                    dataTrack.section_id,
                    dataTrack.section_title,
                    getString(R.string.string_small_course_txt),
                    false,
                    getString(R.string.string_my_collect_list),
                )
            }
        }
        mBind.recyclerView.addItemDecoration(CommonItemDecoration(0,
            DisplayUtil.dip2px(18f),
            0,
            DisplayUtil.dip2px(18f),
            0,
            DisplayUtil.dip2px(18f)))
        mBind.swipeRefresh.setOnRefreshListener { getData() }
    }

    override fun onFragmentFirstVisible() {
        super.onFragmentFirstVisible()
        loadingHelper.showLoadingView()
        getData()
        mAdViewModel.getCampaignTriggerAds(ConsCommon.POSITION_IN_BREAK_COLLECTION_PAGE)
    }

    private fun getData() {
        if (type == 0) {
            mViewModel?.getCollectCourse(false)
        } else if (type == 1) {
            mViewModel?.getCollectTrack()
        }
    }

    override fun initData() {

    }

    override fun startObserve() {
        mViewModel?.apply {
            collectCourses.observe(this@CollectFragment, Observer {
                mBind.swipeRefresh.finishRefresh()
                loadingHelper.showContentView()
                it.successData?.let { it1 -> likeCourseAdapter.replaceData(it1) }
                if (it.successData.isNullOrEmpty()) {
                    likeCourseAdapter.emptyView =
                        EmptyViewUtils.getEmptyCollectView(requireActivity()) { getData() }
                }
            })
            collectTracks.observe(this@CollectFragment, Observer {
                mBind.swipeRefresh.finishRefresh()
                loadingHelper.showContentView()
                it.successData?.let { it1 -> likeTrackAdapter.replaceData(it1) }
                if (it.successData.isNullOrEmpty()) {
                    likeTrackAdapter.emptyView =
                        EmptyViewUtils.getEmptyCollectView(requireActivity()) { initData() }
                }
            })
            collectCourseChange.observe(this@CollectFragment, Observer {
                if (it.isSuccess) {
                    mViewModel?.getCollectCourse(false)
                }
            })
            collectTrackChange.observe(this@CollectFragment, Observer {
                if (it.isSuccess) {
                    mViewModel?.getCollectTrack()
                }
            })
        }

        mAdViewModel.campaignTriggerAds.observe(viewLifecycleOwner) {
            if (it.isSuccess) {
                it.successData?.let { ad ->
                    mHomeAdAdapter.setNewData(mutableListOf(ad))
                }
            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        when (event?.eventCode) {
            ConsEventCode.CLICK_AD_CLOSE_BTN_REFRESH_ADAPTER -> {
                if (activity is CollectActivity) {
                    val tab =
                        (activity as CollectActivity).findViewById<SlidingTabLayout>(R.id.tabLayout)
                    if (tab.currentTab == type) mHomeAdAdapter.setNewData(mutableListOf())
                }
            }
            // 收藏状态 => 刷新 - 睡眠 - 大自然声
            ConsEventCode.NATURAL_SOUND_COLLECT_CHANGE_EVENT -> getData()
            else -> {}
        }
    }

}