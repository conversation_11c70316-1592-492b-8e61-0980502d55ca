package com.imoblife.now.activity.breath

import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.imoblife.now.R
import com.imoblife.now.bean.BreathStaticsEntity
import com.imoblife.now.databinding.LayoutItemBreathStaticsTopDateBinding
import com.imoblife.now.ext.getBinding

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/10/16
 * 描   述：呼吸「历史」 - TopDate - Adapter
 */
class BreathStaticsTopDateAdapter : BaseQuickAdapter<BreathStaticsEntity.ListEntity, BaseViewHolder>(R.layout.layout_item_breath_statics_top_date) {

    override fun convert(holder: BaseViewHolder, item: BreathStaticsEntity.ListEntity?) {
        item?.let {
            holder.getBinding(LayoutItemBreathStaticsTopDateBinding::bind).apply {
                if (it.is_completed == 0) {
                    groupContentComplete.visibility = View.GONE
                    imgDefault.visibility = View.VISIBLE
                } else {
                    groupContentComplete.visibility = View.VISIBLE
                    imgDefault.visibility = View.INVISIBLE
                }
                tvDateTop.text = it.week_string
                tvMin.text = it.totalDuration.toString()
                tvDateBottom.text = it.date_string
            }
        }
    }

}