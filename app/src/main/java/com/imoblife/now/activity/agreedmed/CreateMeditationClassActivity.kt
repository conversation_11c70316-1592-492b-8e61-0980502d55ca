package com.imoblife.now.activity.agreedmed

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.distinctUntilChanged
import com.imoblife.commlibrary.utils.LoadingHelper
import com.imoblife.now.R
import com.imoblife.now.activity.user.UImageActivity
import com.imoblife.now.activity.wallet.AgreedPaymentActivity
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.MeditationTeamRulesEntity
import com.imoblife.now.constant.ConsIntent
import com.imoblife.now.constant.ConsRequestCode
import com.imoblife.now.databinding.LayoutAcCreateMeditationClassBinding
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.util.ImageLoader
import com.imoblife.now.util.ToastUtils
import com.imoblife.now.view.dialog.MeditationClassBottomDialog
import com.imoblife.now.view.dialog.OnClickViewListener
import java.io.File

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-12
 * 描   述：创建约定冥想班
 */
class CreateMeditationClassActivity : BaseVMActivity<CreateMeditationClassViewModel>() {

    // 开始时间
    private val mStartTimeList: MutableList<String> by lazy { mutableListOf() }
    private val mStartTimeEntityList: MutableList<MeditationTeamRulesEntity.StartDate> by lazy { mutableListOf() }
    private var mStartDateKeyContent = ""

    // 周期
    private val mPeriodList: MutableList<String> by lazy { mutableListOf() }

    // 打卡次数
    private val mClockFrequencyList: MutableList<String> by lazy { mutableListOf() }

    // 每次时长
    private val mEveryTimePeriodList by lazy { resources.getStringArray(R.array.meditation_time_duration_rang) }

    // 图片Url
    private var mImgUrl: String? = ""

    // 人数上限
    private var mCeilingOfPersonnelContent = ""

    // 单次约定金
    private var mPaymentForASingleEngagementContent = ""

    private val mTextContent by lazy { "请选择（必填）" }

    // 人数上限_hint
    private var mHintCeilingOfPersonnel = "请输入冥想班人数"

    // 单次约定金_hint
    private var mHintPaymentForASingleEngagement = "请输入约定金"

    companion object {
        fun startActivity(context: Context) {
            Intent(context, CreateMeditationClassActivity::class.java).let {
                context.startActivity(it)
            }
        }
    }

    private lateinit var mBind: LayoutAcCreateMeditationClassBinding

    private lateinit var loadingHelper: LoadingHelper

    override fun getLayoutResId() = R.layout.layout_ac_create_meditation_class

    override fun superInit(intent: Intent?) {
    }

    override fun initVM() = ViewModelProvider(this, ViewModelProvider.NewInstanceFactory()).get(
        CreateMeditationClassViewModel::class.java
    )

    override fun initView() {
        loadingHelper = ToolbarUtils.setToolbar(
            this,
            getString(R.string.string_create_meditation_class),
            NavIconType.BACK,
            true
        )
        mBind = mBinding as LayoutAcCreateMeditationClassBinding
        mBind.apply {
            clickProxy = ClickProxy()
            // 人数上限
            mrvCeilingOfPersonnel.mEtTextChangeListener = { type, number, hint ->
                if (type == 0) {
                    mCeilingOfPersonnelContent = number
                    mHintCeilingOfPersonnel = hint
                }
            }
            // 单次约定金
            mrvPaymentForASingleEngagement.mEtTextChangeListener = { type, number, hint ->
                if (type == 1) {
                    mPaymentForASingleEngagementContent = number
                    if (mTextContent != mrvClockFrequency.getTvText()) {
                        // 合计约定金 = 单次约定金 * 打卡次数
                        tvTotalAgreementMoney.text = getString(
                            R.string.string_money_total,
                            mrvClockFrequency.getTvText().toDouble() * number.toDouble()
                        )
                    }
                    mHintPaymentForASingleEngagement = hint
                }
            }
            etClassName.addTextChangedListener(onTextChanged = { text, _, _, _ ->
                if (!text.isNullOrBlank() && text.length >= 14) ToastUtils.showShortToastCenter(
                    getString(R.string.string_input_txt_edit_more_words)
                )
            })
            etClassDes.addTextChangedListener(onTextChanged = { text, _, _, _ ->
                if (!text.isNullOrBlank() && text.length >= 149) ToastUtils.showShortToastCenter(
                    getString(R.string.string_input_txt_more_words)
                )
            })
        }
    }

    override fun initData() {
        DialogUtil.showWaitLoading()
        mViewModel.getMeditationTeamRules()
    }

    override fun startObserve() {
        mViewModel.apply {
            meditationTeamRulesEntity
                .distinctUntilChanged()
                .observe(this@CreateMeditationClassActivity) {
                    DialogUtil.hideWaitLoading()
                    if (it.isSuccess) {
                        it.successData?.apply {
                            mStartTimeEntityList.clear()
                            mStartTimeList.clear()
                            mPeriodList.clear()
                            // 开始时间
                            mStartTimeEntityList.addAll(start_date)
                            start_date.forEach { entity ->
                                mStartTimeList.add(entity.name)
                            }
                            // 周期
                            val min = period.min
                            val max = period.max
                            (min..max).forEach { index ->
                                mPeriodList.add(index.toString())
                            }
                            mBind.apply {
                                // 人数上限
                                mrvCeilingOfPersonnel.setEtText(
                                    getString(
                                        R.string.string_et_txt_required_number,
                                        user_count.min,
                                        user_count.max
                                    ), user_count.min, user_count.max, 0
                                )
                                // 单次约定金
                                mrvPaymentForASingleEngagement.setEtText(
                                    getString(
                                        R.string.string_et_txt_required_number,
                                        pre_money.min,
                                        pre_money.max
                                    ), pre_money.min, pre_money.max, 1
                                )
                            }
                        }
                    }
                }
            meditationClassCreateEntity.observe(this@CreateMeditationClassActivity) {
                DialogUtil.hideWaitLoading()
                if (it.isSuccess) {
                    AgreedPaymentActivity.startAgreedPaymentActivity(
                        this@CreateMeditationClassActivity,
                        it.successData
                    )
                }
            }
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                // 头像
                R.id.imgHeadPortraits -> {
                    val intent =
                        Intent(this@CreateMeditationClassActivity, UImageActivity::class.java)
                    startActivityForResult(intent, ConsRequestCode.SEND_REQUEST_UPLOAD_IMAGE)
                }
                // 开始时间
                R.id.mrvStartTime -> {
                    if (mStartTimeList.isNotEmpty()) {
                        MeditationClassBottomDialog(
                            mStartTimeList,
                            getString(R.string.string_select_start_time)
                        ).show(this@CreateMeditationClassActivity,
                            object : OnClickViewListener {
                                override fun onImgConfirmClick(item: String?) {
                                    mBind.mrvStartTime.setTvText(item.toString())
                                    mStartTimeEntityList.forEach { entity ->
                                        if (entity.name == item) {
                                            mStartDateKeyContent = entity.id.toString()
                                            return@forEach
                                        }
                                    }
                                }
                            })
                    } else ToastUtils.showShortToast(getString(R.string.string_system_error))
                }
                // 周期
                R.id.mrvPeriod -> {
                    if (mPeriodList.isNotEmpty()) {
                        MeditationClassBottomDialog(
                            mPeriodList,
                            getString(R.string.string_meditation_class_period)
                        ).show(this@CreateMeditationClassActivity,
                            object : OnClickViewListener {
                                override fun onImgConfirmClick(item: String?) {
                                    mBind.mrvPeriod.setTvText(item.toString())
                                    // 打卡次数（打卡次数 <= 周期次数）
                                    item?.let {
                                        mClockFrequencyList.clear()
                                        (2..it.toInt()).forEach { index ->
                                            mClockFrequencyList.add(index.toString())
                                        }
                                    }
                                }
                            })
                    } else ToastUtils.showShortToast(getString(R.string.string_system_error))
                }
                // 打卡次数
                R.id.mrvClockFrequency -> {
                    if (mTextContent == mBind.mrvPeriod.getTvText()) {
                        ToastUtils.showShortToast(getString(R.string.string_select_cycle))
                        return
                    }
                    if (mClockFrequencyList.isNotEmpty()) {
                        MeditationClassBottomDialog(
                            mClockFrequencyList,
                            getString(R.string.string_meditation_class_clock_frequency)
                        ).show(this@CreateMeditationClassActivity,
                            object : OnClickViewListener {
                                override fun onImgConfirmClick(item: String?) {
                                    mBind.mrvClockFrequency.setTvText(item.toString())
                                }
                            })
                    } else ToastUtils.showShortToast(getString(R.string.string_system_error))
                }
                // 每次时长
                R.id.mrvEveryTime -> {
                    MeditationClassBottomDialog(
                        mEveryTimePeriodList.toList(),
                        getString(R.string.string_meditation_class_every_time)
                    ).show(this@CreateMeditationClassActivity,
                        object : OnClickViewListener {
                            override fun onImgConfirmClick(item: String?) {
                                mBind.mrvEveryTime.setTvText(item.toString())
                            }
                        })
                }
                // 立即创建
                R.id.stvCreate -> {
                    mBind.apply {
                        when {
                            mImgUrl.isNullOrBlank() -> ToastUtils.showShortToast(getString(R.string.string_push_meditation_class_img))
                            etClassName.text.isNullOrBlank() -> ToastUtils.showShortToast(
                                getString(
                                    R.string.string_push_meditation_class_name
                                )
                            )
                            mrvStartTime.getTvText() == mTextContent || mStartDateKeyContent.isBlank() -> ToastUtils.showShortToast(
                                getString(R.string.string_push_meditation_class_start_time)
                            )
                            mrvPeriod.getTvText() == mTextContent -> ToastUtils.showShortToast(
                                getString(R.string.string_push_meditation_class_period)
                            )
                            mrvClockFrequency.getTvText() == mTextContent -> ToastUtils.showShortToast(
                                getString(R.string.string_push_meditation_class_clock_frequency)
                            )
                            mrvPeriod.getTvText().toInt() < mrvClockFrequency.getTvText()
                                .toInt() -> ToastUtils.showShortToast(getString(R.string.string_upper_limit_less_than))
                            mrvEveryTime.getTvText() == mTextContent -> ToastUtils.showShortToast(
                                getString(R.string.string_push_meditation_class_every_time)
                            )
                            mHintCeilingOfPersonnel.isNotBlank() -> ToastUtils.showShortToast(
                                mHintCeilingOfPersonnel
                            )
                            mHintPaymentForASingleEngagement.isNotBlank() -> ToastUtils.showShortToast(
                                mHintPaymentForASingleEngagement
                            )
                            else -> {
                                DialogUtil.showWaitLoading()
                                mViewModel.createMeditationClass(
                                    mutableMapOf(
                                        "title" to etClassName.text.toString(),
                                        "description" to etClassDes.text.toString(),
                                        "headimgurl" to mImgUrl,
                                        "start_date" to mStartDateKeyContent,
                                        "period" to mrvPeriod.getTvText(),
                                        "clock_count" to mrvClockFrequency.getTvText(),
                                        "clock_minute" to mrvEveryTime.getTvText(),
                                        "max_user" to mCeilingOfPersonnelContent,
                                        "per_money" to mPaymentForASingleEngagementContent
                                    )
                                )
                            }
                        }
                    }
                }
                else -> {
                }
            }
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            if (requestCode == ConsRequestCode.SEND_REQUEST_UPLOAD_IMAGE) {
                data?.let {
                    mImgUrl = it.getStringExtra(ConsIntent.BUNDLE_IMAGE_URL_KEY)
                    val localUrl = it.getStringExtra(ConsIntent.BUNDLE_IMAGE_PATH_KEY)
                    localUrl?.let { stringUrl ->
                        ImageLoader.loadImageFile(this, File(stringUrl), mBind.imgHeadPortraits)
                    }
                }
            }
        }
    }

}