package com.imoblife.now.activity.faq

import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.MyApplication
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.FAQTabBean
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiService
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers
import com.imoblife.now.util.AssetsUtil

class FAQRepository : BaseRepository() {
    fun getFAQ(liveData: MutableLiveData<UiStatus<MutableList<FAQTabBean>>>) {
        ApiClient.getInstance().createService(ApiService::class.java)
                .faq
                .compose(RxSchedulers.compose())
                .subscribe(object : BaseObserver<BaseResult<MutableList<FAQTabBean>>>() {
                    override fun onSuccess(response: BaseResult<MutableList<FAQTabBean>>?) {
                        response?.let {
                            liveData.value = UiStatus(true, response.result)
                        }
                    }
                    override fun onFailure(msg: String?) {
                        val faq = AssetsUtil.getStr(MyApplication.getInstance(), "FAQ.json")
                        val faqData = Gson().fromJson<BaseResult<MutableList<FAQTabBean>>>(faq, object : TypeToken<BaseResult<MutableList<FAQTabBean>>>() {}.type)
                        liveData.value = UiStatus(true, faqData.result)
                    }
                })
    }

}