package com.imoblife.now.activity.activities

import androidx.lifecycle.ViewModelProvider
import com.imoblife.now.mvvm.BaseVMFragment
import com.imoblife.now.mvvm.Status
import com.imoblife.now.R
import com.imoblife.now.adapter.CourseActiveAdapter
import com.imoblife.now.bean.FoundCourse
import com.imoblife.now.databinding.LayoutFragmentActiveBinding
import com.imoblife.now.ext.args
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.view.EmptyViewUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-09-14
 * 描   述：活动Fragment
 */
class ActiveFragment : BaseVMFragment<ActiveViewModel>() {

    private var mType: Int by args()

    companion object {
        fun newInstance(type: Int): ActiveFragment = ActiveFragment().apply { mType = type }
    }

    private lateinit var mBind: LayoutFragmentActiveBinding

    private val mActiveAdapter by lazy { CourseActiveAdapter(mutableListOf()) }

    override fun getLayoutResId() = R.layout.layout_fragment_active

    override fun initVM() = ViewModelProvider(this,
        ViewModelProvider.NewInstanceFactory()).get(ActiveViewModel::class.java)

    override fun initView() {
        mBind = mBinding as LayoutFragmentActiveBinding
        mBind.apply {
            recyclerView.adapter = mActiveAdapter
            smartRefreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    mViewModel.getActivityData(mType)
                }
                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    mViewModel.getActivityMoreData(mType)
                }
            })
        }
    }

    override fun onFragmentFirstVisible() {
        super.onFragmentFirstVisible()
        DialogUtil.showWaitLoading()
        mViewModel.getActivityData(mType)
    }

    override fun initData() {}

    override fun startObserve() {
        mViewModel.foundCourse.observe(viewLifecycleOwner) {
            DialogUtil.hideWaitLoading()
            if (it.isSuccess) {
                when (it.status) {
                    Status.REFRESHSUCCESS -> {
                        mActiveAdapter.setNewData(it.successData)
                        mBind.smartRefreshLayout.finishRefresh()
                    }
                    Status.MORESUCCESS -> {
                        it.successData?.let { list: List<FoundCourse> ->
                            mActiveAdapter.addData(mActiveAdapter.data.size, list)
                            mBind.smartRefreshLayout.finishLoadMore()
                        }
                    }
                    Status.NOMOREDATA -> {
                        mBind.smartRefreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    Status.EMPTYDATA -> {
                        showEmptyView()
                        mBind.smartRefreshLayout.finishRefreshWithNoMoreData()
                    }
                    else -> {
                    }
                }
            } else {
                when (it.status) {
                    Status.FAILED -> {
                        showEmptyView()
                        mBind.smartRefreshLayout.finishRefresh(false)
                    }
                    Status.MOREFAIL -> {
                        mBind.smartRefreshLayout.finishLoadMore(false)
                    }
                    else -> {
                    }
                }
            }
        }
    }

    private fun showEmptyView() {
        if (mActiveAdapter.data.size <= 0) {
            mActiveAdapter.emptyView =
                EmptyViewUtils.getEmptyBuyView(requireActivity()) {
                    DialogUtil.showWaitLoading()
                    mViewModel.getActivityData(mType)
                }
        }
    }

}