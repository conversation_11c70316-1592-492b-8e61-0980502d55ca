package com.imoblife.now.activity.agreedmed

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.BaseRepository
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.BaseResult
import com.imoblife.now.bean.MeditationClassJoinEntity
import com.imoblife.now.net.ApiClient
import com.imoblife.now.net.ApiServiceAgreedUponUnTheMeditation
import com.imoblife.now.net.BaseObserver
import com.imoblife.now.net.RxSchedulers

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-10-14
 * 描   述：我的参与_冥想班_Repository
 */
class MyJoinMeditationClassRepository : BaseRepository() {

    private var mPage = 1

    /**
     * 获取我参与的冥想班
     */
    fun getJoinMeditationClass(
        initPage: Boolean = false,
        _mMeditationClassJoinList: MutableLiveData<UiStatus<List<MeditationClassJoinEntity>>>,
    ) {
        if (initPage) mPage = 1
        ApiClient
            .getInstance()
            .createService(ApiServiceAgreedUponUnTheMeditation::class.java)
            .getJoinMeditationClass(mPage)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<MeditationClassJoinEntity>>>() {
                override fun onSuccess(response: BaseResult<List<MeditationClassJoinEntity>>?) {
                    response?.result?.let {
                        if (it.isNotEmpty()) {
                            if (mPage == 1) {
                                _mMeditationClassJoinList.value =
                                    UiStatus(true, it, null, Status.REFRESHSUCCESS)
                            } else {
                                _mMeditationClassJoinList.value =
                                    UiStatus(true, it, null, Status.MORESUCCESS)
                            }
                            mPage++
                        } else {
                            checkStatus(_mMeditationClassJoinList)
                        }
                    } ?: checkStatus(_mMeditationClassJoinList)
                }

                override fun onFailure(msg: String?) {
                    super.onFailure(msg)
                    if (mPage == 1) {
                        _mMeditationClassJoinList.value = UiStatus(false, null, null, Status.FAILED)
                    } else {
                        _mMeditationClassJoinList.value =
                            UiStatus(false, null, null, Status.MOREFAIL)
                    }
                }
            })
    }

    private fun checkStatus(_mMeditationClassJoinList: MutableLiveData<UiStatus<List<MeditationClassJoinEntity>>>) {
        if (mPage == 1) {
            _mMeditationClassJoinList.value = UiStatus(true, null, null, Status.EMPTYDATA)
        } else {
            _mMeditationClassJoinList.value = UiStatus(true, null, null, Status.NOMOREDATA)
        }
    }

}