package com.imoblife.now.activity.activities

import android.annotation.SuppressLint
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.ConcatAdapter
import com.imoblife.commlibrary.base.BaseEvent
import com.imoblife.now.R
import com.imoblife.now.activity.joining.JoinViewModel
import com.imoblife.now.adapter.JoiningAdapter
import com.imoblife.now.adapter.JoiningEmptyAdapter
import com.imoblife.now.adapter.JoiningTitleAdapter
import com.imoblife.now.adapter.decoration.CommonItemDecoration
import com.imoblife.now.bean.FoundCourse
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.constant.ConsEventCode
import com.imoblife.now.databinding.ActivityJoiningBinding
import com.imoblife.now.ext.args
import com.imoblife.now.ext.dp
import com.imoblife.now.mvvm.BaseVMFragment
import com.imoblife.now.util.DialogUtil
import com.imoblife.now.viewmodel.AdViewModel

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2022/8/18
 * 描   述：我的活动 - Fm => 待开始 | 进行中 | 已结束
 */
class MyActiveFragment : BaseVMFragment<JoinViewModel>() {

    companion object {

        fun newInstance(type: Int): MyActiveFragment = MyActiveFragment().apply { mType = type }

    }

    private lateinit var mBind: ActivityJoiningBinding

    private var mType: Int by args()

    private val mAdViewModel: AdViewModel by viewModels()

    private val mMyJoinData by lazy(LazyThreadSafetyMode.NONE) { mutableListOf<FoundCourse>() }
    private val mRecommendData by lazy(LazyThreadSafetyMode.NONE) { mutableListOf<FoundCourse>() }
    private val mJoiningEmptyAdapter by lazy(LazyThreadSafetyMode.NONE) { JoiningEmptyAdapter() }
    private val mJoiningTitleAdapter by lazy(LazyThreadSafetyMode.NONE) {
        JoiningTitleAdapter(
            isHideImg = true
        )
    }
    private val mJoiningAdapter by lazy(LazyThreadSafetyMode.NONE) {
        JoiningAdapter(
            mMyJoinData,
            "Joining"
        )
    }
    private val mRecommendAdapter by lazy(LazyThreadSafetyMode.NONE) {
        JoiningAdapter(
            mRecommendData,
            "Recommend"
        )
    }

    // 我的活动中部横条
    private val mAdMoreAdapter by lazy(LazyThreadSafetyMode.NONE) { MyActiveCenterAdAdapter() }

    private val mConcatAdapter by lazy(LazyThreadSafetyMode.NONE) {
        ConcatAdapter(
            mJoiningEmptyAdapter,
            mJoiningAdapter,
            mJoiningTitleAdapter,
            mAdMoreAdapter,
            mRecommendAdapter
        )
    }

    override fun getLayoutResId() = R.layout.activity_joining

    override fun initVM() = ViewModelProvider(this).get(JoinViewModel::class.java)

    override fun initView() {
        mBind = mBinding as ActivityJoiningBinding
        mBind.apply {
            swipeLayout.setOnRefreshListener { mViewModel.getUserJoining(proceed_type = mType) }
            recyclerView.apply {
                adapter = mConcatAdapter
                addItemDecoration(CommonItemDecoration(0, 13.dp, 13.dp, 13.dp, 13.dp, 13.dp))
            }
        }
    }

    override fun initData() {
        if (mType == 2) {
            mAdViewModel.getCampaignTriggerAds(ConsCommon.POSITION_IN_MY_ACTIVITIES_CENTRAL_HORIZONTAL_BAR)
        }
    }

    override fun onFragmentFirstVisible() {
        super.onFragmentFirstVisible()
        DialogUtil.showWaitLoading()
        mViewModel.getUserJoining(proceed_type = mType)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun startObserve() {
        mViewModel.userJoiningActive.observe(this) { uiStatus ->
            mBind.swipeLayout.finishRefresh()
            DialogUtil.hideWaitLoading()
            if (uiStatus.isSuccess) {
                uiStatus.successData?.apply {
                    mMyJoinData.clear()
                    if (my.isNullOrEmpty()) {
                        mJoiningEmptyAdapter.setNewData(mutableListOf("null"))
                    } else {
                        mJoiningEmptyAdapter.setNewData(mutableListOf<String>())
                        mMyJoinData.addAll(my)
                    }
                    mJoiningAdapter.notifyDataSetChanged()
                    mRecommendData.clear()
                    if (recommend.isNullOrEmpty()) {
                        mJoiningTitleAdapter.setNewData(mutableListOf<String>())
                    } else {
                        mJoiningTitleAdapter.setNewData(mutableListOf(getString(R.string.string_more_active_txt)))
                        mRecommendData.addAll(recommend)
                    }
                    mRecommendAdapter.notifyDataSetChanged()
                }
            }
        }

        // fm - 进行中 - 中部横条
        mAdViewModel.campaignTriggerAds.observe(this) {
            if (it.isSuccess) {
                it.successData?.let { entity ->
                    entity.local_pre_source = "我的活动 - 进行中 - 中部横条"
                    mAdMoreAdapter.setNewData(mutableListOf(entity))
                }
            }
        }
    }

    override fun onEventMainThread(event: BaseEvent?) {
        super.onEventMainThread(event)
        if (event?.eventCode == ConsEventCode.LOGIN_CHANGE_EVENT
            || event?.eventCode == ConsEventCode.CHANGE_SUBSCRIBE_EVENT
        ) {
            DialogUtil.showWaitLoading()
            mViewModel.getUserJoining(proceed_type = mType)
        }
    }

}