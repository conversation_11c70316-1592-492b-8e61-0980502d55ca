package com.imoblife.now.activity.agreedmed

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import cn.sharesdk.sina.weibo.SinaWeibo
import cn.sharesdk.tencent.qq.QQ
import cn.sharesdk.wechat.friends.Wechat
import cn.sharesdk.wechat.moments.WechatMoments
import cn.sharesdk.wework.Wework
import com.imoblife.now.mvvm.BaseVMActivity
import com.imoblife.now.R
import com.imoblife.now.adapter.loading.NavIconType
import com.imoblife.now.adapter.loading.ToolbarUtils
import com.imoblife.now.bean.MeditationClassShareEntity
import com.imoblife.now.bean.MeditationClassShareParamsEntity
import com.imoblife.now.constant.ConsCommon
import com.imoblife.now.databinding.ActivityCreateMeditationSuccessBinding
import com.imoblife.now.share.ShareCenter
import com.imoblife.now.util.ToastUtils

/**
 * 版   权：纳沃科技@版权所有
 * 创建日期：2021/10/13 14:33
 * 创 建 者：TUS
 * 描   述：约定冥想创建成功
 */
class CreateMeditationSuccessActivity : BaseVMActivity<AgreedUponInTheMeditationViewModel>() {

    companion object {
        fun startCreateMeditationSuccess(context: Context, teamId: Int?) {
            Intent(context, CreateMeditationSuccessActivity::class.java).run {
                putExtra(ConsCommon.MEDITATION_CLASS_TEAM_ID, teamId)
                context.startActivity(this)
            }
        }
    }

    private lateinit var mBind: ActivityCreateMeditationSuccessBinding

    private var mTeamId: Int? = 0

    // 生成邀请卡信息
    private var mMeditationClassShare: MeditationClassShareEntity? = null

    // 分享info
    private var mMeditationClassShareParams: MeditationClassShareParamsEntity? = null

    override fun getLayoutResId(): Int {
        return R.layout.activity_create_meditation_success
    }

    override fun superInit(intent: Intent?) {
        if (hasExtra(ConsCommon.MEDITATION_CLASS_TEAM_ID)) {
            mTeamId = intent?.getIntExtra(ConsCommon.MEDITATION_CLASS_TEAM_ID, 0)
        }
    }

    override fun initView() {
        ToolbarUtils.setToolbar(this,
            getString(R.string.string_create_success_title),
            NavIconType.DEFAULT,
            getString(
                R.string.string_completed),
            {
                AgreedUponInTheMeditationActivity.startActivity(this)
                finish()
            },
            true)
        mBind = mBinding as ActivityCreateMeditationSuccessBinding
        mBind.clickProxy = ClickProxy()
    }

    override fun initData() {
        mTeamId?.let {
            mViewModel.getMeditationClassShare(it)
            mViewModel.getMeditationClassShareParams(it)
        }
    }

    override fun initVM(): AgreedUponInTheMeditationViewModel {
        return ViewModelProvider(this).get(AgreedUponInTheMeditationViewModel::class.java)
    }

    override fun startObserve() {
        mViewModel.apply {
            meditationClassShare.observe(this@CreateMeditationSuccessActivity) {
                if (it.isSuccess) {
                    mMeditationClassShare = it.successData
                }
            }
            meditationClassShareParamsEntity.observe(this@CreateMeditationSuccessActivity) {
                if (it.isSuccess) {
                    it.successData?.let { entity -> mMeditationClassShareParams = entity }
                }
            }
        }
    }

    inner class ClickProxy {

        fun onViewClicked(view: View) {
            when (view.id) {
                R.id.share_join_txt -> {
                    mMeditationClassShare?.let {
                        ShareMyAgreedUponInTheMeditationActivity.startActivity(this@CreateMeditationSuccessActivity,
                            it)
                    }
                }
                R.id.share_wechat_txt -> {
                    doShare(Wechat.NAME)
                }
                R.id.share_wechat_moments_txt -> {
                    doShare(WechatMoments.NAME)
                }
                R.id.share_qq_txt -> {
                    doShare(QQ.NAME)
                }
                R.id.share_weibo_txt -> {
                    doShare(SinaWeibo.NAME)
                }
                R.id.share_enterprise_we_chat -> {
                    doShare(Wework.NAME)
                }
            }
        }
    }

    private fun doShare(platform: String) {
        mMeditationClassShareParams?.let {
            ShareCenter().doShareUrl(platform, it.link, it.title, it.desc, it.img_url)
        } ?: let {
            ToastUtils.showShortToast(getString(R.string.default_error))
            mTeamId?.let { mViewModel.getMeditationClassShareParams(it) }
        }
    }

    override fun onBackPressed() {}

}