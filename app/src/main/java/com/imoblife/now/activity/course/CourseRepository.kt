package com.imoblife.now.activity.course

import androidx.lifecycle.MutableLiveData
import com.imoblife.now.mvvm.Status
import com.imoblife.now.mvvm.UiStatus
import com.imoblife.now.bean.*
import com.imoblife.now.model.CourseMgr
import com.imoblife.now.model.TrackMgr
import com.imoblife.now.net.*
import io.reactivex.Observable
import io.reactivex.functions.Function
import java.util.ArrayList

/**
 *    <AUTHOR> Koala
 *    e-mail : <EMAIL>
 *    @date   : 5/14/21 6:55 PM
 *    desc   :
 *    version: 1.0
 */
class CourseRepository {

    fun getHotCourse(liveData: MutableLiveData<UiStatus<List<Course>>>, pageId: Int, categoryId: Int) {
        ApiClient.getInstance().createService(ApiService::class.java)
            .getHotCourse(pageId, categoryId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<Course>>>() {
                override fun onSuccess(response: BaseResult<List<Course>>) {
                    liveData.value = UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }
                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.REFRESHSUCCESS)
                }
            })
    }

    fun getCourseByTag(
        tag: String,
        page: Int,
        size: Int,
        pageId: Int,
        categoryId: Int,
        liveData: MutableLiveData<UiStatus<List<Course>>>
    ) {
        ApiClient.getInstance().createService(ApiService::class.java)
            .getCourseByTag(tag, page, size, pageId, categoryId)
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<Course>>>() {
                override fun onSuccess(response: BaseResult<List<Course>>) {
                    liveData.value = UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }
                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.REFRESHSUCCESS)
                }
            })
    }
    fun getCourseTag(liveData: MutableLiveData<UiStatus<List<CourseTagGroup>>>) {
        ApiClient.getInstance().createService(ApiService::class.java)
            .courseTag
            .compose(RxSchedulers.compose())
            .subscribe(object : BaseObserver<BaseResult<List<CourseTagGroup>>>() {
                override fun onSuccess(response: BaseResult<List<CourseTagGroup>>) {
                    liveData.value = UiStatus(true, response?.result, "", Status.REFRESHSUCCESS)
                }
                override fun onFailure(msg: String?) {
                    liveData.value = UiStatus(false, null, msg, Status.REFRESHSUCCESS)
                }
            })
    }
    fun getCourseDetail(courseId:Int,pageId: Int,categoryId: Int,liveData: MutableLiveData<UiStatus<CourseDetail>>){
        CourseMgr.getInstance()
            .getCourseById(courseId, pageId, categoryId, object : BaseCallBack<CourseDetail?>() {
                override fun onSuc(response: CourseDetail?) {
                    response?.let { liveData.value=UiStatus(true,it) }
                }
                override fun onFail(message: String?) {
                }
            })
    }

    fun getTrackList(
        courseId:Int,
        pageId: Int,
        categoryId: Int,
        liveData: MutableLiveData<UiStatus<List<GroupTrack>>>){
        TrackMgr.getInstance()
            .getNextTrackObservable(courseId, pageId, categoryId)
            .compose(RxSchedulers.compose())
//            .map(TrackMgr.getInstance().saveTracksToDb(courseId))
//            .publish(PublishFunction.getFunction(TrackMgr.getCacheObservable<Any>(courseId)))
//            .map<BaseResult<List<GroupTrack>>>(getNextTrack())
            .subscribe(object : BaseObserver<BaseResult<List<GroupTrack>>>() {
                override fun onSuccess(response: BaseResult<List<GroupTrack>>) {
                    response?.result?.let {
                        liveData.value= UiStatus(true,it)
                    }
                }
            })
    }
    private var isFindNext=false
//    private fun getNextPlayTrack(groupTracks: List<GroupTrack>?) {
//        if (groupTracks == null) {
//            return
//        }
//        isFindNext = false
//        val trackListCN: MutableList<Track> = ArrayList()
//        Observable.fromIterable(groupTracks)
//            .subscribe { groupTrack: GroupTrack ->
//                val trackList =
//                    groupTrack.group_item
//                Observable.fromIterable(trackList)
//                    .subscribe { track: Track ->
//                        if (track.isAudio || track.isVideo) {
//                            if (track.isTrackCn) {
//                                trackListCN.add(track)
//                                if (mView != null) {
//                                    mView.showPlayList(trackListCN)
//                                }
//                                if (!isFindNext) {
//                                    if (!track.isIs_listen && !track.isPreview) {
//                                        isFindNext = true
//                                        mView.showNextTracksData(track)
//                                    } else {
//                                        mView.showNextTracksData(null)
//                                    }
//                                }
//                            }
//                        }
//                    }
//            }
//    }

    private fun getNextTrack(): Function<BaseResult<List<GroupTrack?>?>?, BaseResult<List<GroupTrack?>?>> {
        return label@ Function { listBaseResult: BaseResult<List<GroupTrack?>?>? ->
//            if (listBaseResult == null || listBaseResult.result == null) {
//                return@label listBaseResult
//            }
//            getNextPlayTrack(listBaseResult?.getResult())
            listBaseResult
        }
    }
}